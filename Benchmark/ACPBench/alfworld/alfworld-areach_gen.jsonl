{"id": 9148073064266127852, "group": "reachable_action_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. toaster1 is at location9. coffeemachine1 and countertop2 are at location11. stoveburner1 and stoveburner3 are at location5. shelf2 is at location29. countertop3 is at location3. countertop1 is at location2. garbagecan1 is at location8. drawer3 is at location27. fridge1 is at location10. shelf3 is at location26. shelf1 is at location17. stoveburner2 and stoveburner4 are at location21. cabinet5 is at location13. microwave1 is at location24. drawer1 is at location20. cabinet1 is at location19. cabinet4 is at location14. cabinet2 is at location22. cabinet3 is at location16. drawer2 is at location15. cabinet6 is at location4. sinkbasin1 is at location6. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. egg2, potato1, bowl1, tomato1, plate2, egg1, and mug1 are at location10. plate3, butterknife1, creditcard1, bread1, knife2, spoon2, houseplant1, spatula1, cellphone3, papertowelroll1, and glassbottle1 are at location3. lettuce2, cellphone2, lettuce1, apple1, cellphone1, plate1, peppershaker1, and knife1 are at location2. apple2, potato3, and glassbottle3 are at location8. vase2 is at location17. pot1 is at location5. stoveknob1 is at location18. stoveknob4 is at location7. dishsponge1 and fork1 are at location27. peppershaker3 is at location16. stoveknob3 and stoveknob2 are at location12. bowl2 is at location29. cup1 is at location24. window2 is at location28. soapbottle2 and statue1 are at location26. spatula2, glassbottle2, spatula3, sink1, and potato2 are at location6. lightswitch1 is at location25. chair1 is at location23. chair2 is at location1. peppershaker2 is at location20. saltshaker1 is at location15. pan1 and spoon1 are at location11. soapbottle1 is at location4. vase1 and dishsponge2 are at location13. window1 is at location30. agent agent1 is at location location5. The objects are in/on receptacle as follows. cellphone2 is on plate1. spatula3, glassbottle2, spatula2, and potato2 are in sinkbasin1. plate3, cellphone3, creditcard1, butterknife1, glassbottle1, bread1, houseplant1, spatula1, spoon2, papertowelroll1, and knife2 are on countertop3. apple2, glassbottle3, and potato3 are in garbagecan1. saltshaker1 is in drawer2. pan1 and spoon1 are on countertop2. peppershaker1, cellphone1, apple1, lettuce2, knife1, lettuce1, cellphone2, and plate1 are on countertop1. vase1 and dishsponge2 are in cabinet5. peppershaker3 is in cabinet3. fork1 and dishsponge1 are in drawer3. pan1 is on stoveburner2. soapbottle2 and statue1 are on shelf3. pan1 is on stoveburner4. tomato1, egg2, bowl1, egg1, plate2, mug1, and potato1 are in fridge1. pot1 is on stoveburner3. pot1 is on stoveburner1. vase2 is on shelf1. soapbottle1 is in cabinet6. cup1 is in microwave1. peppershaker2 is in drawer1. bowl2 is on shelf2. cabinet5, drawer2, cabinet1, fridge1, drawer1, drawer3, and microwave1 are closed. mug1 is cool. cabinet2 is checked. cabinet2 is open. Nothing has been validated. agent1 is holding object mug2. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a moves from the current position ?lstart to the next position ?lend that has the receptacle ?r, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r that is at location ?l, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r while at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from a not openable receptacle ?r that is at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a grasps object ?o from an openable receptacle ?r while at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot on a not openable receptacle ?r with type ?rt at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o of type ?ot in an openable receptacle ?r of type ?rt while at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans a cleanable object ?o in a sink ?r at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a heats up object ?o with a microwave ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a chills a coolable object ?o in receptacle ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a toggles object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co that is at location ?l with a knife ?ko, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - check that the togglable object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is warmed up and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is cool and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - ensure that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(toggle_object_on agent1 location17 location16 countertop1)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190907_171933_349922)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location20 location21 location22 location23 location24 location25 location26 location27 location28 location29 location3 location30 location31 location4 location5 location6 location7 location8 location9 - location alarmclock apple apple1 apple2 baseballbat basketball bathtub blinds book boots bowl bowl1 bowl2 box bread bread1 butterknife butterknife1 candle cd cellphone cellphone1 cellphone2 cellphone3 chair chair1 chair2 cloth creditcard creditcard1 cup cup1 curtains desklamp dishsponge dishsponge1 dishsponge2 egg egg1 egg2 faucet1 floorlamp footstool fork fork1 glassbottle glassbottle1 glassbottle2 glassbottle3 handtowel houseplant houseplant1 kettle keychain knife knife1 knife2 ladle laptop laundryhamperlid lettuce lettuce1 lettuce2 lightswitch lightswitch1 mirror mug mug1 mug2 newspaper painting pan pan1 papertowel papertowelroll papertowelroll1 pen pencil peppershaker peppershaker1 peppershaker2 peppershaker3 pillow plate plate2 plate3 plunger poster pot pot1 potato potato1 potato2 potato3 remotecontrol saltshaker saltshaker1 scrubbrush showerdoor showerglass sink sink1 soapbar soapbottle soapbottle1 soapbottle2 spatula spatula1 spatula2 spatula3 spoon spoon1 spoon2 spraybottle statue statue1 stoveknob stoveknob1 stoveknob2 stoveknob3 stoveknob4 teddybear television tennisracket tissuebox toiletpaper toiletpaperroll tomato tomato1 towel vase vase1 vase2 watch wateringcan window window1 window2 winebottle - object plate1 - objectorreceptacle alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 cabinet5 cabinet6 coffeemachine1 countertop1 countertop2 countertop3 drawer1 drawer2 drawer3 fridge1 garbagecan1 microwave1 shelf1 shelf2 shelf3 sinkbasin1 stoveburner1 stoveburner2 stoveburner3 stoveburner4 toaster1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location5) (cancontain cabinettype bowltype) (cancontain cabinettype cuptype) (cancontain cabinettype dishspongetype) (cancontain cabinettype glassbottletype) (cancontain cabinettype mugtype) (cancontain cabinettype pantype) (cancontain cabinettype peppershakertype) (cancontain cabinettype platetype) (cancontain cabinettype pottype) (cancontain cabinettype saltshakertype) (cancontain cabinettype soapbottletype) (cancontain cabinettype vasetype) (cancontain coffeemachinetype mugtype) (cancontain countertoptype appletype) (cancontain countertoptype bowltype) (cancontain countertoptype breadtype) (cancontain countertoptype butterknifetype) (cancontain countertoptype cellphonetype) (cancontain countertoptype creditcardtype) (cancontain countertoptype cuptype) (cancontain countertoptype dishspongetype) (cancontain countertoptype eggtype) (cancontain countertoptype forktype) (cancontain countertoptype glassbottletype) (cancontain countertoptype knifetype) (cancontain countertoptype lettucetype) (cancontain countertoptype mugtype) (cancontain countertoptype pantype) (cancontain countertoptype peppershakertype) (cancontain countertoptype platetype) (cancontain countertoptype potatotype) (cancontain countertoptype pottype) (cancontain countertoptype saltshakertype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spatulatype) (cancontain countertoptype spoontype) (cancontain countertoptype statuetype) (cancontain countertoptype tomatotype) (cancontain countertoptype vasetype) (cancontain drawertype butterknifetype) (cancontain drawertype cellphonetype) (cancontain drawertype creditcardtype) (cancontain drawertype dishspongetype) (cancontain drawertype forktype) (cancontain drawertype knifetype) (cancontain drawertype peppershakertype) (cancontain drawertype saltshakertype) (cancontain drawertype soapbottletype) (cancontain drawertype spatulatype) (cancontain drawertype spoontype) (cancontain fridgetype appletype) (cancontain fridgetype bowltype) (cancontain fridgetype breadtype) (cancontain fridgetype cuptype) (cancontain fridgetype eggtype) (cancontain fridgetype glassbottletype) (cancontain fridgetype lettucetype) (cancontain fridgetype mugtype) (cancontain fridgetype pantype) (cancontain fridgetype platetype) (cancontain fridgetype potatotype) (cancontain fridgetype pottype) (cancontain fridgetype tomatotype) (cancontain garbagecantype appletype) (cancontain garbagecantype breadtype) (cancontain garbagecantype dishspongetype) (cancontain garbagecantype eggtype) (cancontain garbagecantype lettucetype) (cancontain garbagecantype potatotype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype tomatotype) (cancontain microwavetype appletype) (cancontain microwavetype bowltype) (cancontain microwavetype breadtype) (cancontain microwavetype cuptype) (cancontain microwavetype eggtype) (cancontain microwavetype glassbottletype) (cancontain microwavetype mugtype) (cancontain microwavetype platetype) (cancontain microwavetype potatotype) (cancontain microwavetype tomatotype) (cancontain shelftype bowltype) (cancontain shelftype cellphonetype) (cancontain shelftype creditcardtype) (cancontain shelftype cuptype) (cancontain shelftype dishspongetype) (cancontain shelftype glassbottletype) (cancontain shelftype mugtype) (cancontain shelftype peppershakertype) (cancontain shelftype platetype) (cancontain shelftype pottype) (cancontain shelftype saltshakertype) (cancontain shelftype soapbottletype) (cancontain shelftype statuetype) (cancontain shelftype vasetype) (cancontain sinkbasintype appletype) (cancontain sinkbasintype bowltype) (cancontain sinkbasintype butterknifetype) (cancontain sinkbasintype cuptype) (cancontain sinkbasintype dishspongetype) (cancontain sinkbasintype eggtype) (cancontain sinkbasintype forktype) (cancontain sinkbasintype glassbottletype) (cancontain sinkbasintype knifetype) (cancontain sinkbasintype lettucetype) (cancontain sinkbasintype mugtype) (cancontain sinkbasintype pantype) (cancontain sinkbasintype platetype) (cancontain sinkbasintype potatotype) (cancontain sinkbasintype pottype) (cancontain sinkbasintype spatulatype) (cancontain sinkbasintype spoontype) (cancontain sinkbasintype tomatotype) (cancontain stoveburnertype pantype) (cancontain stoveburnertype pottype) (checked cabinet2) (cleanable apple1) (cleanable apple2) (cleanable bowl1) (cleanable bowl2) (cleanable butterknife1) (cleanable cup1) (cleanable dishsponge1) (cleanable dishsponge2) (cleanable egg1) (cleanable egg2) (cleanable fork1) (cleanable knife1) (cleanable knife2) (cleanable lettuce1) (cleanable lettuce2) (cleanable mug1) (cleanable mug2) (cleanable pan1) (cleanable plate1) (cleanable plate2) (cleanable plate3) (cleanable pot1) (cleanable potato1) (cleanable potato2) (cleanable potato3) (cleanable spatula1) (cleanable spatula2) (cleanable spatula3) (cleanable spoon1) (cleanable spoon2) (cleanable tomato1) (closed cabinet1) (closed cabinet5) (closed drawer1) (closed drawer2) (closed drawer3) (closed fridge1) (closed microwave1) (coolable apple1) (coolable apple2) (coolable bowl1) (coolable bowl2) (coolable bread1) (coolable cup1) (coolable egg1) (coolable egg2) (coolable lettuce1) (coolable lettuce2) (coolable mug1) (coolable mug2) (coolable pan1) (coolable plate1) (coolable plate2) (coolable plate3) (coolable pot1) (coolable potato1) (coolable potato2) (coolable potato3) (coolable tomato1) (heatable apple1) (heatable apple2) (heatable bread1) (heatable cup1) (heatable egg1) (heatable egg2) (heatable mug1) (heatable mug2) (heatable plate1) (heatable plate2) (heatable plate3) (heatable potato1) (heatable potato2) (heatable potato3) (heatable tomato1) (holds agent1 mug2) (inreceptacle apple1 countertop1) (inreceptacle apple2 garbagecan1) (inreceptacle bowl1 fridge1) (inreceptacle bowl2 shelf2) (inreceptacle bread1 countertop3) (inreceptacle butterknife1 countertop3) (inreceptacle cellphone1 countertop1) (inreceptacle cellphone2 countertop1) (inreceptacle cellphone2 plate1) (inreceptacle cellphone3 countertop3) (inreceptacle creditcard1 countertop3) (inreceptacle cup1 microwave1) (inreceptacle dishsponge1 drawer3) (inreceptacle dishsponge2 cabinet5) (inreceptacle egg1 fridge1) (inreceptacle egg2 fridge1) (inreceptacle fork1 drawer3) (inreceptacle glassbottle1 countertop3) (inreceptacle glassbottle2 sinkbasin1) (inreceptacle glassbottle3 garbagecan1) (inreceptacle houseplant1 countertop3) (inreceptacle knife1 countertop1) (inreceptacle knife2 countertop3) (inreceptacle lettuce1 countertop1) (inreceptacle lettuce2 countertop1) (inreceptacle mug1 fridge1) (inreceptacle pan1 countertop2) (inreceptacle pan1 stoveburner2) (inreceptacle pan1 stoveburner4) (inreceptacle papertowelroll1 countertop3) (inreceptacle peppershaker1 countertop1) (inreceptacle peppershaker2 drawer1) (inreceptacle peppershaker3 cabinet3) (inreceptacle plate1 countertop1) (inreceptacle plate2 fridge1) (inreceptacle plate3 countertop3) (inreceptacle pot1 stoveburner1) (inreceptacle pot1 stoveburner3) (inreceptacle potato1 fridge1) (inreceptacle potato2 sinkbasin1) (inreceptacle potato3 garbagecan1) (inreceptacle saltshaker1 drawer2) (inreceptacle soapbottle1 cabinet6) (inreceptacle soapbottle2 shelf3) (inreceptacle spatula1 countertop3) (inreceptacle spatula2 sinkbasin1) (inreceptacle spatula3 sinkbasin1) (inreceptacle spoon1 countertop2) (inreceptacle spoon2 countertop3) (inreceptacle statue1 shelf3) (inreceptacle tomato1 fridge1) (inreceptacle vase1 cabinet5) (inreceptacle vase2 shelf1) (iscool mug1) (notopenable cabinet3) (notopenable cabinet4) (notopenable cabinet6) (notopenable coffeemachine1) (notopenable countertop1) (notopenable countertop2) (notopenable countertop3) (notopenable garbagecan1) (notopenable shelf1) (notopenable shelf2) (notopenable shelf3) (notopenable sinkbasin1) (notopenable stoveburner1) (notopenable stoveburner2) (notopenable stoveburner3) (notopenable stoveburner4) (notopenable toaster1) (notvalidated) (objectatlocation apple1 location2) (objectatlocation apple2 location8) (objectatlocation bowl1 location10) (objectatlocation bowl2 location29) (objectatlocation bread1 location3) (objectatlocation butterknife1 location3) (objectatlocation cellphone1 location2) (objectatlocation cellphone2 location2) (objectatlocation cellphone3 location3) (objectatlocation chair1 location23) (objectatlocation chair2 location1) (objectatlocation creditcard1 location3) (objectatlocation cup1 location24) (objectatlocation dishsponge1 location27) (objectatlocation dishsponge2 location13) (objectatlocation egg1 location10) (objectatlocation egg2 location10) (objectatlocation fork1 location27) (objectatlocation glassbottle1 location3) (objectatlocation glassbottle2 location6) (objectatlocation glassbottle3 location8) (objectatlocation houseplant1 location3) (objectatlocation knife1 location2) (objectatlocation knife2 location3) (objectatlocation lettuce1 location2) (objectatlocation lettuce2 location2) (objectatlocation lightswitch1 location25) (objectatlocation mug1 location10) (objectatlocation pan1 location11) (objectatlocation papertowelroll1 location3) (objectatlocation peppershaker1 location2) (objectatlocation peppershaker2 location20) (objectatlocation peppershaker3 location16) (objectatlocation plate1 location2) (objectatlocation plate2 location10) (objectatlocation plate3 location3) (objectatlocation pot1 location5) (objectatlocation potato1 location10) (objectatlocation potato2 location6) (objectatlocation potato3 location8) (objectatlocation saltshaker1 location15) (objectatlocation sink1 location6) (objectatlocation soapbottle1 location4) (objectatlocation soapbottle2 location26) (objectatlocation spatula1 location3) (objectatlocation spatula2 location6) (objectatlocation spatula3 location6) (objectatlocation spoon1 location11) (objectatlocation spoon2 location3) (objectatlocation statue1 location26) (objectatlocation stoveknob1 location18) (objectatlocation stoveknob2 location12) (objectatlocation stoveknob3 location12) (objectatlocation stoveknob4 location7) (objectatlocation tomato1 location10) (objectatlocation vase1 location13) (objectatlocation vase2 location17) (objectatlocation window1 location30) (objectatlocation window2 location28) (objecttype apple1 appletype) (objecttype apple2 appletype) (objecttype bowl1 bowltype) (objecttype bowl2 bowltype) (objecttype bread1 breadtype) (objecttype butterknife1 butterknifetype) (objecttype cellphone1 cellphonetype) (objecttype cellphone2 cellphonetype) (objecttype cellphone3 cellphonetype) (objecttype chair1 chairtype) (objecttype chair2 chairtype) (objecttype creditcard1 creditcardtype) (objecttype cup1 cuptype) (objecttype dishsponge1 dishspongetype) (objecttype dishsponge2 dishspongetype) (objecttype egg1 eggtype) (objecttype egg2 eggtype) (objecttype fork1 forktype) (objecttype glassbottle1 glassbottletype) (objecttype glassbottle2 glassbottletype) (objecttype glassbottle3 glassbottletype) (objecttype houseplant1 houseplanttype) (objecttype knife1 knifetype) (objecttype knife2 knifetype) (objecttype lettuce1 lettucetype) (objecttype lettuce2 lettucetype) (objecttype lightswitch1 lightswitchtype) (objecttype mug1 mugtype) (objecttype mug2 mugtype) (objecttype pan1 pantype) (objecttype papertowelroll1 papertowelrolltype) (objecttype peppershaker1 peppershakertype) (objecttype peppershaker2 peppershakertype) (objecttype peppershaker3 peppershakertype) (objecttype plate1 platetype) (objecttype plate2 platetype) (objecttype plate3 platetype) (objecttype pot1 pottype) (objecttype potato1 potatotype) (objecttype potato2 potatotype) (objecttype potato3 potatotype) (objecttype saltshaker1 saltshakertype) (objecttype sink1 sinktype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype spatula1 spatulatype) (objecttype spatula2 spatulatype) (objecttype spatula3 spatulatype) (objecttype spoon1 spoontype) (objecttype spoon2 spoontype) (objecttype statue1 statuetype) (objecttype stoveknob1 stoveknobtype) (objecttype stoveknob2 stoveknobtype) (objecttype stoveknob3 stoveknobtype) (objecttype stoveknob4 stoveknobtype) (objecttype tomato1 tomatotype) (objecttype vase1 vasetype) (objecttype vase2 vasetype) (objecttype window1 windowtype) (objecttype window2 windowtype) (openable cabinet1) (openable cabinet2) (openable cabinet5) (openable drawer1) (openable drawer2) (openable drawer3) (openable fridge1) (openable microwave1) (opened cabinet2) (pickupable apple1) (pickupable apple2) (pickupable bowl1) (pickupable bowl2) (pickupable bread1) (pickupable butterknife1) (pickupable cellphone1) (pickupable cellphone2) (pickupable cellphone3) (pickupable creditcard1) (pickupable cup1) (pickupable dishsponge1) (pickupable dishsponge2) (pickupable egg1) (pickupable egg2) (pickupable fork1) (pickupable glassbottle1) (pickupable glassbottle2) (pickupable glassbottle3) (pickupable knife1) (pickupable knife2) (pickupable lettuce1) (pickupable lettuce2) (pickupable mug1) (pickupable mug2) (pickupable pan1) (pickupable papertowelroll1) (pickupable peppershaker1) (pickupable peppershaker2) (pickupable peppershaker3) (pickupable plate1) (pickupable plate2) (pickupable plate3) (pickupable pot1) (pickupable potato1) (pickupable potato2) (pickupable potato3) (pickupable saltshaker1) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable spatula1) (pickupable spatula2) (pickupable spatula3) (pickupable spoon1) (pickupable spoon2) (pickupable statue1) (pickupable tomato1) (pickupable vase1) (pickupable vase2) (receptacleatlocation cabinet1 location19) (receptacleatlocation cabinet2 location22) (receptacleatlocation cabinet3 location16) (receptacleatlocation cabinet4 location14) (receptacleatlocation cabinet5 location13) (receptacleatlocation cabinet6 location4) (receptacleatlocation coffeemachine1 location11) (receptacleatlocation countertop1 location2) (receptacleatlocation countertop2 location11) (receptacleatlocation countertop3 location3) (receptacleatlocation drawer1 location20) (receptacleatlocation drawer2 location15) (receptacleatlocation drawer3 location27) (receptacleatlocation fridge1 location10) (receptacleatlocation garbagecan1 location8) (receptacleatlocation microwave1 location24) (receptacleatlocation shelf1 location17) (receptacleatlocation shelf2 location29) (receptacleatlocation shelf3 location26) (receptacleatlocation sinkbasin1 location6) (receptacleatlocation stoveburner1 location5) (receptacleatlocation stoveburner2 location21) (receptacleatlocation stoveburner3 location5) (receptacleatlocation stoveburner4 location21) (receptacleatlocation toaster1 location9) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype cabinet5 cabinettype) (receptacletype cabinet6 cabinettype) (receptacletype coffeemachine1 coffeemachinetype) (receptacletype countertop1 countertoptype) (receptacletype countertop2 countertoptype) (receptacletype countertop3 countertoptype) (receptacletype drawer1 drawertype) (receptacletype drawer2 drawertype) (receptacletype drawer3 drawertype) (receptacletype fridge1 fridgetype) (receptacletype garbagecan1 garbagecantype) (receptacletype microwave1 microwavetype) (receptacletype shelf1 shelftype) (receptacletype shelf2 shelftype) (receptacletype shelf3 shelftype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype stoveburner1 stoveburnertype) (receptacletype stoveburner2 stoveburnertype) (receptacletype stoveburner3 stoveburnertype) (receptacletype stoveburner4 stoveburnertype) (receptacletype toaster1 toastertype) (sliceable apple1) (sliceable apple2) (sliceable bread1) (sliceable egg1) (sliceable egg2) (sliceable lettuce1) (sliceable lettuce2) (sliceable potato1) (sliceable potato2) (sliceable potato3) (sliceable tomato1))\n    (:goal (validateheatandplace mugtype coffeemachinetype))\n)"}
{"id": -3396427425252615433, "group": "reachable_action_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. fridge1 is at location11. toaster1 is at location10. drawer3 is at location28. countertop1 is at location3. stoveburner2 and stoveburner4 are at location22. countertop3 is at location4. stoveburner3 and stoveburner1 are at location6. cabinet2 is at location23. cabinet1 is at location20. cabinet4 is at location15. cabinet5 is at location14. coffeemachine1 and countertop2 are at location12. microwave1 is at location25. sinkbasin1 is at location7. cabinet6 is at location5. cabinet3 is at location17. drawer1 is at location21. shelf3 is at location27. garbagecan1 is at location9. drawer2 is at location16. shelf1 is at location18. shelf2 is at location30. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. papertowelroll1, soapbottle2, and glassbottle1 are at location9. mug2, creditcard2, glassbottle2, and bowl1 are at location30. creditcard1, apple3, apple2, spatula2, bread1, cellphone1, apple1, dishsponge3, and statue1 are at location3. fork2, statue2, cellphone3, butterknife1, peppershaker2, butterknife2, soapbottle3, spatula3, houseplant1, knife1, and spoon2 are at location4. stoveknob3 and stoveknob2 are at location13. vase2 is at location18. egg2, tomato3, cup3, fork1, potato3, and sink1 are at location7. creditcard3 and saltshaker1 are at location27. spoon1 and dishsponge1 are at location16. tomato1, cup1, cup2, egg1, tomato2, lettuce1, potato2, and potato1 are at location11. pan1 is at location6. peppershaker1 is at location20. plate2 and plate1 are at location14. stoveknob1 is at location19. chair1 is at location24. chair2 is at location1. pot1 is at location22. dishsponge2 is at location2. soapbottle1 and vase1 are at location5. lightswitch1 is at location26. window2 is at location29. spatula1 is at location28. stoveknob4 is at location8. cellphone2 is at location21. window1 is at location31. mug1 is at location25. agent agent1 is at location location4. The objects are in/on receptacle as follows. bread1, cellphone1, apple1, creditcard1, apple2, apple3, spatula2, dishsponge3, and statue1 are on countertop1. cellphone3, peppershaker2, statue2, butterknife1, fork2, houseplant1, spoon2, knife1, butterknife2, soapbottle3, and spatula3 are on countertop3. dishsponge1 and spoon1 are in drawer2. plate2, dishsponge2, and plate1 are in cabinet5. dishsponge2 is on plate1. tomato3, potato3, egg2, cup3, and fork1 are in sinkbasin1. pan1 is on stoveburner1. cellphone2 is in drawer1. lettuce1, potato2, tomato2, tomato1, cup1, egg1, potato1, and cup2 are in fridge1. mug1 is in microwave1. glassbottle2, creditcard2, bowl1, and mug2 are on shelf2. pot1 is on stoveburner2. vase1 and soapbottle1 are in cabinet6. glassbottle1, papertowelroll1, and soapbottle2 are in garbagecan1. saltshaker1 and creditcard3 are on shelf3. vase2 is on shelf1. spatula1 is in drawer3. peppershaker1 is in cabinet1. pot1 is on stoveburner4. pan1 is on stoveburner3. cabinet5, drawer2, cabinet1, fridge1, drawer1, drawer3, cabinet2, and microwave1 are closed. Nothing has been validated. agent1's hands are empty. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a moves from the current position ?lstart to the next position ?lend that has the receptacle ?r, (open_receptacle ?a ?l ?r) - agent ?a that is at location ?l opens receptacle ?r, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r while at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a grasps object ?o from a not openable receptacle ?r while at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from an openable receptacle ?r that is at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o with type ?ot on a not openable receptacle ?r with type ?rt at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o of type ?ot in an openable receptacle ?r of type ?rt while at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans object ?o in a receptacle ?r that is at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a warms up a heatable object ?o with a receptacle ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a chills object ?o in a fridge ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co that is at location ?l with a knife ?ko, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - check that the togglable object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is hot and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is cool and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - validate that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(pickup_object_from_not_openable_receptacle agent1 location17 stoveburner3 shelf2)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190906_191445_723170)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location20 location21 location22 location23 location24 location25 location26 location27 location28 location29 location3 location30 location31 location32 location4 location5 location6 location7 location8 location9 - location alarmclock apple apple1 apple2 apple3 baseballbat basketball bathtub blinds book boots bowl bowl1 box bread bread1 butterknife butterknife1 butterknife2 candle cd cellphone cellphone1 cellphone2 cellphone3 chair chair1 chair2 cloth creditcard creditcard1 creditcard2 creditcard3 cup cup1 cup2 cup3 curtains desklamp dishsponge dishsponge1 dishsponge2 dishsponge3 egg egg1 egg2 faucet1 floorlamp footstool fork fork1 fork2 glassbottle glassbottle1 glassbottle2 handtowel houseplant houseplant1 kettle keychain knife knife1 ladle laptop laundryhamperlid lettuce lettuce1 lightswitch lightswitch1 mirror mug mug1 mug2 newspaper painting pan pan1 papertowel papertowelroll papertowelroll1 pen pencil peppershaker peppershaker1 peppershaker2 pillow plate plate2 plunger poster pot pot1 potato potato1 potato2 potato3 remotecontrol saltshaker saltshaker1 scrubbrush showerdoor showerglass sink sink1 soapbar soapbottle soapbottle1 soapbottle2 soapbottle3 spatula spatula1 spatula2 spatula3 spoon spoon1 spoon2 spraybottle statue statue1 statue2 stoveknob stoveknob1 stoveknob2 stoveknob3 stoveknob4 teddybear television tennisracket tissuebox toiletpaper toiletpaperroll tomato tomato1 tomato2 tomato3 towel vase vase1 vase2 watch wateringcan window window1 window2 winebottle - object plate1 - objectorreceptacle alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 cabinet5 cabinet6 coffeemachine1 countertop1 countertop2 countertop3 drawer1 drawer2 drawer3 fridge1 garbagecan1 microwave1 shelf1 shelf2 shelf3 sinkbasin1 stoveburner1 stoveburner2 stoveburner3 stoveburner4 toaster1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location4) (cancontain cabinettype bowltype) (cancontain cabinettype cuptype) (cancontain cabinettype dishspongetype) (cancontain cabinettype glassbottletype) (cancontain cabinettype mugtype) (cancontain cabinettype pantype) (cancontain cabinettype peppershakertype) (cancontain cabinettype platetype) (cancontain cabinettype pottype) (cancontain cabinettype saltshakertype) (cancontain cabinettype soapbottletype) (cancontain cabinettype vasetype) (cancontain coffeemachinetype mugtype) (cancontain countertoptype appletype) (cancontain countertoptype bowltype) (cancontain countertoptype breadtype) (cancontain countertoptype butterknifetype) (cancontain countertoptype cellphonetype) (cancontain countertoptype creditcardtype) (cancontain countertoptype cuptype) (cancontain countertoptype dishspongetype) (cancontain countertoptype eggtype) (cancontain countertoptype forktype) (cancontain countertoptype glassbottletype) (cancontain countertoptype knifetype) (cancontain countertoptype lettucetype) (cancontain countertoptype mugtype) (cancontain countertoptype pantype) (cancontain countertoptype peppershakertype) (cancontain countertoptype platetype) (cancontain countertoptype potatotype) (cancontain countertoptype pottype) (cancontain countertoptype saltshakertype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spatulatype) (cancontain countertoptype spoontype) (cancontain countertoptype statuetype) (cancontain countertoptype tomatotype) (cancontain countertoptype vasetype) (cancontain drawertype butterknifetype) (cancontain drawertype cellphonetype) (cancontain drawertype creditcardtype) (cancontain drawertype dishspongetype) (cancontain drawertype forktype) (cancontain drawertype knifetype) (cancontain drawertype peppershakertype) (cancontain drawertype saltshakertype) (cancontain drawertype soapbottletype) (cancontain drawertype spatulatype) (cancontain drawertype spoontype) (cancontain fridgetype appletype) (cancontain fridgetype bowltype) (cancontain fridgetype breadtype) (cancontain fridgetype cuptype) (cancontain fridgetype eggtype) (cancontain fridgetype glassbottletype) (cancontain fridgetype lettucetype) (cancontain fridgetype mugtype) (cancontain fridgetype pantype) (cancontain fridgetype platetype) (cancontain fridgetype potatotype) (cancontain fridgetype pottype) (cancontain fridgetype tomatotype) (cancontain garbagecantype appletype) (cancontain garbagecantype breadtype) (cancontain garbagecantype dishspongetype) (cancontain garbagecantype eggtype) (cancontain garbagecantype lettucetype) (cancontain garbagecantype potatotype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype tomatotype) (cancontain microwavetype appletype) (cancontain microwavetype bowltype) (cancontain microwavetype breadtype) (cancontain microwavetype cuptype) (cancontain microwavetype eggtype) (cancontain microwavetype glassbottletype) (cancontain microwavetype mugtype) (cancontain microwavetype platetype) (cancontain microwavetype potatotype) (cancontain microwavetype tomatotype) (cancontain shelftype bowltype) (cancontain shelftype cellphonetype) (cancontain shelftype creditcardtype) (cancontain shelftype cuptype) (cancontain shelftype dishspongetype) (cancontain shelftype glassbottletype) (cancontain shelftype mugtype) (cancontain shelftype peppershakertype) (cancontain shelftype platetype) (cancontain shelftype pottype) (cancontain shelftype saltshakertype) (cancontain shelftype soapbottletype) (cancontain shelftype statuetype) (cancontain shelftype vasetype) (cancontain sinkbasintype appletype) (cancontain sinkbasintype bowltype) (cancontain sinkbasintype butterknifetype) (cancontain sinkbasintype cuptype) (cancontain sinkbasintype dishspongetype) (cancontain sinkbasintype eggtype) (cancontain sinkbasintype forktype) (cancontain sinkbasintype glassbottletype) (cancontain sinkbasintype knifetype) (cancontain sinkbasintype lettucetype) (cancontain sinkbasintype mugtype) (cancontain sinkbasintype pantype) (cancontain sinkbasintype platetype) (cancontain sinkbasintype potatotype) (cancontain sinkbasintype pottype) (cancontain sinkbasintype spatulatype) (cancontain sinkbasintype spoontype) (cancontain sinkbasintype tomatotype) (cancontain stoveburnertype pantype) (cancontain stoveburnertype pottype) (cleanable apple1) (cleanable apple2) (cleanable apple3) (cleanable bowl1) (cleanable butterknife1) (cleanable butterknife2) (cleanable cup1) (cleanable cup2) (cleanable cup3) (cleanable dishsponge1) (cleanable dishsponge2) (cleanable dishsponge3) (cleanable egg1) (cleanable egg2) (cleanable fork1) (cleanable fork2) (cleanable knife1) (cleanable lettuce1) (cleanable mug1) (cleanable mug2) (cleanable pan1) (cleanable plate1) (cleanable plate2) (cleanable pot1) (cleanable potato1) (cleanable potato2) (cleanable potato3) (cleanable spatula1) (cleanable spatula2) (cleanable spatula3) (cleanable spoon1) (cleanable spoon2) (cleanable tomato1) (cleanable tomato2) (cleanable tomato3) (closed cabinet1) (closed cabinet2) (closed cabinet5) (closed drawer1) (closed drawer2) (closed drawer3) (closed fridge1) (closed microwave1) (coolable apple1) (coolable apple2) (coolable apple3) (coolable bowl1) (coolable bread1) (coolable cup1) (coolable cup2) (coolable cup3) (coolable egg1) (coolable egg2) (coolable lettuce1) (coolable mug1) (coolable mug2) (coolable pan1) (coolable plate1) (coolable plate2) (coolable pot1) (coolable potato1) (coolable potato2) (coolable potato3) (coolable tomato1) (coolable tomato2) (coolable tomato3) (handempty agent1) (heatable apple1) (heatable apple2) (heatable apple3) (heatable bread1) (heatable cup1) (heatable cup2) (heatable cup3) (heatable egg1) (heatable egg2) (heatable mug1) (heatable mug2) (heatable plate1) (heatable plate2) (heatable potato1) (heatable potato2) (heatable potato3) (heatable tomato1) (heatable tomato2) (heatable tomato3) (inreceptacle apple1 countertop1) (inreceptacle apple2 countertop1) (inreceptacle apple3 countertop1) (inreceptacle bowl1 shelf2) (inreceptacle bread1 countertop1) (inreceptacle butterknife1 countertop3) (inreceptacle butterknife2 countertop3) (inreceptacle cellphone1 countertop1) (inreceptacle cellphone2 drawer1) (inreceptacle cellphone3 countertop3) (inreceptacle creditcard1 countertop1) (inreceptacle creditcard2 shelf2) (inreceptacle creditcard3 shelf3) (inreceptacle cup1 fridge1) (inreceptacle cup2 fridge1) (inreceptacle cup3 sinkbasin1) (inreceptacle dishsponge1 drawer2) (inreceptacle dishsponge2 cabinet5) (inreceptacle dishsponge2 plate1) (inreceptacle dishsponge3 countertop1) (inreceptacle egg1 fridge1) (inreceptacle egg2 sinkbasin1) (inreceptacle fork1 sinkbasin1) (inreceptacle fork2 countertop3) (inreceptacle glassbottle1 garbagecan1) (inreceptacle glassbottle2 shelf2) (inreceptacle houseplant1 countertop3) (inreceptacle knife1 countertop3) (inreceptacle lettuce1 fridge1) (inreceptacle mug1 microwave1) (inreceptacle mug2 shelf2) (inreceptacle pan1 stoveburner1) (inreceptacle pan1 stoveburner3) (inreceptacle papertowelroll1 garbagecan1) (inreceptacle peppershaker1 cabinet1) (inreceptacle peppershaker2 countertop3) (inreceptacle plate1 cabinet5) (inreceptacle plate2 cabinet5) (inreceptacle pot1 stoveburner2) (inreceptacle pot1 stoveburner4) (inreceptacle potato1 fridge1) (inreceptacle potato2 fridge1) (inreceptacle potato3 sinkbasin1) (inreceptacle saltshaker1 shelf3) (inreceptacle soapbottle1 cabinet6) (inreceptacle soapbottle2 garbagecan1) (inreceptacle soapbottle3 countertop3) (inreceptacle spatula1 drawer3) (inreceptacle spatula2 countertop1) (inreceptacle spatula3 countertop3) (inreceptacle spoon1 drawer2) (inreceptacle spoon2 countertop3) (inreceptacle statue1 countertop1) (inreceptacle statue2 countertop3) (inreceptacle tomato1 fridge1) (inreceptacle tomato2 fridge1) (inreceptacle tomato3 sinkbasin1) (inreceptacle vase1 cabinet6) (inreceptacle vase2 shelf1) (notopenable cabinet3) (notopenable cabinet4) (notopenable cabinet6) (notopenable coffeemachine1) (notopenable countertop1) (notopenable countertop2) (notopenable countertop3) (notopenable garbagecan1) (notopenable shelf1) (notopenable shelf2) (notopenable shelf3) (notopenable sinkbasin1) (notopenable stoveburner1) (notopenable stoveburner2) (notopenable stoveburner3) (notopenable stoveburner4) (notopenable toaster1) (notvalidated) (objectatlocation apple1 location3) (objectatlocation apple2 location3) (objectatlocation apple3 location3) (objectatlocation bowl1 location30) (objectatlocation bread1 location3) (objectatlocation butterknife1 location4) (objectatlocation butterknife2 location4) (objectatlocation cellphone1 location3) (objectatlocation cellphone2 location21) (objectatlocation cellphone3 location4) (objectatlocation chair1 location24) (objectatlocation chair2 location1) (objectatlocation creditcard1 location3) (objectatlocation creditcard2 location30) (objectatlocation creditcard3 location27) (objectatlocation cup1 location11) (objectatlocation cup2 location11) (objectatlocation cup3 location7) (objectatlocation dishsponge1 location16) (objectatlocation dishsponge2 location2) (objectatlocation dishsponge3 location3) (objectatlocation egg1 location11) (objectatlocation egg2 location7) (objectatlocation fork1 location7) (objectatlocation fork2 location4) (objectatlocation glassbottle1 location9) (objectatlocation glassbottle2 location30) (objectatlocation houseplant1 location4) (objectatlocation knife1 location4) (objectatlocation lettuce1 location11) (objectatlocation lightswitch1 location26) (objectatlocation mug1 location25) (objectatlocation mug2 location30) (objectatlocation pan1 location6) (objectatlocation papertowelroll1 location9) (objectatlocation peppershaker1 location20) (objectatlocation peppershaker2 location4) (objectatlocation plate1 location14) (objectatlocation plate2 location14) (objectatlocation pot1 location22) (objectatlocation potato1 location11) (objectatlocation potato2 location11) (objectatlocation potato3 location7) (objectatlocation saltshaker1 location27) (objectatlocation sink1 location7) (objectatlocation soapbottle1 location5) (objectatlocation soapbottle2 location9) (objectatlocation soapbottle3 location4) (objectatlocation spatula1 location28) (objectatlocation spatula2 location3) (objectatlocation spatula3 location4) (objectatlocation spoon1 location16) (objectatlocation spoon2 location4) (objectatlocation statue1 location3) (objectatlocation statue2 location4) (objectatlocation stoveknob1 location19) (objectatlocation stoveknob2 location13) (objectatlocation stoveknob3 location13) (objectatlocation stoveknob4 location8) (objectatlocation tomato1 location11) (objectatlocation tomato2 location11) (objectatlocation tomato3 location7) (objectatlocation vase1 location5) (objectatlocation vase2 location18) (objectatlocation window1 location31) (objectatlocation window2 location29) (objecttype apple1 appletype) (objecttype apple2 appletype) (objecttype apple3 appletype) (objecttype bowl1 bowltype) (objecttype bread1 breadtype) (objecttype butterknife1 butterknifetype) (objecttype butterknife2 butterknifetype) (objecttype cellphone1 cellphonetype) (objecttype cellphone2 cellphonetype) (objecttype cellphone3 cellphonetype) (objecttype chair1 chairtype) (objecttype chair2 chairtype) (objecttype creditcard1 creditcardtype) (objecttype creditcard2 creditcardtype) (objecttype creditcard3 creditcardtype) (objecttype cup1 cuptype) (objecttype cup2 cuptype) (objecttype cup3 cuptype) (objecttype dishsponge1 dishspongetype) (objecttype dishsponge2 dishspongetype) (objecttype dishsponge3 dishspongetype) (objecttype egg1 eggtype) (objecttype egg2 eggtype) (objecttype fork1 forktype) (objecttype fork2 forktype) (objecttype glassbottle1 glassbottletype) (objecttype glassbottle2 glassbottletype) (objecttype houseplant1 houseplanttype) (objecttype knife1 knifetype) (objecttype lettuce1 lettucetype) (objecttype lightswitch1 lightswitchtype) (objecttype mug1 mugtype) (objecttype mug2 mugtype) (objecttype pan1 pantype) (objecttype papertowelroll1 papertowelrolltype) (objecttype peppershaker1 peppershakertype) (objecttype peppershaker2 peppershakertype) (objecttype plate1 platetype) (objecttype plate2 platetype) (objecttype pot1 pottype) (objecttype potato1 potatotype) (objecttype potato2 potatotype) (objecttype potato3 potatotype) (objecttype saltshaker1 saltshakertype) (objecttype sink1 sinktype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype soapbottle3 soapbottletype) (objecttype spatula1 spatulatype) (objecttype spatula2 spatulatype) (objecttype spatula3 spatulatype) (objecttype spoon1 spoontype) (objecttype spoon2 spoontype) (objecttype statue1 statuetype) (objecttype statue2 statuetype) (objecttype stoveknob1 stoveknobtype) (objecttype stoveknob2 stoveknobtype) (objecttype stoveknob3 stoveknobtype) (objecttype stoveknob4 stoveknobtype) (objecttype tomato1 tomatotype) (objecttype tomato2 tomatotype) (objecttype tomato3 tomatotype) (objecttype vase1 vasetype) (objecttype vase2 vasetype) (objecttype window1 windowtype) (objecttype window2 windowtype) (openable cabinet1) (openable cabinet2) (openable cabinet5) (openable drawer1) (openable drawer2) (openable drawer3) (openable fridge1) (openable microwave1) (pickupable apple1) (pickupable apple2) (pickupable apple3) (pickupable bowl1) (pickupable bread1) (pickupable butterknife1) (pickupable butterknife2) (pickupable cellphone1) (pickupable cellphone2) (pickupable cellphone3) (pickupable creditcard1) (pickupable creditcard2) (pickupable creditcard3) (pickupable cup1) (pickupable cup2) (pickupable cup3) (pickupable dishsponge1) (pickupable dishsponge2) (pickupable dishsponge3) (pickupable egg1) (pickupable egg2) (pickupable fork1) (pickupable fork2) (pickupable glassbottle1) (pickupable glassbottle2) (pickupable knife1) (pickupable lettuce1) (pickupable mug1) (pickupable mug2) (pickupable pan1) (pickupable papertowelroll1) (pickupable peppershaker1) (pickupable peppershaker2) (pickupable plate1) (pickupable plate2) (pickupable pot1) (pickupable potato1) (pickupable potato2) (pickupable potato3) (pickupable saltshaker1) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable soapbottle3) (pickupable spatula1) (pickupable spatula2) (pickupable spatula3) (pickupable spoon1) (pickupable spoon2) (pickupable statue1) (pickupable statue2) (pickupable tomato1) (pickupable tomato2) (pickupable tomato3) (pickupable vase1) (pickupable vase2) (receptacleatlocation cabinet1 location20) (receptacleatlocation cabinet2 location23) (receptacleatlocation cabinet3 location17) (receptacleatlocation cabinet4 location15) (receptacleatlocation cabinet5 location14) (receptacleatlocation cabinet6 location5) (receptacleatlocation coffeemachine1 location12) (receptacleatlocation countertop1 location3) (receptacleatlocation countertop2 location12) (receptacleatlocation countertop3 location4) (receptacleatlocation drawer1 location21) (receptacleatlocation drawer2 location16) (receptacleatlocation drawer3 location28) (receptacleatlocation fridge1 location11) (receptacleatlocation garbagecan1 location9) (receptacleatlocation microwave1 location25) (receptacleatlocation shelf1 location18) (receptacleatlocation shelf2 location30) (receptacleatlocation shelf3 location27) (receptacleatlocation sinkbasin1 location7) (receptacleatlocation stoveburner1 location6) (receptacleatlocation stoveburner2 location22) (receptacleatlocation stoveburner3 location6) (receptacleatlocation stoveburner4 location22) (receptacleatlocation toaster1 location10) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype cabinet5 cabinettype) (receptacletype cabinet6 cabinettype) (receptacletype coffeemachine1 coffeemachinetype) (receptacletype countertop1 countertoptype) (receptacletype countertop2 countertoptype) (receptacletype countertop3 countertoptype) (receptacletype drawer1 drawertype) (receptacletype drawer2 drawertype) (receptacletype drawer3 drawertype) (receptacletype fridge1 fridgetype) (receptacletype garbagecan1 garbagecantype) (receptacletype microwave1 microwavetype) (receptacletype shelf1 shelftype) (receptacletype shelf2 shelftype) (receptacletype shelf3 shelftype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype stoveburner1 stoveburnertype) (receptacletype stoveburner2 stoveburnertype) (receptacletype stoveburner3 stoveburnertype) (receptacletype stoveburner4 stoveburnertype) (receptacletype toaster1 toastertype) (sliceable apple1) (sliceable apple2) (sliceable apple3) (sliceable bread1) (sliceable egg1) (sliceable egg2) (sliceable lettuce1) (sliceable potato1) (sliceable potato2) (sliceable potato3) (sliceable tomato1) (sliceable tomato2) (sliceable tomato3))\n    (:goal (validatepickandplace saltshakertype cabinettype))\n)"}
{"id": -6281757491233387134, "group": "reachable_action_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. garbagecan1 is at location2. cabinet2 is at location11. cabinet1 is at location4. toilet1 is at location7. handtowelholder1 is at location18. cabinet4 is at location15. handtowelholder2 is at location17. towelholder1 is at location5. sinkbasin2 is at location6. toiletpaperhanger1 is at location10. sinkbasin1 is at location16. cabinet3 is at location8. countertop1 is at location3. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. sink1 is at location12. candle1 and cloth1 are at location3. showerdoor1 is at location1. toiletpaper1 and soapbar1 are at location7. showerglass1 and towel1 are at location5. handtowel1 is at location18. handtowel2 is at location17. lightswitch1 is at location13. cloth2 is at location11. plunger1 and scrubbrush1 are at location10. soapbottle2 and spraybottle1 are at location4. mirror1 is at location9. sink2 is at location14. spraybottle2 and soapbottle1 are at location15. toiletpaper2 is at location8. spraybottle3 is at location2. agent agent1 is at location location10. The objects are in/on receptacle as follows. cloth1 and candle1 are on countertop1. spraybottle2 and soapbottle1 are in cabinet4. towel1 is on towelholder1. toiletpaper2 is in cabinet3. cloth2 is in cabinet2. handtowel2 is on handtowelholder2. handtowel1 is on handtowelholder1. spraybottle3 is in garbagecan1. soapbar1 and toiletpaper1 are in toilet1. spraybottle1 and soapbottle2 are in cabinet1. cabinet2, cabinet3, and cabinet4 are closed. soapbar2 is clean. cabinet1 is checked. cabinet1 is open. Nothing has been validated. agent1 is holding object soapbar2. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a moves from the current position ?lstart to the next position ?lend that has the receptacle ?r, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r while at location ?l, (close_receptacle ?a ?l ?r) - agent ?a that is at location ?l closes receptacle ?r, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from a not openable receptacle ?r that is at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from an openable receptacle ?r while at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o with type ?ot on a not openable receptacle ?r with type ?rt at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o of type ?ot in an openable receptacle ?r of type ?rt that is at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans object ?o in sink ?r at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a heats up object ?o with a microwave ?r that is in location ?l, (cool_object ?a ?l ?r ?o) - agent ?a cools down object ?o in a fridge ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a turns on object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co with a knife ?ko at location ?l, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - ensure that object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is hot and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is cool and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - check that the two objects ?o1 and ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(validate_clean_and_place_in_receptacle sinkbasintype alarmclocktype handtowelholder2 microwavetype)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190908_214946_567644)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location3 location4 location5 location6 location7 location8 location9 - location alarmclock apple baseballbat basketball bathtub blinds book boots bowl box bread butterknife candle candle1 cd cellphone chair cloth cloth1 cloth2 creditcard cup curtains desklamp dishsponge egg faucet1 faucet2 faucet3 floorlamp footstool fork glassbottle handtowel handtowel1 handtowel2 houseplant kettle keychain knife ladle laptop laundryhamperlid lettuce lightswitch lightswitch1 mirror mirror1 mug newspaper painting pan papertowel papertowelroll pen pencil peppershaker pillow plate plunger plunger1 poster pot potato remotecontrol saltshaker scrubbrush scrubbrush1 showerdoor showerdoor1 showerglass showerglass1 sink sink1 sink2 soapbar soapbar1 soapbar2 soapbottle soapbottle1 soapbottle2 spatula spoon spraybottle spraybottle1 spraybottle2 spraybottle3 statue stoveknob teddybear television tennisracket tissuebox toiletpaper toiletpaper1 toiletpaper2 toiletpaperroll tomato towel towel1 vase watch wateringcan window winebottle - object alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 countertop1 garbagecan1 handtowelholder1 handtowelholder2 sinkbasin1 sinkbasin2 toilet1 toiletpaperhanger1 towelholder1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location10) (cancontain cabinettype candletype) (cancontain cabinettype clothtype) (cancontain cabinettype handtoweltype) (cancontain cabinettype plungertype) (cancontain cabinettype soapbartype) (cancontain cabinettype soapbottletype) (cancontain cabinettype spraybottletype) (cancontain cabinettype toiletpapertype) (cancontain countertoptype candletype) (cancontain countertoptype clothtype) (cancontain countertoptype handtoweltype) (cancontain countertoptype soapbartype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spraybottletype) (cancontain countertoptype toiletpapertype) (cancontain garbagecantype clothtype) (cancontain garbagecantype handtoweltype) (cancontain garbagecantype soapbartype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype spraybottletype) (cancontain garbagecantype toiletpapertype) (cancontain handtowelholdertype handtoweltype) (cancontain sinkbasintype clothtype) (cancontain sinkbasintype handtoweltype) (cancontain sinkbasintype soapbartype) (cancontain toiletpaperhangertype toiletpapertype) (cancontain toilettype candletype) (cancontain toilettype clothtype) (cancontain toilettype handtoweltype) (cancontain toilettype soapbartype) (cancontain toilettype soapbottletype) (cancontain toilettype spraybottletype) (cancontain toilettype toiletpapertype) (cancontain towelholdertype toweltype) (checked cabinet1) (cleanable cloth1) (cleanable cloth2) (cleanable soapbar1) (cleanable soapbar2) (closed cabinet2) (closed cabinet3) (closed cabinet4) (holds agent1 soapbar2) (inreceptacle candle1 countertop1) (inreceptacle cloth1 countertop1) (inreceptacle cloth2 cabinet2) (inreceptacle handtowel1 handtowelholder1) (inreceptacle handtowel2 handtowelholder2) (inreceptacle soapbar1 toilet1) (inreceptacle soapbottle1 cabinet4) (inreceptacle soapbottle2 cabinet1) (inreceptacle spraybottle1 cabinet1) (inreceptacle spraybottle2 cabinet4) (inreceptacle spraybottle3 garbagecan1) (inreceptacle toiletpaper1 toilet1) (inreceptacle toiletpaper2 cabinet3) (inreceptacle towel1 towelholder1) (isclean soapbar2) (notopenable countertop1) (notopenable garbagecan1) (notopenable handtowelholder1) (notopenable handtowelholder2) (notopenable sinkbasin1) (notopenable sinkbasin2) (notopenable toilet1) (notopenable toiletpaperhanger1) (notopenable towelholder1) (notvalidated) (objectatlocation candle1 location3) (objectatlocation cloth1 location3) (objectatlocation cloth2 location11) (objectatlocation handtowel1 location18) (objectatlocation handtowel2 location17) (objectatlocation lightswitch1 location13) (objectatlocation mirror1 location9) (objectatlocation plunger1 location10) (objectatlocation scrubbrush1 location10) (objectatlocation showerdoor1 location1) (objectatlocation showerglass1 location5) (objectatlocation sink1 location12) (objectatlocation sink2 location14) (objectatlocation soapbar1 location7) (objectatlocation soapbottle1 location15) (objectatlocation soapbottle2 location4) (objectatlocation spraybottle1 location4) (objectatlocation spraybottle2 location15) (objectatlocation spraybottle3 location2) (objectatlocation toiletpaper1 location7) (objectatlocation toiletpaper2 location8) (objectatlocation towel1 location5) (objecttype candle1 candletype) (objecttype cloth1 clothtype) (objecttype cloth2 clothtype) (objecttype handtowel1 handtoweltype) (objecttype handtowel2 handtoweltype) (objecttype lightswitch1 lightswitchtype) (objecttype mirror1 mirrortype) (objecttype plunger1 plungertype) (objecttype scrubbrush1 scrubbrushtype) (objecttype showerdoor1 showerdoortype) (objecttype showerglass1 showerglasstype) (objecttype sink1 sinktype) (objecttype sink2 sinktype) (objecttype soapbar1 soapbartype) (objecttype soapbar2 soapbartype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype spraybottle1 spraybottletype) (objecttype spraybottle2 spraybottletype) (objecttype spraybottle3 spraybottletype) (objecttype toiletpaper1 toiletpapertype) (objecttype toiletpaper2 toiletpapertype) (objecttype towel1 toweltype) (openable cabinet1) (openable cabinet2) (openable cabinet3) (openable cabinet4) (opened cabinet1) (pickupable candle1) (pickupable cloth1) (pickupable cloth2) (pickupable handtowel1) (pickupable handtowel2) (pickupable plunger1) (pickupable scrubbrush1) (pickupable soapbar1) (pickupable soapbar2) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable spraybottle1) (pickupable spraybottle2) (pickupable spraybottle3) (pickupable toiletpaper1) (pickupable toiletpaper2) (pickupable towel1) (receptacleatlocation cabinet1 location4) (receptacleatlocation cabinet2 location11) (receptacleatlocation cabinet3 location8) (receptacleatlocation cabinet4 location15) (receptacleatlocation countertop1 location3) (receptacleatlocation garbagecan1 location2) (receptacleatlocation handtowelholder1 location18) (receptacleatlocation handtowelholder2 location17) (receptacleatlocation sinkbasin1 location16) (receptacleatlocation sinkbasin2 location6) (receptacleatlocation toilet1 location7) (receptacleatlocation toiletpaperhanger1 location10) (receptacleatlocation towelholder1 location5) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype countertop1 countertoptype) (receptacletype garbagecan1 garbagecantype) (receptacletype handtowelholder1 handtowelholdertype) (receptacletype handtowelholder2 handtowelholdertype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype sinkbasin2 sinkbasintype) (receptacletype toilet1 toilettype) (receptacletype toiletpaperhanger1 toiletpaperhangertype) (receptacletype towelholder1 towelholdertype))\n    (:goal (validatecleanandplace soapbartype cabinettype))\n)"}
{"id": -*******************, "group": "reachable_action_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. garbagecan1 is at location2. desk2 is at location10. shelf3 is at location11. drawer3 is at location17. shelf2 is at location25. bed1 is at location13. safe1 is at location6. drawer2 is at location18. shelf6 is at location24. laundryhamper1 is at location8. drawer4 and drawer5 are at location12. drawer6 is at location1. shelf5 is at location22. shelf1 is at location20. shelf4 is at location23. desk1 is at location3. drawer1 is at location21. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. cellphone1, pillow1, pillow2, laptop1, and laptop2 are at location13. blinds2 is at location15. cellphone3 is at location12. bowl1, cd1, pencil1, alarmclock1, and mug1 are at location3. pen1, pencil3, mug2, cellphone2, and cd3 are at location10. chair1 is at location21. mirror1 is at location19. cd2 is at location2. window2 is at location4. bowl3 is at location24. keychain1 and keychain2 are at location6. baseballbat1 is at location9. window1 is at location5. alarmclock2 is at location11. chair2 is at location26. laundryhamperlid1 is at location8. alarmclock3, bowl2, and desklamp1 are at location23. pencil2 and creditcard1 are at location22. blinds1 is at location16. basketball1 is at location7. lightswitch1 is at location14. agent agent1 is at location location12. The objects are in/on receptacle as follows. bowl3 is on shelf6. pen1, desklamp1, mug2, alarmclock3, pencil3, cellphone2, cd3, and bowl2 are on desk2. keychain1 and keychain2 are in safe1. cellphone3 is in drawer5. alarmclock1, bowl1, cd1, mug1, and pencil1 are on desk1. alarmclock3, desklamp1, and bowl2 are on shelf4. pillow2, cellphone1, pillow1, laptop1, and laptop2 are in bed1. creditcard1 and pencil2 are on shelf5. alarmclock2 is on shelf3. cd2 is in garbagecan1. drawer3, drawer1, safe1, and drawer6 are closed. desklamp1 is off. Nothing has been validated. agent1 is holding object book1. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a moves from the current position ?lstart to the next position ?lend that has the receptacle ?r, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r that is at location ?l, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r that is at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from a not openable receptacle ?r that is at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a collects object ?o from an openable receptacle ?r while at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o of type ?ot on a not openable receptacle ?r of type ?rt while at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o with type ?ot in an openable receptacle ?r with type ?rt at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans object ?o in a receptacle ?r that is at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a warms up a heatable object ?o with a receptacle ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a chills object ?o in a fridge ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co that is at location ?l with a knife ?ko, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - check that the togglable object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is warmed up and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is chilled and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - check that the two objects ?o1 and ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(validate_cool_and_place_in_receptacle pillow spoontype drawer6 desktype)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190908_020029_636862)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location20 location21 location22 location23 location24 location25 location26 location27 location3 location4 location5 location6 location7 location8 location9 - location alarmclock alarmclock1 alarmclock2 alarmclock3 apple baseballbat baseballbat1 basketball basketball1 bathtub blinds blinds1 blinds2 book book1 boots bowl bowl1 bowl2 bowl3 box bread butterknife candle cd cd1 cd2 cd3 cellphone cellphone1 cellphone2 cellphone3 chair chair1 chair2 cloth creditcard creditcard1 cup curtains desklamp desklamp1 dishsponge egg floorlamp footstool fork glassbottle handtowel houseplant kettle keychain keychain1 keychain2 knife ladle laptop laptop1 laptop2 laundryhamperlid laundryhamperlid1 lettuce lightswitch lightswitch1 mirror mirror1 mug mug1 mug2 newspaper painting pan papertowel papertowelroll pen pen1 pencil pencil1 pencil2 pencil3 peppershaker pillow pillow1 pillow2 plate plunger poster pot potato remotecontrol saltshaker scrubbrush showerdoor showerglass sink soapbar soapbottle spatula spoon spraybottle statue stoveknob teddybear television tennisracket tissuebox toiletpaper toiletpaperroll tomato towel vase watch wateringcan window window1 window2 winebottle - object alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype bed1 desk1 desk2 drawer1 drawer2 drawer3 drawer4 drawer5 drawer6 garbagecan1 laundryhamper1 safe1 shelf1 shelf2 shelf3 shelf4 shelf5 shelf6 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location12) (cancontain bedtype baseballbattype) (cancontain bedtype basketballtype) (cancontain bedtype booktype) (cancontain bedtype cellphonetype) (cancontain bedtype laptoptype) (cancontain bedtype pillowtype) (cancontain desktype alarmclocktype) (cancontain desktype basketballtype) (cancontain desktype booktype) (cancontain desktype bowltype) (cancontain desktype cdtype) (cancontain desktype cellphonetype) (cancontain desktype creditcardtype) (cancontain desktype keychaintype) (cancontain desktype laptoptype) (cancontain desktype mugtype) (cancontain desktype penciltype) (cancontain desktype pentype) (cancontain drawertype booktype) (cancontain drawertype cdtype) (cancontain drawertype cellphonetype) (cancontain drawertype creditcardtype) (cancontain drawertype keychaintype) (cancontain drawertype penciltype) (cancontain drawertype pentype) (cancontain garbagecantype cdtype) (cancontain garbagecantype penciltype) (cancontain garbagecantype pentype) (cancontain safetype cdtype) (cancontain safetype cellphonetype) (cancontain safetype creditcardtype) (cancontain safetype keychaintype) (cancontain shelftype alarmclocktype) (cancontain shelftype booktype) (cancontain shelftype bowltype) (cancontain shelftype cdtype) (cancontain shelftype cellphonetype) (cancontain shelftype creditcardtype) (cancontain shelftype keychaintype) (cancontain shelftype mugtype) (cancontain shelftype penciltype) (cancontain shelftype pentype) (cleanable bowl1) (cleanable bowl2) (cleanable bowl3) (cleanable mug1) (cleanable mug2) (closed drawer1) (closed drawer3) (closed drawer6) (closed safe1) (coolable bowl1) (coolable bowl2) (coolable bowl3) (coolable mug1) (coolable mug2) (heatable mug1) (heatable mug2) (holds agent1 book1) (inreceptacle alarmclock1 desk1) (inreceptacle alarmclock2 shelf3) (inreceptacle alarmclock3 desk2) (inreceptacle alarmclock3 shelf4) (inreceptacle bowl1 desk1) (inreceptacle bowl2 desk2) (inreceptacle bowl2 shelf4) (inreceptacle bowl3 shelf6) (inreceptacle cd1 desk1) (inreceptacle cd2 garbagecan1) (inreceptacle cd3 desk2) (inreceptacle cellphone1 bed1) (inreceptacle cellphone2 desk2) (inreceptacle cellphone3 drawer5) (inreceptacle creditcard1 shelf5) (inreceptacle desklamp1 desk2) (inreceptacle desklamp1 shelf4) (inreceptacle keychain1 safe1) (inreceptacle keychain2 safe1) (inreceptacle laptop1 bed1) (inreceptacle laptop2 bed1) (inreceptacle mug1 desk1) (inreceptacle mug2 desk2) (inreceptacle pen1 desk2) (inreceptacle pencil1 desk1) (inreceptacle pencil2 shelf5) (inreceptacle pencil3 desk2) (inreceptacle pillow1 bed1) (inreceptacle pillow2 bed1) (isoff desklamp1) (notopenable bed1) (notopenable desk1) (notopenable desk2) (notopenable drawer2) (notopenable drawer4) (notopenable drawer5) (notopenable garbagecan1) (notopenable laundryhamper1) (notopenable shelf1) (notopenable shelf2) (notopenable shelf3) (notopenable shelf4) (notopenable shelf5) (notopenable shelf6) (notvalidated) (objectatlocation alarmclock1 location3) (objectatlocation alarmclock2 location11) (objectatlocation alarmclock3 location23) (objectatlocation baseballbat1 location9) (objectatlocation basketball1 location7) (objectatlocation blinds1 location16) (objectatlocation blinds2 location15) (objectatlocation bowl1 location3) (objectatlocation bowl2 location23) (objectatlocation bowl3 location24) (objectatlocation cd1 location3) (objectatlocation cd2 location2) (objectatlocation cd3 location10) (objectatlocation cellphone1 location13) (objectatlocation cellphone2 location10) (objectatlocation cellphone3 location12) (objectatlocation chair1 location21) (objectatlocation chair2 location26) (objectatlocation creditcard1 location22) (objectatlocation desklamp1 location23) (objectatlocation keychain1 location6) (objectatlocation keychain2 location6) (objectatlocation laptop1 location13) (objectatlocation laptop2 location13) (objectatlocation laundryhamperlid1 location8) (objectatlocation lightswitch1 location14) (objectatlocation mirror1 location19) (objectatlocation mug1 location3) (objectatlocation mug2 location10) (objectatlocation pen1 location10) (objectatlocation pencil1 location3) (objectatlocation pencil2 location22) (objectatlocation pencil3 location10) (objectatlocation pillow1 location13) (objectatlocation pillow2 location13) (objectatlocation window1 location5) (objectatlocation window2 location4) (objecttype alarmclock1 alarmclocktype) (objecttype alarmclock2 alarmclocktype) (objecttype alarmclock3 alarmclocktype) (objecttype baseballbat1 baseballbattype) (objecttype basketball1 basketballtype) (objecttype blinds1 blindstype) (objecttype blinds2 blindstype) (objecttype book1 booktype) (objecttype bowl1 bowltype) (objecttype bowl2 bowltype) (objecttype bowl3 bowltype) (objecttype cd1 cdtype) (objecttype cd2 cdtype) (objecttype cd3 cdtype) (objecttype cellphone1 cellphonetype) (objecttype cellphone2 cellphonetype) (objecttype cellphone3 cellphonetype) (objecttype chair1 chairtype) (objecttype chair2 chairtype) (objecttype creditcard1 creditcardtype) (objecttype desklamp1 desklamptype) (objecttype keychain1 keychaintype) (objecttype keychain2 keychaintype) (objecttype laptop1 laptoptype) (objecttype laptop2 laptoptype) (objecttype laundryhamperlid1 laundryhamperlidtype) (objecttype lightswitch1 lightswitchtype) (objecttype mirror1 mirrortype) (objecttype mug1 mugtype) (objecttype mug2 mugtype) (objecttype pen1 pentype) (objecttype pencil1 penciltype) (objecttype pencil2 penciltype) (objecttype pencil3 penciltype) (objecttype pillow1 pillowtype) (objecttype pillow2 pillowtype) (objecttype window1 windowtype) (objecttype window2 windowtype) (openable drawer1) (openable drawer3) (openable drawer6) (openable safe1) (pickupable alarmclock1) (pickupable alarmclock2) (pickupable alarmclock3) (pickupable baseballbat1) (pickupable basketball1) (pickupable book1) (pickupable bowl1) (pickupable bowl2) (pickupable bowl3) (pickupable cd1) (pickupable cd2) (pickupable cd3) (pickupable cellphone1) (pickupable cellphone2) (pickupable cellphone3) (pickupable creditcard1) (pickupable keychain1) (pickupable keychain2) (pickupable laptop1) (pickupable laptop2) (pickupable mug1) (pickupable mug2) (pickupable pen1) (pickupable pencil1) (pickupable pencil2) (pickupable pencil3) (pickupable pillow1) (pickupable pillow2) (receptacleatlocation bed1 location13) (receptacleatlocation desk1 location3) (receptacleatlocation desk2 location10) (receptacleatlocation drawer1 location21) (receptacleatlocation drawer2 location18) (receptacleatlocation drawer3 location17) (receptacleatlocation drawer4 location12) (receptacleatlocation drawer5 location12) (receptacleatlocation drawer6 location1) (receptacleatlocation garbagecan1 location2) (receptacleatlocation laundryhamper1 location8) (receptacleatlocation safe1 location6) (receptacleatlocation shelf1 location20) (receptacleatlocation shelf2 location25) (receptacleatlocation shelf3 location11) (receptacleatlocation shelf4 location23) (receptacleatlocation shelf5 location22) (receptacleatlocation shelf6 location24) (receptacletype bed1 bedtype) (receptacletype desk1 desktype) (receptacletype desk2 desktype) (receptacletype drawer1 drawertype) (receptacletype drawer2 drawertype) (receptacletype drawer3 drawertype) (receptacletype drawer4 drawertype) (receptacletype drawer5 drawertype) (receptacletype drawer6 drawertype) (receptacletype garbagecan1 garbagecantype) (receptacletype laundryhamper1 laundryhampertype) (receptacletype safe1 safetype) (receptacletype shelf1 shelftype) (receptacletype shelf2 shelftype) (receptacletype shelf3 shelftype) (receptacletype shelf4 shelftype) (receptacletype shelf5 shelftype) (receptacletype shelf6 shelftype) (toggleable desklamp1))\n    (:goal (validateexamineinlight desklamptype booktype))\n)"}
{"id": -8987086661864095205, "group": "reachable_action_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. toaster1 is at location9. coffeemachine1 and countertop2 are at location11. stoveburner1 and stoveburner3 are at location5. shelf2 is at location29. countertop3 is at location3. countertop1 is at location2. garbagecan1 is at location8. drawer3 is at location27. fridge1 is at location10. shelf3 is at location26. shelf1 is at location17. stoveburner2 and stoveburner4 are at location21. cabinet5 is at location13. microwave1 is at location24. drawer1 is at location20. cabinet1 is at location19. cabinet4 is at location14. cabinet2 is at location22. cabinet3 is at location16. drawer2 is at location15. cabinet6 is at location4. sinkbasin1 is at location6. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. potato1, apple1, cup1, cup2, lettuce1, and potato2 are at location10. saltshaker3 is at location26. butterknife1, spoon3, saltshaker2, knife1, soapbottle1, fork2, houseplant1, tomato1, glassbottle3, and lettuce3 are at location3. lettuce2, cellphone3, creditcard2, spoon1, bread1, mug1, creditcard1, and statue1 are at location2. apple2 and egg1 are at location8. pot1 is at location5. stoveknob1 is at location18. stoveknob4 is at location7. spatula1 is at location27. stoveknob3 and stoveknob2 are at location12. saltshaker1 is at location16. dishsponge1 is at location15. window2 is at location28. spoon2, spatula2, fork1, and sink1 are at location6. lightswitch1 is at location25. chair1 is at location23. chair2 is at location1. papertowelroll1 and vase2 are at location29. peppershaker2 and cellphone2 are at location20. bowl1 and glassbottle2 are at location14. pan1, peppershaker1, and cellphone1 are at location11. vase1 is at location17. plate1 is at location4. glassbottle1 is at location19. window1 is at location30. agent agent1 is at location location13. The objects are in/on receptacle as follows. bread1, cellphone3, creditcard1, lettuce2, spoon1, mug1, creditcard2, and statue1 are on countertop1. dishsponge1 is in drawer2. apple2 and egg1 are in garbagecan1. vase2 and papertowelroll1 are on shelf2. saltshaker2, butterknife1, fork2, soapbottle1, spoon3, glassbottle3, lettuce3, houseplant1, knife1, and tomato1 are on countertop3. pan1, cellphone1, and peppershaker1 are on countertop2. cellphone2 and peppershaker2 are in drawer1. glassbottle1 is in cabinet1. vase1 is on shelf1. lettuce1, potato2, apple1, cup1, potato1, and cup2 are in fridge1. saltshaker3 is on shelf3. saltshaker1 is in cabinet3. spoon2, spatula2, and fork1 are in sinkbasin1. pan1 is on stoveburner2. pan1 is on stoveburner4. pot1 is on stoveburner3. plate1 is in cabinet6. bowl1 and glassbottle2 are in cabinet4. pot1 is on stoveburner1. spatula1 is in drawer3. cabinet5, drawer2, cabinet1, fridge1, drawer1, drawer3, cabinet2, and microwave1 are closed. Nothing has been validated. agent1 is holding object egg2. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a goes to receptacle ?r from the current location ?lstart to the next location ?lend, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r that is at location ?l, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r while at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a collects object ?o from a not openable receptacle ?r while at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from an openable receptacle ?r that is at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o with type ?ot on a not openable receptacle ?r with type ?rt at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot in an openable receptacle ?r with type ?rt at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans object ?o in sink ?r at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a warms up object ?o with a microwave ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a cools down object ?o with a receptacle ?r that is in location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a turns off object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a turns on object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts object ?co with a knife ?ko at location ?l, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - ensure that object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is hot and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is cool and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - validate that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(cool_object agent1 location27 coffeemachine1 location27)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190908_113523_123938)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location20 location21 location22 location23 location24 location25 location26 location27 location28 location29 location3 location30 location31 location4 location5 location6 location7 location8 location9 - location alarmclock apple apple1 apple2 baseballbat basketball bathtub blinds book boots bowl bowl1 box bread bread1 butterknife butterknife1 candle cd cellphone cellphone1 cellphone2 cellphone3 chair chair1 chair2 cloth creditcard creditcard1 creditcard2 cup cup1 cup2 curtains desklamp dishsponge dishsponge1 egg egg1 egg2 faucet1 floorlamp footstool fork fork1 fork2 glassbottle glassbottle1 glassbottle2 glassbottle3 handtowel houseplant houseplant1 kettle keychain knife knife1 ladle laptop laundryhamperlid lettuce lettuce1 lettuce2 lettuce3 lightswitch lightswitch1 mirror mug mug1 newspaper painting pan pan1 papertowel papertowelroll papertowelroll1 pen pencil peppershaker peppershaker1 peppershaker2 pillow plate plate1 plunger poster pot pot1 potato potato1 potato2 remotecontrol saltshaker saltshaker1 saltshaker2 saltshaker3 scrubbrush showerdoor showerglass sink sink1 soapbar soapbottle soapbottle1 spatula spatula1 spatula2 spoon spoon1 spoon2 spoon3 spraybottle statue statue1 stoveknob stoveknob1 stoveknob2 stoveknob3 stoveknob4 teddybear television tennisracket tissuebox toiletpaper toiletpaperroll tomato tomato1 towel vase vase1 vase2 watch wateringcan window window1 window2 winebottle - object alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 cabinet5 cabinet6 coffeemachine1 countertop1 countertop2 countertop3 drawer1 drawer2 drawer3 fridge1 garbagecan1 microwave1 shelf1 shelf2 shelf3 sinkbasin1 stoveburner1 stoveburner2 stoveburner3 stoveburner4 toaster1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location13) (cancontain cabinettype bowltype) (cancontain cabinettype cuptype) (cancontain cabinettype dishspongetype) (cancontain cabinettype glassbottletype) (cancontain cabinettype mugtype) (cancontain cabinettype pantype) (cancontain cabinettype peppershakertype) (cancontain cabinettype platetype) (cancontain cabinettype pottype) (cancontain cabinettype saltshakertype) (cancontain cabinettype soapbottletype) (cancontain cabinettype vasetype) (cancontain coffeemachinetype mugtype) (cancontain countertoptype appletype) (cancontain countertoptype bowltype) (cancontain countertoptype breadtype) (cancontain countertoptype butterknifetype) (cancontain countertoptype cellphonetype) (cancontain countertoptype creditcardtype) (cancontain countertoptype cuptype) (cancontain countertoptype dishspongetype) (cancontain countertoptype eggtype) (cancontain countertoptype forktype) (cancontain countertoptype glassbottletype) (cancontain countertoptype knifetype) (cancontain countertoptype lettucetype) (cancontain countertoptype mugtype) (cancontain countertoptype pantype) (cancontain countertoptype peppershakertype) (cancontain countertoptype platetype) (cancontain countertoptype potatotype) (cancontain countertoptype pottype) (cancontain countertoptype saltshakertype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spatulatype) (cancontain countertoptype spoontype) (cancontain countertoptype statuetype) (cancontain countertoptype tomatotype) (cancontain countertoptype vasetype) (cancontain drawertype butterknifetype) (cancontain drawertype cellphonetype) (cancontain drawertype creditcardtype) (cancontain drawertype dishspongetype) (cancontain drawertype forktype) (cancontain drawertype knifetype) (cancontain drawertype peppershakertype) (cancontain drawertype saltshakertype) (cancontain drawertype soapbottletype) (cancontain drawertype spatulatype) (cancontain drawertype spoontype) (cancontain fridgetype appletype) (cancontain fridgetype bowltype) (cancontain fridgetype breadtype) (cancontain fridgetype cuptype) (cancontain fridgetype eggtype) (cancontain fridgetype glassbottletype) (cancontain fridgetype lettucetype) (cancontain fridgetype mugtype) (cancontain fridgetype pantype) (cancontain fridgetype platetype) (cancontain fridgetype potatotype) (cancontain fridgetype pottype) (cancontain fridgetype tomatotype) (cancontain garbagecantype appletype) (cancontain garbagecantype breadtype) (cancontain garbagecantype dishspongetype) (cancontain garbagecantype eggtype) (cancontain garbagecantype lettucetype) (cancontain garbagecantype potatotype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype tomatotype) (cancontain microwavetype appletype) (cancontain microwavetype bowltype) (cancontain microwavetype breadtype) (cancontain microwavetype cuptype) (cancontain microwavetype eggtype) (cancontain microwavetype glassbottletype) (cancontain microwavetype mugtype) (cancontain microwavetype platetype) (cancontain microwavetype potatotype) (cancontain microwavetype tomatotype) (cancontain shelftype bowltype) (cancontain shelftype cellphonetype) (cancontain shelftype creditcardtype) (cancontain shelftype cuptype) (cancontain shelftype dishspongetype) (cancontain shelftype glassbottletype) (cancontain shelftype mugtype) (cancontain shelftype peppershakertype) (cancontain shelftype platetype) (cancontain shelftype pottype) (cancontain shelftype saltshakertype) (cancontain shelftype soapbottletype) (cancontain shelftype statuetype) (cancontain shelftype vasetype) (cancontain sinkbasintype appletype) (cancontain sinkbasintype bowltype) (cancontain sinkbasintype butterknifetype) (cancontain sinkbasintype cuptype) (cancontain sinkbasintype dishspongetype) (cancontain sinkbasintype eggtype) (cancontain sinkbasintype forktype) (cancontain sinkbasintype glassbottletype) (cancontain sinkbasintype knifetype) (cancontain sinkbasintype lettucetype) (cancontain sinkbasintype mugtype) (cancontain sinkbasintype pantype) (cancontain sinkbasintype platetype) (cancontain sinkbasintype potatotype) (cancontain sinkbasintype pottype) (cancontain sinkbasintype spatulatype) (cancontain sinkbasintype spoontype) (cancontain sinkbasintype tomatotype) (cancontain stoveburnertype pantype) (cancontain stoveburnertype pottype) (cleanable apple1) (cleanable apple2) (cleanable bowl1) (cleanable butterknife1) (cleanable cup1) (cleanable cup2) (cleanable dishsponge1) (cleanable egg1) (cleanable egg2) (cleanable fork1) (cleanable fork2) (cleanable knife1) (cleanable lettuce1) (cleanable lettuce2) (cleanable lettuce3) (cleanable mug1) (cleanable pan1) (cleanable plate1) (cleanable pot1) (cleanable potato1) (cleanable potato2) (cleanable spatula1) (cleanable spatula2) (cleanable spoon1) (cleanable spoon2) (cleanable spoon3) (cleanable tomato1) (closed cabinet1) (closed cabinet2) (closed cabinet5) (closed drawer1) (closed drawer2) (closed drawer3) (closed fridge1) (closed microwave1) (coolable apple1) (coolable apple2) (coolable bowl1) (coolable bread1) (coolable cup1) (coolable cup2) (coolable egg1) (coolable egg2) (coolable lettuce1) (coolable lettuce2) (coolable lettuce3) (coolable mug1) (coolable pan1) (coolable plate1) (coolable pot1) (coolable potato1) (coolable potato2) (coolable tomato1) (heatable apple1) (heatable apple2) (heatable bread1) (heatable cup1) (heatable cup2) (heatable egg1) (heatable egg2) (heatable mug1) (heatable plate1) (heatable potato1) (heatable potato2) (heatable tomato1) (holds agent1 egg2) (inreceptacle apple1 fridge1) (inreceptacle apple2 garbagecan1) (inreceptacle bowl1 cabinet4) (inreceptacle bread1 countertop1) (inreceptacle butterknife1 countertop3) (inreceptacle cellphone1 countertop2) (inreceptacle cellphone2 drawer1) (inreceptacle cellphone3 countertop1) (inreceptacle creditcard1 countertop1) (inreceptacle creditcard2 countertop1) (inreceptacle cup1 fridge1) (inreceptacle cup2 fridge1) (inreceptacle dishsponge1 drawer2) (inreceptacle egg1 garbagecan1) (inreceptacle fork1 sinkbasin1) (inreceptacle fork2 countertop3) (inreceptacle glassbottle1 cabinet1) (inreceptacle glassbottle2 cabinet4) (inreceptacle glassbottle3 countertop3) (inreceptacle houseplant1 countertop3) (inreceptacle knife1 countertop3) (inreceptacle lettuce1 fridge1) (inreceptacle lettuce2 countertop1) (inreceptacle lettuce3 countertop3) (inreceptacle mug1 countertop1) (inreceptacle pan1 countertop2) (inreceptacle pan1 stoveburner2) (inreceptacle pan1 stoveburner4) (inreceptacle papertowelroll1 shelf2) (inreceptacle peppershaker1 countertop2) (inreceptacle peppershaker2 drawer1) (inreceptacle plate1 cabinet6) (inreceptacle pot1 stoveburner1) (inreceptacle pot1 stoveburner3) (inreceptacle potato1 fridge1) (inreceptacle potato2 fridge1) (inreceptacle saltshaker1 cabinet3) (inreceptacle saltshaker2 countertop3) (inreceptacle saltshaker3 shelf3) (inreceptacle soapbottle1 countertop3) (inreceptacle spatula1 drawer3) (inreceptacle spatula2 sinkbasin1) (inreceptacle spoon1 countertop1) (inreceptacle spoon2 sinkbasin1) (inreceptacle spoon3 countertop3) (inreceptacle statue1 countertop1) (inreceptacle tomato1 countertop3) (inreceptacle vase1 shelf1) (inreceptacle vase2 shelf2) (notopenable cabinet3) (notopenable cabinet4) (notopenable cabinet6) (notopenable coffeemachine1) (notopenable countertop1) (notopenable countertop2) (notopenable countertop3) (notopenable garbagecan1) (notopenable shelf1) (notopenable shelf2) (notopenable shelf3) (notopenable sinkbasin1) (notopenable stoveburner1) (notopenable stoveburner2) (notopenable stoveburner3) (notopenable stoveburner4) (notopenable toaster1) (notvalidated) (objectatlocation apple1 location10) (objectatlocation apple2 location8) (objectatlocation bowl1 location14) (objectatlocation bread1 location2) (objectatlocation butterknife1 location3) (objectatlocation cellphone1 location11) (objectatlocation cellphone2 location20) (objectatlocation cellphone3 location2) (objectatlocation chair1 location23) (objectatlocation chair2 location1) (objectatlocation creditcard1 location2) (objectatlocation creditcard2 location2) (objectatlocation cup1 location10) (objectatlocation cup2 location10) (objectatlocation dishsponge1 location15) (objectatlocation egg1 location8) (objectatlocation fork1 location6) (objectatlocation fork2 location3) (objectatlocation glassbottle1 location19) (objectatlocation glassbottle2 location14) (objectatlocation glassbottle3 location3) (objectatlocation houseplant1 location3) (objectatlocation knife1 location3) (objectatlocation lettuce1 location10) (objectatlocation lettuce2 location2) (objectatlocation lettuce3 location3) (objectatlocation lightswitch1 location25) (objectatlocation mug1 location2) (objectatlocation pan1 location11) (objectatlocation papertowelroll1 location29) (objectatlocation peppershaker1 location11) (objectatlocation peppershaker2 location20) (objectatlocation plate1 location4) (objectatlocation pot1 location5) (objectatlocation potato1 location10) (objectatlocation potato2 location10) (objectatlocation saltshaker1 location16) (objectatlocation saltshaker2 location3) (objectatlocation saltshaker3 location26) (objectatlocation sink1 location6) (objectatlocation soapbottle1 location3) (objectatlocation spatula1 location27) (objectatlocation spatula2 location6) (objectatlocation spoon1 location2) (objectatlocation spoon2 location6) (objectatlocation spoon3 location3) (objectatlocation statue1 location2) (objectatlocation stoveknob1 location18) (objectatlocation stoveknob2 location12) (objectatlocation stoveknob3 location12) (objectatlocation stoveknob4 location7) (objectatlocation tomato1 location3) (objectatlocation vase1 location17) (objectatlocation vase2 location29) (objectatlocation window1 location30) (objectatlocation window2 location28) (objecttype apple1 appletype) (objecttype apple2 appletype) (objecttype bowl1 bowltype) (objecttype bread1 breadtype) (objecttype butterknife1 butterknifetype) (objecttype cellphone1 cellphonetype) (objecttype cellphone2 cellphonetype) (objecttype cellphone3 cellphonetype) (objecttype chair1 chairtype) (objecttype chair2 chairtype) (objecttype creditcard1 creditcardtype) (objecttype creditcard2 creditcardtype) (objecttype cup1 cuptype) (objecttype cup2 cuptype) (objecttype dishsponge1 dishspongetype) (objecttype egg1 eggtype) (objecttype egg2 eggtype) (objecttype fork1 forktype) (objecttype fork2 forktype) (objecttype glassbottle1 glassbottletype) (objecttype glassbottle2 glassbottletype) (objecttype glassbottle3 glassbottletype) (objecttype houseplant1 houseplanttype) (objecttype knife1 knifetype) (objecttype lettuce1 lettucetype) (objecttype lettuce2 lettucetype) (objecttype lettuce3 lettucetype) (objecttype lightswitch1 lightswitchtype) (objecttype mug1 mugtype) (objecttype pan1 pantype) (objecttype papertowelroll1 papertowelrolltype) (objecttype peppershaker1 peppershakertype) (objecttype peppershaker2 peppershakertype) (objecttype plate1 platetype) (objecttype pot1 pottype) (objecttype potato1 potatotype) (objecttype potato2 potatotype) (objecttype saltshaker1 saltshakertype) (objecttype saltshaker2 saltshakertype) (objecttype saltshaker3 saltshakertype) (objecttype sink1 sinktype) (objecttype soapbottle1 soapbottletype) (objecttype spatula1 spatulatype) (objecttype spatula2 spatulatype) (objecttype spoon1 spoontype) (objecttype spoon2 spoontype) (objecttype spoon3 spoontype) (objecttype statue1 statuetype) (objecttype stoveknob1 stoveknobtype) (objecttype stoveknob2 stoveknobtype) (objecttype stoveknob3 stoveknobtype) (objecttype stoveknob4 stoveknobtype) (objecttype tomato1 tomatotype) (objecttype vase1 vasetype) (objecttype vase2 vasetype) (objecttype window1 windowtype) (objecttype window2 windowtype) (openable cabinet1) (openable cabinet2) (openable cabinet5) (openable drawer1) (openable drawer2) (openable drawer3) (openable fridge1) (openable microwave1) (pickupable apple1) (pickupable apple2) (pickupable bowl1) (pickupable bread1) (pickupable butterknife1) (pickupable cellphone1) (pickupable cellphone2) (pickupable cellphone3) (pickupable creditcard1) (pickupable creditcard2) (pickupable cup1) (pickupable cup2) (pickupable dishsponge1) (pickupable egg1) (pickupable egg2) (pickupable fork1) (pickupable fork2) (pickupable glassbottle1) (pickupable glassbottle2) (pickupable glassbottle3) (pickupable knife1) (pickupable lettuce1) (pickupable lettuce2) (pickupable lettuce3) (pickupable mug1) (pickupable pan1) (pickupable papertowelroll1) (pickupable peppershaker1) (pickupable peppershaker2) (pickupable plate1) (pickupable pot1) (pickupable potato1) (pickupable potato2) (pickupable saltshaker1) (pickupable saltshaker2) (pickupable saltshaker3) (pickupable soapbottle1) (pickupable spatula1) (pickupable spatula2) (pickupable spoon1) (pickupable spoon2) (pickupable spoon3) (pickupable statue1) (pickupable tomato1) (pickupable vase1) (pickupable vase2) (receptacleatlocation cabinet1 location19) (receptacleatlocation cabinet2 location22) (receptacleatlocation cabinet3 location16) (receptacleatlocation cabinet4 location14) (receptacleatlocation cabinet5 location13) (receptacleatlocation cabinet6 location4) (receptacleatlocation coffeemachine1 location11) (receptacleatlocation countertop1 location2) (receptacleatlocation countertop2 location11) (receptacleatlocation countertop3 location3) (receptacleatlocation drawer1 location20) (receptacleatlocation drawer2 location15) (receptacleatlocation drawer3 location27) (receptacleatlocation fridge1 location10) (receptacleatlocation garbagecan1 location8) (receptacleatlocation microwave1 location24) (receptacleatlocation shelf1 location17) (receptacleatlocation shelf2 location29) (receptacleatlocation shelf3 location26) (receptacleatlocation sinkbasin1 location6) (receptacleatlocation stoveburner1 location5) (receptacleatlocation stoveburner2 location21) (receptacleatlocation stoveburner3 location5) (receptacleatlocation stoveburner4 location21) (receptacleatlocation toaster1 location9) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype cabinet5 cabinettype) (receptacletype cabinet6 cabinettype) (receptacletype coffeemachine1 coffeemachinetype) (receptacletype countertop1 countertoptype) (receptacletype countertop2 countertoptype) (receptacletype countertop3 countertoptype) (receptacletype drawer1 drawertype) (receptacletype drawer2 drawertype) (receptacletype drawer3 drawertype) (receptacletype fridge1 fridgetype) (receptacletype garbagecan1 garbagecantype) (receptacletype microwave1 microwavetype) (receptacletype shelf1 shelftype) (receptacletype shelf2 shelftype) (receptacletype shelf3 shelftype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype stoveburner1 stoveburnertype) (receptacletype stoveburner2 stoveburnertype) (receptacletype stoveburner3 stoveburnertype) (receptacletype stoveburner4 stoveburnertype) (receptacletype toaster1 toastertype) (sliceable apple1) (sliceable apple2) (sliceable bread1) (sliceable egg1) (sliceable egg2) (sliceable lettuce1) (sliceable lettuce2) (sliceable lettuce3) (sliceable potato1) (sliceable potato2) (sliceable tomato1))\n    (:goal (validateheatandplace eggtype garbagecantype))\n)"}
{"id": -1849797190471058403, "group": "reachable_action_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. fridge1 is at location11. toaster1 is at location10. drawer3 is at location28. countertop1 is at location3. stoveburner2 and stoveburner4 are at location22. countertop3 is at location4. stoveburner3 and stoveburner1 are at location6. cabinet2 is at location23. cabinet1 is at location20. cabinet4 is at location15. cabinet5 is at location14. coffeemachine1 and countertop2 are at location12. microwave1 is at location25. sinkbasin1 is at location7. cabinet6 is at location5. cabinet3 is at location17. drawer1 is at location21. shelf3 is at location27. garbagecan1 is at location9. drawer2 is at location16. shelf1 is at location18. shelf2 is at location30. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. papertowelroll1, soapbottle2, and glassbottle1 are at location9. mug2, creditcard2, glassbottle2, and bowl1 are at location30. creditcard1, apple3, apple2, spatula2, bread1, cellphone1, apple1, dishsponge3, and statue1 are at location3. fork2, statue2, cellphone3, butterknife1, peppershaker2, butterknife2, soapbottle3, spatula3, houseplant1, knife1, and spoon2 are at location4. stoveknob3 and stoveknob2 are at location13. vase2 is at location18. egg2, tomato3, cup3, fork1, potato3, and sink1 are at location7. creditcard3 and saltshaker1 are at location27. spoon1 and dishsponge1 are at location16. tomato1, cup1, cup2, egg1, tomato2, lettuce1, potato2, and potato1 are at location11. peppershaker1 is at location20. plate2 and plate1 are at location14. stoveknob1 is at location19. chair1 is at location24. chair2 is at location1. pot1 is at location22. dishsponge2 is at location2. soapbottle1 and vase1 are at location5. lightswitch1 is at location26. window2 is at location29. spatula1 is at location28. stoveknob4 is at location8. cellphone2 is at location21. window1 is at location31. mug1 is at location25. agent agent1 is at location location4. The objects are in/on receptacle as follows. bread1, cellphone1, apple1, creditcard1, apple2, apple3, spatula2, dishsponge3, and statue1 are on countertop1. cellphone3, peppershaker2, statue2, butterknife1, fork2, houseplant1, spoon2, knife1, butterknife2, soapbottle3, and spatula3 are on countertop3. dishsponge1 and spoon1 are in drawer2. plate2, dishsponge2, and plate1 are in cabinet5. dishsponge2 is on plate1. tomato3, potato3, egg2, cup3, and fork1 are in sinkbasin1. cellphone2 is in drawer1. lettuce1, potato2, tomato2, tomato1, cup1, egg1, potato1, and cup2 are in fridge1. mug1 is in microwave1. glassbottle2, creditcard2, bowl1, and mug2 are on shelf2. pot1 is on stoveburner2. vase1 and soapbottle1 are in cabinet6. glassbottle1, papertowelroll1, and soapbottle2 are in garbagecan1. saltshaker1 and creditcard3 are on shelf3. vase2 is on shelf1. spatula1 is in drawer3. peppershaker1 is in cabinet1. pot1 is on stoveburner4. pan1 is on stoveburner3. cabinet5, drawer2, cabinet1, fridge1, drawer1, drawer3, cabinet2, and microwave1 are closed. Nothing has been validated. agent1 is holding object pan1. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a goes to receptacle ?r from the current location ?lstart to the next location ?lend, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r that is at location ?l, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r that is at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from a not openable receptacle ?r while at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a grasps object ?o from an openable receptacle ?r while at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot on a not openable receptacle ?r with type ?rt at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot in an openable receptacle ?r with type ?rt at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans a cleanable object ?o in a sink ?r at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a warms up a heatable object ?o with a receptacle ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a chills a coolable object ?o in receptacle ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a toggles object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co with a knife ?ko at location ?l, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - check that the togglable object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is hot and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is chilled and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - validate that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(put_object_on_not_openable_receptacle agent1 location29 spatula3 drawer3 tissueboxtype toiletpaperhangertype)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190906_191445_723170)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location20 location21 location22 location23 location24 location25 location26 location27 location28 location29 location3 location30 location31 location32 location4 location5 location6 location7 location8 location9 - location alarmclock apple apple1 apple2 apple3 baseballbat basketball bathtub blinds book boots bowl bowl1 box bread bread1 butterknife butterknife1 butterknife2 candle cd cellphone cellphone1 cellphone2 cellphone3 chair chair1 chair2 cloth creditcard creditcard1 creditcard2 creditcard3 cup cup1 cup2 cup3 curtains desklamp dishsponge dishsponge1 dishsponge2 dishsponge3 egg egg1 egg2 faucet1 floorlamp footstool fork fork1 fork2 glassbottle glassbottle1 glassbottle2 handtowel houseplant houseplant1 kettle keychain knife knife1 ladle laptop laundryhamperlid lettuce lettuce1 lightswitch lightswitch1 mirror mug mug1 mug2 newspaper painting pan pan1 papertowel papertowelroll papertowelroll1 pen pencil peppershaker peppershaker1 peppershaker2 pillow plate plate2 plunger poster pot pot1 potato potato1 potato2 potato3 remotecontrol saltshaker saltshaker1 scrubbrush showerdoor showerglass sink sink1 soapbar soapbottle soapbottle1 soapbottle2 soapbottle3 spatula spatula1 spatula2 spatula3 spoon spoon1 spoon2 spraybottle statue statue1 statue2 stoveknob stoveknob1 stoveknob2 stoveknob3 stoveknob4 teddybear television tennisracket tissuebox toiletpaper toiletpaperroll tomato tomato1 tomato2 tomato3 towel vase vase1 vase2 watch wateringcan window window1 window2 winebottle - object plate1 - objectorreceptacle alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 cabinet5 cabinet6 coffeemachine1 countertop1 countertop2 countertop3 drawer1 drawer2 drawer3 fridge1 garbagecan1 microwave1 shelf1 shelf2 shelf3 sinkbasin1 stoveburner1 stoveburner2 stoveburner3 stoveburner4 toaster1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location4) (cancontain cabinettype bowltype) (cancontain cabinettype cuptype) (cancontain cabinettype dishspongetype) (cancontain cabinettype glassbottletype) (cancontain cabinettype mugtype) (cancontain cabinettype pantype) (cancontain cabinettype peppershakertype) (cancontain cabinettype platetype) (cancontain cabinettype pottype) (cancontain cabinettype saltshakertype) (cancontain cabinettype soapbottletype) (cancontain cabinettype vasetype) (cancontain coffeemachinetype mugtype) (cancontain countertoptype appletype) (cancontain countertoptype bowltype) (cancontain countertoptype breadtype) (cancontain countertoptype butterknifetype) (cancontain countertoptype cellphonetype) (cancontain countertoptype creditcardtype) (cancontain countertoptype cuptype) (cancontain countertoptype dishspongetype) (cancontain countertoptype eggtype) (cancontain countertoptype forktype) (cancontain countertoptype glassbottletype) (cancontain countertoptype knifetype) (cancontain countertoptype lettucetype) (cancontain countertoptype mugtype) (cancontain countertoptype pantype) (cancontain countertoptype peppershakertype) (cancontain countertoptype platetype) (cancontain countertoptype potatotype) (cancontain countertoptype pottype) (cancontain countertoptype saltshakertype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spatulatype) (cancontain countertoptype spoontype) (cancontain countertoptype statuetype) (cancontain countertoptype tomatotype) (cancontain countertoptype vasetype) (cancontain drawertype butterknifetype) (cancontain drawertype cellphonetype) (cancontain drawertype creditcardtype) (cancontain drawertype dishspongetype) (cancontain drawertype forktype) (cancontain drawertype knifetype) (cancontain drawertype peppershakertype) (cancontain drawertype saltshakertype) (cancontain drawertype soapbottletype) (cancontain drawertype spatulatype) (cancontain drawertype spoontype) (cancontain fridgetype appletype) (cancontain fridgetype bowltype) (cancontain fridgetype breadtype) (cancontain fridgetype cuptype) (cancontain fridgetype eggtype) (cancontain fridgetype glassbottletype) (cancontain fridgetype lettucetype) (cancontain fridgetype mugtype) (cancontain fridgetype pantype) (cancontain fridgetype platetype) (cancontain fridgetype potatotype) (cancontain fridgetype pottype) (cancontain fridgetype tomatotype) (cancontain garbagecantype appletype) (cancontain garbagecantype breadtype) (cancontain garbagecantype dishspongetype) (cancontain garbagecantype eggtype) (cancontain garbagecantype lettucetype) (cancontain garbagecantype potatotype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype tomatotype) (cancontain microwavetype appletype) (cancontain microwavetype bowltype) (cancontain microwavetype breadtype) (cancontain microwavetype cuptype) (cancontain microwavetype eggtype) (cancontain microwavetype glassbottletype) (cancontain microwavetype mugtype) (cancontain microwavetype platetype) (cancontain microwavetype potatotype) (cancontain microwavetype tomatotype) (cancontain shelftype bowltype) (cancontain shelftype cellphonetype) (cancontain shelftype creditcardtype) (cancontain shelftype cuptype) (cancontain shelftype dishspongetype) (cancontain shelftype glassbottletype) (cancontain shelftype mugtype) (cancontain shelftype peppershakertype) (cancontain shelftype platetype) (cancontain shelftype pottype) (cancontain shelftype saltshakertype) (cancontain shelftype soapbottletype) (cancontain shelftype statuetype) (cancontain shelftype vasetype) (cancontain sinkbasintype appletype) (cancontain sinkbasintype bowltype) (cancontain sinkbasintype butterknifetype) (cancontain sinkbasintype cuptype) (cancontain sinkbasintype dishspongetype) (cancontain sinkbasintype eggtype) (cancontain sinkbasintype forktype) (cancontain sinkbasintype glassbottletype) (cancontain sinkbasintype knifetype) (cancontain sinkbasintype lettucetype) (cancontain sinkbasintype mugtype) (cancontain sinkbasintype pantype) (cancontain sinkbasintype platetype) (cancontain sinkbasintype potatotype) (cancontain sinkbasintype pottype) (cancontain sinkbasintype spatulatype) (cancontain sinkbasintype spoontype) (cancontain sinkbasintype tomatotype) (cancontain stoveburnertype pantype) (cancontain stoveburnertype pottype) (cleanable apple1) (cleanable apple2) (cleanable apple3) (cleanable bowl1) (cleanable butterknife1) (cleanable butterknife2) (cleanable cup1) (cleanable cup2) (cleanable cup3) (cleanable dishsponge1) (cleanable dishsponge2) (cleanable dishsponge3) (cleanable egg1) (cleanable egg2) (cleanable fork1) (cleanable fork2) (cleanable knife1) (cleanable lettuce1) (cleanable mug1) (cleanable mug2) (cleanable pan1) (cleanable plate1) (cleanable plate2) (cleanable pot1) (cleanable potato1) (cleanable potato2) (cleanable potato3) (cleanable spatula1) (cleanable spatula2) (cleanable spatula3) (cleanable spoon1) (cleanable spoon2) (cleanable tomato1) (cleanable tomato2) (cleanable tomato3) (closed cabinet1) (closed cabinet2) (closed cabinet5) (closed drawer1) (closed drawer2) (closed drawer3) (closed fridge1) (closed microwave1) (coolable apple1) (coolable apple2) (coolable apple3) (coolable bowl1) (coolable bread1) (coolable cup1) (coolable cup2) (coolable cup3) (coolable egg1) (coolable egg2) (coolable lettuce1) (coolable mug1) (coolable mug2) (coolable pan1) (coolable plate1) (coolable plate2) (coolable pot1) (coolable potato1) (coolable potato2) (coolable potato3) (coolable tomato1) (coolable tomato2) (coolable tomato3) (heatable apple1) (heatable apple2) (heatable apple3) (heatable bread1) (heatable cup1) (heatable cup2) (heatable cup3) (heatable egg1) (heatable egg2) (heatable mug1) (heatable mug2) (heatable plate1) (heatable plate2) (heatable potato1) (heatable potato2) (heatable potato3) (heatable tomato1) (heatable tomato2) (heatable tomato3) (holds agent1 pan1) (inreceptacle apple1 countertop1) (inreceptacle apple2 countertop1) (inreceptacle apple3 countertop1) (inreceptacle bowl1 shelf2) (inreceptacle bread1 countertop1) (inreceptacle butterknife1 countertop3) (inreceptacle butterknife2 countertop3) (inreceptacle cellphone1 countertop1) (inreceptacle cellphone2 drawer1) (inreceptacle cellphone3 countertop3) (inreceptacle creditcard1 countertop1) (inreceptacle creditcard2 shelf2) (inreceptacle creditcard3 shelf3) (inreceptacle cup1 fridge1) (inreceptacle cup2 fridge1) (inreceptacle cup3 sinkbasin1) (inreceptacle dishsponge1 drawer2) (inreceptacle dishsponge2 cabinet5) (inreceptacle dishsponge2 plate1) (inreceptacle dishsponge3 countertop1) (inreceptacle egg1 fridge1) (inreceptacle egg2 sinkbasin1) (inreceptacle fork1 sinkbasin1) (inreceptacle fork2 countertop3) (inreceptacle glassbottle1 garbagecan1) (inreceptacle glassbottle2 shelf2) (inreceptacle houseplant1 countertop3) (inreceptacle knife1 countertop3) (inreceptacle lettuce1 fridge1) (inreceptacle mug1 microwave1) (inreceptacle mug2 shelf2) (inreceptacle pan1 stoveburner3) (inreceptacle papertowelroll1 garbagecan1) (inreceptacle peppershaker1 cabinet1) (inreceptacle peppershaker2 countertop3) (inreceptacle plate1 cabinet5) (inreceptacle plate2 cabinet5) (inreceptacle pot1 stoveburner2) (inreceptacle pot1 stoveburner4) (inreceptacle potato1 fridge1) (inreceptacle potato2 fridge1) (inreceptacle potato3 sinkbasin1) (inreceptacle saltshaker1 shelf3) (inreceptacle soapbottle1 cabinet6) (inreceptacle soapbottle2 garbagecan1) (inreceptacle soapbottle3 countertop3) (inreceptacle spatula1 drawer3) (inreceptacle spatula2 countertop1) (inreceptacle spatula3 countertop3) (inreceptacle spoon1 drawer2) (inreceptacle spoon2 countertop3) (inreceptacle statue1 countertop1) (inreceptacle statue2 countertop3) (inreceptacle tomato1 fridge1) (inreceptacle tomato2 fridge1) (inreceptacle tomato3 sinkbasin1) (inreceptacle vase1 cabinet6) (inreceptacle vase2 shelf1) (notopenable cabinet3) (notopenable cabinet4) (notopenable cabinet6) (notopenable coffeemachine1) (notopenable countertop1) (notopenable countertop2) (notopenable countertop3) (notopenable garbagecan1) (notopenable shelf1) (notopenable shelf2) (notopenable shelf3) (notopenable sinkbasin1) (notopenable stoveburner1) (notopenable stoveburner2) (notopenable stoveburner3) (notopenable stoveburner4) (notopenable toaster1) (notvalidated) (objectatlocation apple1 location3) (objectatlocation apple2 location3) (objectatlocation apple3 location3) (objectatlocation bowl1 location30) (objectatlocation bread1 location3) (objectatlocation butterknife1 location4) (objectatlocation butterknife2 location4) (objectatlocation cellphone1 location3) (objectatlocation cellphone2 location21) (objectatlocation cellphone3 location4) (objectatlocation chair1 location24) (objectatlocation chair2 location1) (objectatlocation creditcard1 location3) (objectatlocation creditcard2 location30) (objectatlocation creditcard3 location27) (objectatlocation cup1 location11) (objectatlocation cup2 location11) (objectatlocation cup3 location7) (objectatlocation dishsponge1 location16) (objectatlocation dishsponge2 location2) (objectatlocation dishsponge3 location3) (objectatlocation egg1 location11) (objectatlocation egg2 location7) (objectatlocation fork1 location7) (objectatlocation fork2 location4) (objectatlocation glassbottle1 location9) (objectatlocation glassbottle2 location30) (objectatlocation houseplant1 location4) (objectatlocation knife1 location4) (objectatlocation lettuce1 location11) (objectatlocation lightswitch1 location26) (objectatlocation mug1 location25) (objectatlocation mug2 location30) (objectatlocation papertowelroll1 location9) (objectatlocation peppershaker1 location20) (objectatlocation peppershaker2 location4) (objectatlocation plate1 location14) (objectatlocation plate2 location14) (objectatlocation pot1 location22) (objectatlocation potato1 location11) (objectatlocation potato2 location11) (objectatlocation potato3 location7) (objectatlocation saltshaker1 location27) (objectatlocation sink1 location7) (objectatlocation soapbottle1 location5) (objectatlocation soapbottle2 location9) (objectatlocation soapbottle3 location4) (objectatlocation spatula1 location28) (objectatlocation spatula2 location3) (objectatlocation spatula3 location4) (objectatlocation spoon1 location16) (objectatlocation spoon2 location4) (objectatlocation statue1 location3) (objectatlocation statue2 location4) (objectatlocation stoveknob1 location19) (objectatlocation stoveknob2 location13) (objectatlocation stoveknob3 location13) (objectatlocation stoveknob4 location8) (objectatlocation tomato1 location11) (objectatlocation tomato2 location11) (objectatlocation tomato3 location7) (objectatlocation vase1 location5) (objectatlocation vase2 location18) (objectatlocation window1 location31) (objectatlocation window2 location29) (objecttype apple1 appletype) (objecttype apple2 appletype) (objecttype apple3 appletype) (objecttype bowl1 bowltype) (objecttype bread1 breadtype) (objecttype butterknife1 butterknifetype) (objecttype butterknife2 butterknifetype) (objecttype cellphone1 cellphonetype) (objecttype cellphone2 cellphonetype) (objecttype cellphone3 cellphonetype) (objecttype chair1 chairtype) (objecttype chair2 chairtype) (objecttype creditcard1 creditcardtype) (objecttype creditcard2 creditcardtype) (objecttype creditcard3 creditcardtype) (objecttype cup1 cuptype) (objecttype cup2 cuptype) (objecttype cup3 cuptype) (objecttype dishsponge1 dishspongetype) (objecttype dishsponge2 dishspongetype) (objecttype dishsponge3 dishspongetype) (objecttype egg1 eggtype) (objecttype egg2 eggtype) (objecttype fork1 forktype) (objecttype fork2 forktype) (objecttype glassbottle1 glassbottletype) (objecttype glassbottle2 glassbottletype) (objecttype houseplant1 houseplanttype) (objecttype knife1 knifetype) (objecttype lettuce1 lettucetype) (objecttype lightswitch1 lightswitchtype) (objecttype mug1 mugtype) (objecttype mug2 mugtype) (objecttype pan1 pantype) (objecttype papertowelroll1 papertowelrolltype) (objecttype peppershaker1 peppershakertype) (objecttype peppershaker2 peppershakertype) (objecttype plate1 platetype) (objecttype plate2 platetype) (objecttype pot1 pottype) (objecttype potato1 potatotype) (objecttype potato2 potatotype) (objecttype potato3 potatotype) (objecttype saltshaker1 saltshakertype) (objecttype sink1 sinktype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype soapbottle3 soapbottletype) (objecttype spatula1 spatulatype) (objecttype spatula2 spatulatype) (objecttype spatula3 spatulatype) (objecttype spoon1 spoontype) (objecttype spoon2 spoontype) (objecttype statue1 statuetype) (objecttype statue2 statuetype) (objecttype stoveknob1 stoveknobtype) (objecttype stoveknob2 stoveknobtype) (objecttype stoveknob3 stoveknobtype) (objecttype stoveknob4 stoveknobtype) (objecttype tomato1 tomatotype) (objecttype tomato2 tomatotype) (objecttype tomato3 tomatotype) (objecttype vase1 vasetype) (objecttype vase2 vasetype) (objecttype window1 windowtype) (objecttype window2 windowtype) (openable cabinet1) (openable cabinet2) (openable cabinet5) (openable drawer1) (openable drawer2) (openable drawer3) (openable fridge1) (openable microwave1) (pickupable apple1) (pickupable apple2) (pickupable apple3) (pickupable bowl1) (pickupable bread1) (pickupable butterknife1) (pickupable butterknife2) (pickupable cellphone1) (pickupable cellphone2) (pickupable cellphone3) (pickupable creditcard1) (pickupable creditcard2) (pickupable creditcard3) (pickupable cup1) (pickupable cup2) (pickupable cup3) (pickupable dishsponge1) (pickupable dishsponge2) (pickupable dishsponge3) (pickupable egg1) (pickupable egg2) (pickupable fork1) (pickupable fork2) (pickupable glassbottle1) (pickupable glassbottle2) (pickupable knife1) (pickupable lettuce1) (pickupable mug1) (pickupable mug2) (pickupable pan1) (pickupable papertowelroll1) (pickupable peppershaker1) (pickupable peppershaker2) (pickupable plate1) (pickupable plate2) (pickupable pot1) (pickupable potato1) (pickupable potato2) (pickupable potato3) (pickupable saltshaker1) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable soapbottle3) (pickupable spatula1) (pickupable spatula2) (pickupable spatula3) (pickupable spoon1) (pickupable spoon2) (pickupable statue1) (pickupable statue2) (pickupable tomato1) (pickupable tomato2) (pickupable tomato3) (pickupable vase1) (pickupable vase2) (receptacleatlocation cabinet1 location20) (receptacleatlocation cabinet2 location23) (receptacleatlocation cabinet3 location17) (receptacleatlocation cabinet4 location15) (receptacleatlocation cabinet5 location14) (receptacleatlocation cabinet6 location5) (receptacleatlocation coffeemachine1 location12) (receptacleatlocation countertop1 location3) (receptacleatlocation countertop2 location12) (receptacleatlocation countertop3 location4) (receptacleatlocation drawer1 location21) (receptacleatlocation drawer2 location16) (receptacleatlocation drawer3 location28) (receptacleatlocation fridge1 location11) (receptacleatlocation garbagecan1 location9) (receptacleatlocation microwave1 location25) (receptacleatlocation shelf1 location18) (receptacleatlocation shelf2 location30) (receptacleatlocation shelf3 location27) (receptacleatlocation sinkbasin1 location7) (receptacleatlocation stoveburner1 location6) (receptacleatlocation stoveburner2 location22) (receptacleatlocation stoveburner3 location6) (receptacleatlocation stoveburner4 location22) (receptacleatlocation toaster1 location10) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype cabinet5 cabinettype) (receptacletype cabinet6 cabinettype) (receptacletype coffeemachine1 coffeemachinetype) (receptacletype countertop1 countertoptype) (receptacletype countertop2 countertoptype) (receptacletype countertop3 countertoptype) (receptacletype drawer1 drawertype) (receptacletype drawer2 drawertype) (receptacletype drawer3 drawertype) (receptacletype fridge1 fridgetype) (receptacletype garbagecan1 garbagecantype) (receptacletype microwave1 microwavetype) (receptacletype shelf1 shelftype) (receptacletype shelf2 shelftype) (receptacletype shelf3 shelftype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype stoveburner1 stoveburnertype) (receptacletype stoveburner2 stoveburnertype) (receptacletype stoveburner3 stoveburnertype) (receptacletype stoveburner4 stoveburnertype) (receptacletype toaster1 toastertype) (sliceable apple1) (sliceable apple2) (sliceable apple3) (sliceable bread1) (sliceable egg1) (sliceable egg2) (sliceable lettuce1) (sliceable potato1) (sliceable potato2) (sliceable potato3) (sliceable tomato1) (sliceable tomato2) (sliceable tomato3))\n    (:goal (validatepickandplace saltshakertype cabinettype))\n)"}
{"id": -3974012751578268555, "group": "reachable_action_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. toaster1 is at location9. coffeemachine1 and countertop2 are at location11. stoveburner1 and stoveburner3 are at location5. shelf2 is at location29. countertop3 is at location3. countertop1 is at location2. garbagecan1 is at location8. drawer3 is at location27. fridge1 is at location10. shelf3 is at location26. shelf1 is at location17. stoveburner2 and stoveburner4 are at location21. cabinet5 is at location13. microwave1 is at location24. drawer1 is at location20. cabinet1 is at location19. cabinet4 is at location14. cabinet2 is at location22. cabinet3 is at location16. drawer2 is at location15. cabinet6 is at location4. sinkbasin1 is at location6. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. egg2, potato1, bowl1, tomato1, plate2, egg1, and mug1 are at location10. plate3, butterknife1, creditcard1, bread1, knife2, spoon2, houseplant1, spatula1, cellphone3, papertowelroll1, and glassbottle1 are at location3. lettuce2, cellphone2, lettuce1, apple1, cellphone1, plate1, peppershaker1, and knife1 are at location2. apple2, potato3, and glassbottle3 are at location8. vase2 is at location17. pot1 is at location5. stoveknob1 is at location18. stoveknob4 is at location7. dishsponge1 and fork1 are at location27. peppershaker3 is at location16. stoveknob3 and stoveknob2 are at location12. bowl2 is at location29. cup1 is at location24. window2 is at location28. soapbottle2 and statue1 are at location26. spatula2, glassbottle2, spatula3, sink1, and potato2 are at location6. lightswitch1 is at location25. chair1 is at location23. chair2 is at location1. peppershaker2 is at location20. saltshaker1 is at location15. pan1 and spoon1 are at location11. soapbottle1 is at location4. vase1 and dishsponge2 are at location13. window1 is at location30. agent agent1 is at location location5. The objects are in/on receptacle as follows. cellphone2 is on plate1. spatula3, glassbottle2, spatula2, and potato2 are in sinkbasin1. plate3, cellphone3, creditcard1, butterknife1, glassbottle1, bread1, houseplant1, spatula1, spoon2, papertowelroll1, and knife2 are on countertop3. apple2, glassbottle3, and potato3 are in garbagecan1. saltshaker1 is in drawer2. pan1 and spoon1 are on countertop2. peppershaker1, cellphone1, apple1, lettuce2, knife1, lettuce1, cellphone2, and plate1 are on countertop1. vase1 and dishsponge2 are in cabinet5. peppershaker3 is in cabinet3. fork1 and dishsponge1 are in drawer3. pan1 is on stoveburner2. soapbottle2 and statue1 are on shelf3. pan1 is on stoveburner4. tomato1, egg2, bowl1, egg1, plate2, mug1, and potato1 are in fridge1. pot1 is on stoveburner3. pot1 is on stoveburner1. vase2 is on shelf1. soapbottle1 is in cabinet6. cup1 is in microwave1. peppershaker2 is in drawer1. bowl2 is on shelf2. cabinet5, drawer2, cabinet1, fridge1, drawer1, drawer3, cabinet2, and microwave1 are closed. mug1 is cool. mug2 is hot. Nothing has been validated. agent1 is holding object mug2. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a navigates to the receptacle ?r from the current location ?lstart to the next location ?lend, (open_receptacle ?a ?l ?r) - agent ?a that is at location ?l opens receptacle ?r, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r that is at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a grasps object ?o from a not openable receptacle ?r while at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a collects object ?o from an openable receptacle ?r while at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o of type ?ot on a not openable receptacle ?r of type ?rt while at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o of type ?ot in an openable receptacle ?r of type ?rt that is at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans object ?o in a receptacle ?r that is at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a warms up object ?o with a microwave ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a cools down object ?o in a fridge ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a toggles object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co with a knife ?ko at location ?l, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - validate that togglable object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is warmed up and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is colded down and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - ensure that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(pickup_object_from_openable_receptacle agent1 location18 microwavetype cabinet6)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190907_171933_349922)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location20 location21 location22 location23 location24 location25 location26 location27 location28 location29 location3 location30 location31 location4 location5 location6 location7 location8 location9 - location alarmclock apple apple1 apple2 baseballbat basketball bathtub blinds book boots bowl bowl1 bowl2 box bread bread1 butterknife butterknife1 candle cd cellphone cellphone1 cellphone2 cellphone3 chair chair1 chair2 cloth creditcard creditcard1 cup cup1 curtains desklamp dishsponge dishsponge1 dishsponge2 egg egg1 egg2 faucet1 floorlamp footstool fork fork1 glassbottle glassbottle1 glassbottle2 glassbottle3 handtowel houseplant houseplant1 kettle keychain knife knife1 knife2 ladle laptop laundryhamperlid lettuce lettuce1 lettuce2 lightswitch lightswitch1 mirror mug mug1 mug2 newspaper painting pan pan1 papertowel papertowelroll papertowelroll1 pen pencil peppershaker peppershaker1 peppershaker2 peppershaker3 pillow plate plate2 plate3 plunger poster pot pot1 potato potato1 potato2 potato3 remotecontrol saltshaker saltshaker1 scrubbrush showerdoor showerglass sink sink1 soapbar soapbottle soapbottle1 soapbottle2 spatula spatula1 spatula2 spatula3 spoon spoon1 spoon2 spraybottle statue statue1 stoveknob stoveknob1 stoveknob2 stoveknob3 stoveknob4 teddybear television tennisracket tissuebox toiletpaper toiletpaperroll tomato tomato1 towel vase vase1 vase2 watch wateringcan window window1 window2 winebottle - object plate1 - objectorreceptacle alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 cabinet5 cabinet6 coffeemachine1 countertop1 countertop2 countertop3 drawer1 drawer2 drawer3 fridge1 garbagecan1 microwave1 shelf1 shelf2 shelf3 sinkbasin1 stoveburner1 stoveburner2 stoveburner3 stoveburner4 toaster1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location5) (cancontain cabinettype bowltype) (cancontain cabinettype cuptype) (cancontain cabinettype dishspongetype) (cancontain cabinettype glassbottletype) (cancontain cabinettype mugtype) (cancontain cabinettype pantype) (cancontain cabinettype peppershakertype) (cancontain cabinettype platetype) (cancontain cabinettype pottype) (cancontain cabinettype saltshakertype) (cancontain cabinettype soapbottletype) (cancontain cabinettype vasetype) (cancontain coffeemachinetype mugtype) (cancontain countertoptype appletype) (cancontain countertoptype bowltype) (cancontain countertoptype breadtype) (cancontain countertoptype butterknifetype) (cancontain countertoptype cellphonetype) (cancontain countertoptype creditcardtype) (cancontain countertoptype cuptype) (cancontain countertoptype dishspongetype) (cancontain countertoptype eggtype) (cancontain countertoptype forktype) (cancontain countertoptype glassbottletype) (cancontain countertoptype knifetype) (cancontain countertoptype lettucetype) (cancontain countertoptype mugtype) (cancontain countertoptype pantype) (cancontain countertoptype peppershakertype) (cancontain countertoptype platetype) (cancontain countertoptype potatotype) (cancontain countertoptype pottype) (cancontain countertoptype saltshakertype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spatulatype) (cancontain countertoptype spoontype) (cancontain countertoptype statuetype) (cancontain countertoptype tomatotype) (cancontain countertoptype vasetype) (cancontain drawertype butterknifetype) (cancontain drawertype cellphonetype) (cancontain drawertype creditcardtype) (cancontain drawertype dishspongetype) (cancontain drawertype forktype) (cancontain drawertype knifetype) (cancontain drawertype peppershakertype) (cancontain drawertype saltshakertype) (cancontain drawertype soapbottletype) (cancontain drawertype spatulatype) (cancontain drawertype spoontype) (cancontain fridgetype appletype) (cancontain fridgetype bowltype) (cancontain fridgetype breadtype) (cancontain fridgetype cuptype) (cancontain fridgetype eggtype) (cancontain fridgetype glassbottletype) (cancontain fridgetype lettucetype) (cancontain fridgetype mugtype) (cancontain fridgetype pantype) (cancontain fridgetype platetype) (cancontain fridgetype potatotype) (cancontain fridgetype pottype) (cancontain fridgetype tomatotype) (cancontain garbagecantype appletype) (cancontain garbagecantype breadtype) (cancontain garbagecantype dishspongetype) (cancontain garbagecantype eggtype) (cancontain garbagecantype lettucetype) (cancontain garbagecantype potatotype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype tomatotype) (cancontain microwavetype appletype) (cancontain microwavetype bowltype) (cancontain microwavetype breadtype) (cancontain microwavetype cuptype) (cancontain microwavetype eggtype) (cancontain microwavetype glassbottletype) (cancontain microwavetype mugtype) (cancontain microwavetype platetype) (cancontain microwavetype potatotype) (cancontain microwavetype tomatotype) (cancontain shelftype bowltype) (cancontain shelftype cellphonetype) (cancontain shelftype creditcardtype) (cancontain shelftype cuptype) (cancontain shelftype dishspongetype) (cancontain shelftype glassbottletype) (cancontain shelftype mugtype) (cancontain shelftype peppershakertype) (cancontain shelftype platetype) (cancontain shelftype pottype) (cancontain shelftype saltshakertype) (cancontain shelftype soapbottletype) (cancontain shelftype statuetype) (cancontain shelftype vasetype) (cancontain sinkbasintype appletype) (cancontain sinkbasintype bowltype) (cancontain sinkbasintype butterknifetype) (cancontain sinkbasintype cuptype) (cancontain sinkbasintype dishspongetype) (cancontain sinkbasintype eggtype) (cancontain sinkbasintype forktype) (cancontain sinkbasintype glassbottletype) (cancontain sinkbasintype knifetype) (cancontain sinkbasintype lettucetype) (cancontain sinkbasintype mugtype) (cancontain sinkbasintype pantype) (cancontain sinkbasintype platetype) (cancontain sinkbasintype potatotype) (cancontain sinkbasintype pottype) (cancontain sinkbasintype spatulatype) (cancontain sinkbasintype spoontype) (cancontain sinkbasintype tomatotype) (cancontain stoveburnertype pantype) (cancontain stoveburnertype pottype) (cleanable apple1) (cleanable apple2) (cleanable bowl1) (cleanable bowl2) (cleanable butterknife1) (cleanable cup1) (cleanable dishsponge1) (cleanable dishsponge2) (cleanable egg1) (cleanable egg2) (cleanable fork1) (cleanable knife1) (cleanable knife2) (cleanable lettuce1) (cleanable lettuce2) (cleanable mug1) (cleanable mug2) (cleanable pan1) (cleanable plate1) (cleanable plate2) (cleanable plate3) (cleanable pot1) (cleanable potato1) (cleanable potato2) (cleanable potato3) (cleanable spatula1) (cleanable spatula2) (cleanable spatula3) (cleanable spoon1) (cleanable spoon2) (cleanable tomato1) (closed cabinet1) (closed cabinet2) (closed cabinet5) (closed drawer1) (closed drawer2) (closed drawer3) (closed fridge1) (closed microwave1) (coolable apple1) (coolable apple2) (coolable bowl1) (coolable bowl2) (coolable bread1) (coolable cup1) (coolable egg1) (coolable egg2) (coolable lettuce1) (coolable lettuce2) (coolable mug1) (coolable mug2) (coolable pan1) (coolable plate1) (coolable plate2) (coolable plate3) (coolable pot1) (coolable potato1) (coolable potato2) (coolable potato3) (coolable tomato1) (heatable apple1) (heatable apple2) (heatable bread1) (heatable cup1) (heatable egg1) (heatable egg2) (heatable mug1) (heatable mug2) (heatable plate1) (heatable plate2) (heatable plate3) (heatable potato1) (heatable potato2) (heatable potato3) (heatable tomato1) (holds agent1 mug2) (inreceptacle apple1 countertop1) (inreceptacle apple2 garbagecan1) (inreceptacle bowl1 fridge1) (inreceptacle bowl2 shelf2) (inreceptacle bread1 countertop3) (inreceptacle butterknife1 countertop3) (inreceptacle cellphone1 countertop1) (inreceptacle cellphone2 countertop1) (inreceptacle cellphone2 plate1) (inreceptacle cellphone3 countertop3) (inreceptacle creditcard1 countertop3) (inreceptacle cup1 microwave1) (inreceptacle dishsponge1 drawer3) (inreceptacle dishsponge2 cabinet5) (inreceptacle egg1 fridge1) (inreceptacle egg2 fridge1) (inreceptacle fork1 drawer3) (inreceptacle glassbottle1 countertop3) (inreceptacle glassbottle2 sinkbasin1) (inreceptacle glassbottle3 garbagecan1) (inreceptacle houseplant1 countertop3) (inreceptacle knife1 countertop1) (inreceptacle knife2 countertop3) (inreceptacle lettuce1 countertop1) (inreceptacle lettuce2 countertop1) (inreceptacle mug1 fridge1) (inreceptacle pan1 countertop2) (inreceptacle pan1 stoveburner2) (inreceptacle pan1 stoveburner4) (inreceptacle papertowelroll1 countertop3) (inreceptacle peppershaker1 countertop1) (inreceptacle peppershaker2 drawer1) (inreceptacle peppershaker3 cabinet3) (inreceptacle plate1 countertop1) (inreceptacle plate2 fridge1) (inreceptacle plate3 countertop3) (inreceptacle pot1 stoveburner1) (inreceptacle pot1 stoveburner3) (inreceptacle potato1 fridge1) (inreceptacle potato2 sinkbasin1) (inreceptacle potato3 garbagecan1) (inreceptacle saltshaker1 drawer2) (inreceptacle soapbottle1 cabinet6) (inreceptacle soapbottle2 shelf3) (inreceptacle spatula1 countertop3) (inreceptacle spatula2 sinkbasin1) (inreceptacle spatula3 sinkbasin1) (inreceptacle spoon1 countertop2) (inreceptacle spoon2 countertop3) (inreceptacle statue1 shelf3) (inreceptacle tomato1 fridge1) (inreceptacle vase1 cabinet5) (inreceptacle vase2 shelf1) (iscool mug1) (ishot mug2) (notopenable cabinet3) (notopenable cabinet4) (notopenable cabinet6) (notopenable coffeemachine1) (notopenable countertop1) (notopenable countertop2) (notopenable countertop3) (notopenable garbagecan1) (notopenable shelf1) (notopenable shelf2) (notopenable shelf3) (notopenable sinkbasin1) (notopenable stoveburner1) (notopenable stoveburner2) (notopenable stoveburner3) (notopenable stoveburner4) (notopenable toaster1) (notvalidated) (objectatlocation apple1 location2) (objectatlocation apple2 location8) (objectatlocation bowl1 location10) (objectatlocation bowl2 location29) (objectatlocation bread1 location3) (objectatlocation butterknife1 location3) (objectatlocation cellphone1 location2) (objectatlocation cellphone2 location2) (objectatlocation cellphone3 location3) (objectatlocation chair1 location23) (objectatlocation chair2 location1) (objectatlocation creditcard1 location3) (objectatlocation cup1 location24) (objectatlocation dishsponge1 location27) (objectatlocation dishsponge2 location13) (objectatlocation egg1 location10) (objectatlocation egg2 location10) (objectatlocation fork1 location27) (objectatlocation glassbottle1 location3) (objectatlocation glassbottle2 location6) (objectatlocation glassbottle3 location8) (objectatlocation houseplant1 location3) (objectatlocation knife1 location2) (objectatlocation knife2 location3) (objectatlocation lettuce1 location2) (objectatlocation lettuce2 location2) (objectatlocation lightswitch1 location25) (objectatlocation mug1 location10) (objectatlocation pan1 location11) (objectatlocation papertowelroll1 location3) (objectatlocation peppershaker1 location2) (objectatlocation peppershaker2 location20) (objectatlocation peppershaker3 location16) (objectatlocation plate1 location2) (objectatlocation plate2 location10) (objectatlocation plate3 location3) (objectatlocation pot1 location5) (objectatlocation potato1 location10) (objectatlocation potato2 location6) (objectatlocation potato3 location8) (objectatlocation saltshaker1 location15) (objectatlocation sink1 location6) (objectatlocation soapbottle1 location4) (objectatlocation soapbottle2 location26) (objectatlocation spatula1 location3) (objectatlocation spatula2 location6) (objectatlocation spatula3 location6) (objectatlocation spoon1 location11) (objectatlocation spoon2 location3) (objectatlocation statue1 location26) (objectatlocation stoveknob1 location18) (objectatlocation stoveknob2 location12) (objectatlocation stoveknob3 location12) (objectatlocation stoveknob4 location7) (objectatlocation tomato1 location10) (objectatlocation vase1 location13) (objectatlocation vase2 location17) (objectatlocation window1 location30) (objectatlocation window2 location28) (objecttype apple1 appletype) (objecttype apple2 appletype) (objecttype bowl1 bowltype) (objecttype bowl2 bowltype) (objecttype bread1 breadtype) (objecttype butterknife1 butterknifetype) (objecttype cellphone1 cellphonetype) (objecttype cellphone2 cellphonetype) (objecttype cellphone3 cellphonetype) (objecttype chair1 chairtype) (objecttype chair2 chairtype) (objecttype creditcard1 creditcardtype) (objecttype cup1 cuptype) (objecttype dishsponge1 dishspongetype) (objecttype dishsponge2 dishspongetype) (objecttype egg1 eggtype) (objecttype egg2 eggtype) (objecttype fork1 forktype) (objecttype glassbottle1 glassbottletype) (objecttype glassbottle2 glassbottletype) (objecttype glassbottle3 glassbottletype) (objecttype houseplant1 houseplanttype) (objecttype knife1 knifetype) (objecttype knife2 knifetype) (objecttype lettuce1 lettucetype) (objecttype lettuce2 lettucetype) (objecttype lightswitch1 lightswitchtype) (objecttype mug1 mugtype) (objecttype mug2 mugtype) (objecttype pan1 pantype) (objecttype papertowelroll1 papertowelrolltype) (objecttype peppershaker1 peppershakertype) (objecttype peppershaker2 peppershakertype) (objecttype peppershaker3 peppershakertype) (objecttype plate1 platetype) (objecttype plate2 platetype) (objecttype plate3 platetype) (objecttype pot1 pottype) (objecttype potato1 potatotype) (objecttype potato2 potatotype) (objecttype potato3 potatotype) (objecttype saltshaker1 saltshakertype) (objecttype sink1 sinktype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype spatula1 spatulatype) (objecttype spatula2 spatulatype) (objecttype spatula3 spatulatype) (objecttype spoon1 spoontype) (objecttype spoon2 spoontype) (objecttype statue1 statuetype) (objecttype stoveknob1 stoveknobtype) (objecttype stoveknob2 stoveknobtype) (objecttype stoveknob3 stoveknobtype) (objecttype stoveknob4 stoveknobtype) (objecttype tomato1 tomatotype) (objecttype vase1 vasetype) (objecttype vase2 vasetype) (objecttype window1 windowtype) (objecttype window2 windowtype) (openable cabinet1) (openable cabinet2) (openable cabinet5) (openable drawer1) (openable drawer2) (openable drawer3) (openable fridge1) (openable microwave1) (pickupable apple1) (pickupable apple2) (pickupable bowl1) (pickupable bowl2) (pickupable bread1) (pickupable butterknife1) (pickupable cellphone1) (pickupable cellphone2) (pickupable cellphone3) (pickupable creditcard1) (pickupable cup1) (pickupable dishsponge1) (pickupable dishsponge2) (pickupable egg1) (pickupable egg2) (pickupable fork1) (pickupable glassbottle1) (pickupable glassbottle2) (pickupable glassbottle3) (pickupable knife1) (pickupable knife2) (pickupable lettuce1) (pickupable lettuce2) (pickupable mug1) (pickupable mug2) (pickupable pan1) (pickupable papertowelroll1) (pickupable peppershaker1) (pickupable peppershaker2) (pickupable peppershaker3) (pickupable plate1) (pickupable plate2) (pickupable plate3) (pickupable pot1) (pickupable potato1) (pickupable potato2) (pickupable potato3) (pickupable saltshaker1) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable spatula1) (pickupable spatula2) (pickupable spatula3) (pickupable spoon1) (pickupable spoon2) (pickupable statue1) (pickupable tomato1) (pickupable vase1) (pickupable vase2) (receptacleatlocation cabinet1 location19) (receptacleatlocation cabinet2 location22) (receptacleatlocation cabinet3 location16) (receptacleatlocation cabinet4 location14) (receptacleatlocation cabinet5 location13) (receptacleatlocation cabinet6 location4) (receptacleatlocation coffeemachine1 location11) (receptacleatlocation countertop1 location2) (receptacleatlocation countertop2 location11) (receptacleatlocation countertop3 location3) (receptacleatlocation drawer1 location20) (receptacleatlocation drawer2 location15) (receptacleatlocation drawer3 location27) (receptacleatlocation fridge1 location10) (receptacleatlocation garbagecan1 location8) (receptacleatlocation microwave1 location24) (receptacleatlocation shelf1 location17) (receptacleatlocation shelf2 location29) (receptacleatlocation shelf3 location26) (receptacleatlocation sinkbasin1 location6) (receptacleatlocation stoveburner1 location5) (receptacleatlocation stoveburner2 location21) (receptacleatlocation stoveburner3 location5) (receptacleatlocation stoveburner4 location21) (receptacleatlocation toaster1 location9) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype cabinet5 cabinettype) (receptacletype cabinet6 cabinettype) (receptacletype coffeemachine1 coffeemachinetype) (receptacletype countertop1 countertoptype) (receptacletype countertop2 countertoptype) (receptacletype countertop3 countertoptype) (receptacletype drawer1 drawertype) (receptacletype drawer2 drawertype) (receptacletype drawer3 drawertype) (receptacletype fridge1 fridgetype) (receptacletype garbagecan1 garbagecantype) (receptacletype microwave1 microwavetype) (receptacletype shelf1 shelftype) (receptacletype shelf2 shelftype) (receptacletype shelf3 shelftype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype stoveburner1 stoveburnertype) (receptacletype stoveburner2 stoveburnertype) (receptacletype stoveburner3 stoveburnertype) (receptacletype stoveburner4 stoveburnertype) (receptacletype toaster1 toastertype) (sliceable apple1) (sliceable apple2) (sliceable bread1) (sliceable egg1) (sliceable egg2) (sliceable lettuce1) (sliceable lettuce2) (sliceable potato1) (sliceable potato2) (sliceable potato3) (sliceable tomato1))\n    (:goal (validateheatandplace mugtype coffeemachinetype))\n)"}
{"id": 2075288111787812167, "group": "reachable_action_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. garbagecan1 is at location2. cabinet2 is at location11. cabinet1 is at location4. toilet1 is at location7. handtowelholder1 is at location18. cabinet4 is at location15. handtowelholder2 is at location17. towelholder1 is at location5. sinkbasin2 is at location6. toiletpaperhanger1 is at location10. sinkbasin1 is at location16. cabinet3 is at location8. countertop1 is at location3. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. sink1 is at location12. candle1 and cloth1 are at location3. showerdoor1 is at location1. toiletpaper1 and soapbar1 are at location7. showerglass1 and towel1 are at location5. handtowel1 is at location18. handtowel2 is at location17. lightswitch1 is at location13. cloth2 is at location11. plunger1 and scrubbrush1 are at location10. soapbottle2 and spraybottle1 are at location4. mirror1 is at location9. sink2 is at location14. spraybottle2 and soapbottle1 are at location15. toiletpaper2 is at location8. spraybottle3 is at location2. agent agent1 is at location location10. The objects are in/on receptacle as follows. cloth1 and candle1 are on countertop1. spraybottle2 and soapbottle1 are in cabinet4. towel1 is on towelholder1. toiletpaper2 is in cabinet3. cloth2 is in cabinet2. handtowel2 is on handtowelholder2. handtowel1 is on handtowelholder1. spraybottle3 is in garbagecan1. soapbar1 and toiletpaper1 are in toilet1. spraybottle1 and soapbottle2 are in cabinet1. cabinet2, cabinet3, cabinet1, and cabinet4 are closed. Nothing has been validated. agent1 is holding object soapbar2. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a moves from the current position ?lstart to the next position ?lend that has the receptacle ?r, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r that is at location ?l, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r that is at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a collects object ?o from a not openable receptacle ?r that is at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from an openable receptacle ?r that is at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o of type ?ot on a not openable receptacle ?r of type ?rt that is at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o with type ?ot in an openable receptacle ?r with type ?rt at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans a cleanable object ?o in a sink ?r at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a heats up object ?o with a microwave ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a chills object ?o in a fridge ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a turns off object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a toggles object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co with a knife ?ko at location ?l, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - validate that togglable object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is warmed up and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is chilled and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - check that the two objects ?o1 and ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(clean_object agent1 location1 toilet1 knifetype)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190908_214946_567644)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location3 location4 location5 location6 location7 location8 location9 - location alarmclock apple baseballbat basketball bathtub blinds book boots bowl box bread butterknife candle candle1 cd cellphone chair cloth cloth1 cloth2 creditcard cup curtains desklamp dishsponge egg faucet1 faucet2 faucet3 floorlamp footstool fork glassbottle handtowel handtowel1 handtowel2 houseplant kettle keychain knife ladle laptop laundryhamperlid lettuce lightswitch lightswitch1 mirror mirror1 mug newspaper painting pan papertowel papertowelroll pen pencil peppershaker pillow plate plunger plunger1 poster pot potato remotecontrol saltshaker scrubbrush scrubbrush1 showerdoor showerdoor1 showerglass showerglass1 sink sink1 sink2 soapbar soapbar1 soapbar2 soapbottle soapbottle1 soapbottle2 spatula spoon spraybottle spraybottle1 spraybottle2 spraybottle3 statue stoveknob teddybear television tennisracket tissuebox toiletpaper toiletpaper1 toiletpaper2 toiletpaperroll tomato towel towel1 vase watch wateringcan window winebottle - object alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 countertop1 garbagecan1 handtowelholder1 handtowelholder2 sinkbasin1 sinkbasin2 toilet1 toiletpaperhanger1 towelholder1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location10) (cancontain cabinettype candletype) (cancontain cabinettype clothtype) (cancontain cabinettype handtoweltype) (cancontain cabinettype plungertype) (cancontain cabinettype soapbartype) (cancontain cabinettype soapbottletype) (cancontain cabinettype spraybottletype) (cancontain cabinettype toiletpapertype) (cancontain countertoptype candletype) (cancontain countertoptype clothtype) (cancontain countertoptype handtoweltype) (cancontain countertoptype soapbartype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spraybottletype) (cancontain countertoptype toiletpapertype) (cancontain garbagecantype clothtype) (cancontain garbagecantype handtoweltype) (cancontain garbagecantype soapbartype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype spraybottletype) (cancontain garbagecantype toiletpapertype) (cancontain handtowelholdertype handtoweltype) (cancontain sinkbasintype clothtype) (cancontain sinkbasintype handtoweltype) (cancontain sinkbasintype soapbartype) (cancontain toiletpaperhangertype toiletpapertype) (cancontain toilettype candletype) (cancontain toilettype clothtype) (cancontain toilettype handtoweltype) (cancontain toilettype soapbartype) (cancontain toilettype soapbottletype) (cancontain toilettype spraybottletype) (cancontain toilettype toiletpapertype) (cancontain towelholdertype toweltype) (cleanable cloth1) (cleanable cloth2) (cleanable soapbar1) (cleanable soapbar2) (closed cabinet1) (closed cabinet2) (closed cabinet3) (closed cabinet4) (holds agent1 soapbar2) (inreceptacle candle1 countertop1) (inreceptacle cloth1 countertop1) (inreceptacle cloth2 cabinet2) (inreceptacle handtowel1 handtowelholder1) (inreceptacle handtowel2 handtowelholder2) (inreceptacle soapbar1 toilet1) (inreceptacle soapbottle1 cabinet4) (inreceptacle soapbottle2 cabinet1) (inreceptacle spraybottle1 cabinet1) (inreceptacle spraybottle2 cabinet4) (inreceptacle spraybottle3 garbagecan1) (inreceptacle toiletpaper1 toilet1) (inreceptacle toiletpaper2 cabinet3) (inreceptacle towel1 towelholder1) (notopenable countertop1) (notopenable garbagecan1) (notopenable handtowelholder1) (notopenable handtowelholder2) (notopenable sinkbasin1) (notopenable sinkbasin2) (notopenable toilet1) (notopenable toiletpaperhanger1) (notopenable towelholder1) (notvalidated) (objectatlocation candle1 location3) (objectatlocation cloth1 location3) (objectatlocation cloth2 location11) (objectatlocation handtowel1 location18) (objectatlocation handtowel2 location17) (objectatlocation lightswitch1 location13) (objectatlocation mirror1 location9) (objectatlocation plunger1 location10) (objectatlocation scrubbrush1 location10) (objectatlocation showerdoor1 location1) (objectatlocation showerglass1 location5) (objectatlocation sink1 location12) (objectatlocation sink2 location14) (objectatlocation soapbar1 location7) (objectatlocation soapbottle1 location15) (objectatlocation soapbottle2 location4) (objectatlocation spraybottle1 location4) (objectatlocation spraybottle2 location15) (objectatlocation spraybottle3 location2) (objectatlocation toiletpaper1 location7) (objectatlocation toiletpaper2 location8) (objectatlocation towel1 location5) (objecttype candle1 candletype) (objecttype cloth1 clothtype) (objecttype cloth2 clothtype) (objecttype handtowel1 handtoweltype) (objecttype handtowel2 handtoweltype) (objecttype lightswitch1 lightswitchtype) (objecttype mirror1 mirrortype) (objecttype plunger1 plungertype) (objecttype scrubbrush1 scrubbrushtype) (objecttype showerdoor1 showerdoortype) (objecttype showerglass1 showerglasstype) (objecttype sink1 sinktype) (objecttype sink2 sinktype) (objecttype soapbar1 soapbartype) (objecttype soapbar2 soapbartype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype spraybottle1 spraybottletype) (objecttype spraybottle2 spraybottletype) (objecttype spraybottle3 spraybottletype) (objecttype toiletpaper1 toiletpapertype) (objecttype toiletpaper2 toiletpapertype) (objecttype towel1 toweltype) (openable cabinet1) (openable cabinet2) (openable cabinet3) (openable cabinet4) (pickupable candle1) (pickupable cloth1) (pickupable cloth2) (pickupable handtowel1) (pickupable handtowel2) (pickupable plunger1) (pickupable scrubbrush1) (pickupable soapbar1) (pickupable soapbar2) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable spraybottle1) (pickupable spraybottle2) (pickupable spraybottle3) (pickupable toiletpaper1) (pickupable toiletpaper2) (pickupable towel1) (receptacleatlocation cabinet1 location4) (receptacleatlocation cabinet2 location11) (receptacleatlocation cabinet3 location8) (receptacleatlocation cabinet4 location15) (receptacleatlocation countertop1 location3) (receptacleatlocation garbagecan1 location2) (receptacleatlocation handtowelholder1 location18) (receptacleatlocation handtowelholder2 location17) (receptacleatlocation sinkbasin1 location16) (receptacleatlocation sinkbasin2 location6) (receptacleatlocation toilet1 location7) (receptacleatlocation toiletpaperhanger1 location10) (receptacleatlocation towelholder1 location5) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype countertop1 countertoptype) (receptacletype garbagecan1 garbagecantype) (receptacletype handtowelholder1 handtowelholdertype) (receptacletype handtowelholder2 handtowelholdertype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype sinkbasin2 sinkbasintype) (receptacletype toilet1 toilettype) (receptacletype toiletpaperhanger1 toiletpaperhangertype) (receptacletype towelholder1 towelholdertype))\n    (:goal (validatecleanandplace soapbartype cabinettype))\n)"}
{"id": 7213791436944509550, "group": "reachable_action_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. toaster1 is at location9. coffeemachine1 and countertop2 are at location11. stoveburner1 and stoveburner3 are at location5. shelf2 is at location29. countertop3 is at location3. countertop1 is at location2. garbagecan1 is at location8. drawer3 is at location27. fridge1 is at location10. shelf3 is at location26. shelf1 is at location17. stoveburner2 and stoveburner4 are at location21. cabinet5 is at location13. microwave1 is at location24. drawer1 is at location20. cabinet1 is at location19. cabinet4 is at location14. cabinet2 is at location22. cabinet3 is at location16. drawer2 is at location15. cabinet6 is at location4. sinkbasin1 is at location6. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. egg2, potato1, bowl1, tomato1, plate2, egg1, and mug1 are at location10. plate3, butterknife1, creditcard1, bread1, knife2, spoon2, houseplant1, spatula1, cellphone3, papertowelroll1, and glassbottle1 are at location3. lettuce2, cellphone2, lettuce1, apple1, cellphone1, plate1, peppershaker1, and knife1 are at location2. apple2, potato3, and glassbottle3 are at location8. vase2 is at location17. pot1 is at location5. stoveknob1 is at location18. stoveknob4 is at location7. dishsponge1 and fork1 are at location27. peppershaker3 is at location16. stoveknob3 and stoveknob2 are at location12. bowl2 is at location29. cup1 is at location24. window2 is at location28. soapbottle2 and statue1 are at location26. spatula2, glassbottle2, spatula3, sink1, and potato2 are at location6. lightswitch1 is at location25. chair1 is at location23. chair2 is at location1. peppershaker2 is at location20. saltshaker1 is at location15. pan1 and spoon1 are at location11. soapbottle1 is at location4. vase1 and dishsponge2 are at location13. window1 is at location30. agent agent1 is at location location24. The objects are in/on receptacle as follows. cellphone2 is on plate1. spatula3, glassbottle2, spatula2, and potato2 are in sinkbasin1. plate3, cellphone3, creditcard1, butterknife1, glassbottle1, bread1, houseplant1, spatula1, spoon2, papertowelroll1, and knife2 are on countertop3. apple2, glassbottle3, and potato3 are in garbagecan1. saltshaker1 is in drawer2. pan1 and spoon1 are on countertop2. peppershaker1, cellphone1, apple1, lettuce2, knife1, lettuce1, cellphone2, and plate1 are on countertop1. vase1 and dishsponge2 are in cabinet5. peppershaker3 is in cabinet3. fork1 and dishsponge1 are in drawer3. pan1 is on stoveburner2. soapbottle2 and statue1 are on shelf3. pan1 is on stoveburner4. tomato1, egg2, bowl1, egg1, plate2, mug1, and potato1 are in fridge1. pot1 is on stoveburner3. pot1 is on stoveburner1. vase2 is on shelf1. soapbottle1 is in cabinet6. cup1 is in microwave1. peppershaker2 is in drawer1. bowl2 is on shelf2. cabinet5, drawer2, cabinet1, fridge1, drawer1, drawer3, cabinet2, and microwave1 are closed. mug1 is cool. mug2 is hot. Nothing has been validated. agent1 is holding object mug2. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a navigates from the current position ?lstart to the next position ?lend that has a receptacle ?r, (open_receptacle ?a ?l ?r) - agent ?a opens receptacle ?r while at location ?l, (close_receptacle ?a ?l ?r) - agent ?a that is at location ?l closes receptacle ?r, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a collects object ?o from a not openable receptacle ?r while at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from an openable receptacle ?r that is at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o of type ?ot on a not openable receptacle ?r of type ?rt that is at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o with type ?ot in an openable receptacle ?r with type ?rt at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans a cleanable object ?o in a sink ?r at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a heats up object ?o with a microwave ?r that is in location ?l, (cool_object ?a ?l ?r ?o) - agent ?a chills object ?o in a fridge ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a turns off object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a toggles a togglable object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts a sliceable object ?co that is at location ?l with a knife ?ko, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - validate that togglable object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is hot and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is cool and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - ensure that the two objects ?o1 and object ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(validate_clean_and_place_in_receptacle location3 televisiontype coffeemachine1 towelholdertype)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190907_171933_349922)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location20 location21 location22 location23 location24 location25 location26 location27 location28 location29 location3 location30 location31 location4 location5 location6 location7 location8 location9 - location alarmclock apple apple1 apple2 baseballbat basketball bathtub blinds book boots bowl bowl1 bowl2 box bread bread1 butterknife butterknife1 candle cd cellphone cellphone1 cellphone2 cellphone3 chair chair1 chair2 cloth creditcard creditcard1 cup cup1 curtains desklamp dishsponge dishsponge1 dishsponge2 egg egg1 egg2 faucet1 floorlamp footstool fork fork1 glassbottle glassbottle1 glassbottle2 glassbottle3 handtowel houseplant houseplant1 kettle keychain knife knife1 knife2 ladle laptop laundryhamperlid lettuce lettuce1 lettuce2 lightswitch lightswitch1 mirror mug mug1 mug2 newspaper painting pan pan1 papertowel papertowelroll papertowelroll1 pen pencil peppershaker peppershaker1 peppershaker2 peppershaker3 pillow plate plate2 plate3 plunger poster pot pot1 potato potato1 potato2 potato3 remotecontrol saltshaker saltshaker1 scrubbrush showerdoor showerglass sink sink1 soapbar soapbottle soapbottle1 soapbottle2 spatula spatula1 spatula2 spatula3 spoon spoon1 spoon2 spraybottle statue statue1 stoveknob stoveknob1 stoveknob2 stoveknob3 stoveknob4 teddybear television tennisracket tissuebox toiletpaper toiletpaperroll tomato tomato1 towel vase vase1 vase2 watch wateringcan window window1 window2 winebottle - object plate1 - objectorreceptacle alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype cabinet1 cabinet2 cabinet3 cabinet4 cabinet5 cabinet6 coffeemachine1 countertop1 countertop2 countertop3 drawer1 drawer2 drawer3 fridge1 garbagecan1 microwave1 shelf1 shelf2 shelf3 sinkbasin1 stoveburner1 stoveburner2 stoveburner3 stoveburner4 toaster1 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location24) (cancontain cabinettype bowltype) (cancontain cabinettype cuptype) (cancontain cabinettype dishspongetype) (cancontain cabinettype glassbottletype) (cancontain cabinettype mugtype) (cancontain cabinettype pantype) (cancontain cabinettype peppershakertype) (cancontain cabinettype platetype) (cancontain cabinettype pottype) (cancontain cabinettype saltshakertype) (cancontain cabinettype soapbottletype) (cancontain cabinettype vasetype) (cancontain coffeemachinetype mugtype) (cancontain countertoptype appletype) (cancontain countertoptype bowltype) (cancontain countertoptype breadtype) (cancontain countertoptype butterknifetype) (cancontain countertoptype cellphonetype) (cancontain countertoptype creditcardtype) (cancontain countertoptype cuptype) (cancontain countertoptype dishspongetype) (cancontain countertoptype eggtype) (cancontain countertoptype forktype) (cancontain countertoptype glassbottletype) (cancontain countertoptype knifetype) (cancontain countertoptype lettucetype) (cancontain countertoptype mugtype) (cancontain countertoptype pantype) (cancontain countertoptype peppershakertype) (cancontain countertoptype platetype) (cancontain countertoptype potatotype) (cancontain countertoptype pottype) (cancontain countertoptype saltshakertype) (cancontain countertoptype soapbottletype) (cancontain countertoptype spatulatype) (cancontain countertoptype spoontype) (cancontain countertoptype statuetype) (cancontain countertoptype tomatotype) (cancontain countertoptype vasetype) (cancontain drawertype butterknifetype) (cancontain drawertype cellphonetype) (cancontain drawertype creditcardtype) (cancontain drawertype dishspongetype) (cancontain drawertype forktype) (cancontain drawertype knifetype) (cancontain drawertype peppershakertype) (cancontain drawertype saltshakertype) (cancontain drawertype soapbottletype) (cancontain drawertype spatulatype) (cancontain drawertype spoontype) (cancontain fridgetype appletype) (cancontain fridgetype bowltype) (cancontain fridgetype breadtype) (cancontain fridgetype cuptype) (cancontain fridgetype eggtype) (cancontain fridgetype glassbottletype) (cancontain fridgetype lettucetype) (cancontain fridgetype mugtype) (cancontain fridgetype pantype) (cancontain fridgetype platetype) (cancontain fridgetype potatotype) (cancontain fridgetype pottype) (cancontain fridgetype tomatotype) (cancontain garbagecantype appletype) (cancontain garbagecantype breadtype) (cancontain garbagecantype dishspongetype) (cancontain garbagecantype eggtype) (cancontain garbagecantype lettucetype) (cancontain garbagecantype potatotype) (cancontain garbagecantype soapbottletype) (cancontain garbagecantype tomatotype) (cancontain microwavetype appletype) (cancontain microwavetype bowltype) (cancontain microwavetype breadtype) (cancontain microwavetype cuptype) (cancontain microwavetype eggtype) (cancontain microwavetype glassbottletype) (cancontain microwavetype mugtype) (cancontain microwavetype platetype) (cancontain microwavetype potatotype) (cancontain microwavetype tomatotype) (cancontain shelftype bowltype) (cancontain shelftype cellphonetype) (cancontain shelftype creditcardtype) (cancontain shelftype cuptype) (cancontain shelftype dishspongetype) (cancontain shelftype glassbottletype) (cancontain shelftype mugtype) (cancontain shelftype peppershakertype) (cancontain shelftype platetype) (cancontain shelftype pottype) (cancontain shelftype saltshakertype) (cancontain shelftype soapbottletype) (cancontain shelftype statuetype) (cancontain shelftype vasetype) (cancontain sinkbasintype appletype) (cancontain sinkbasintype bowltype) (cancontain sinkbasintype butterknifetype) (cancontain sinkbasintype cuptype) (cancontain sinkbasintype dishspongetype) (cancontain sinkbasintype eggtype) (cancontain sinkbasintype forktype) (cancontain sinkbasintype glassbottletype) (cancontain sinkbasintype knifetype) (cancontain sinkbasintype lettucetype) (cancontain sinkbasintype mugtype) (cancontain sinkbasintype pantype) (cancontain sinkbasintype platetype) (cancontain sinkbasintype potatotype) (cancontain sinkbasintype pottype) (cancontain sinkbasintype spatulatype) (cancontain sinkbasintype spoontype) (cancontain sinkbasintype tomatotype) (cancontain stoveburnertype pantype) (cancontain stoveburnertype pottype) (cleanable apple1) (cleanable apple2) (cleanable bowl1) (cleanable bowl2) (cleanable butterknife1) (cleanable cup1) (cleanable dishsponge1) (cleanable dishsponge2) (cleanable egg1) (cleanable egg2) (cleanable fork1) (cleanable knife1) (cleanable knife2) (cleanable lettuce1) (cleanable lettuce2) (cleanable mug1) (cleanable mug2) (cleanable pan1) (cleanable plate1) (cleanable plate2) (cleanable plate3) (cleanable pot1) (cleanable potato1) (cleanable potato2) (cleanable potato3) (cleanable spatula1) (cleanable spatula2) (cleanable spatula3) (cleanable spoon1) (cleanable spoon2) (cleanable tomato1) (closed cabinet1) (closed cabinet2) (closed cabinet5) (closed drawer1) (closed drawer2) (closed drawer3) (closed fridge1) (closed microwave1) (coolable apple1) (coolable apple2) (coolable bowl1) (coolable bowl2) (coolable bread1) (coolable cup1) (coolable egg1) (coolable egg2) (coolable lettuce1) (coolable lettuce2) (coolable mug1) (coolable mug2) (coolable pan1) (coolable plate1) (coolable plate2) (coolable plate3) (coolable pot1) (coolable potato1) (coolable potato2) (coolable potato3) (coolable tomato1) (heatable apple1) (heatable apple2) (heatable bread1) (heatable cup1) (heatable egg1) (heatable egg2) (heatable mug1) (heatable mug2) (heatable plate1) (heatable plate2) (heatable plate3) (heatable potato1) (heatable potato2) (heatable potato3) (heatable tomato1) (holds agent1 mug2) (inreceptacle apple1 countertop1) (inreceptacle apple2 garbagecan1) (inreceptacle bowl1 fridge1) (inreceptacle bowl2 shelf2) (inreceptacle bread1 countertop3) (inreceptacle butterknife1 countertop3) (inreceptacle cellphone1 countertop1) (inreceptacle cellphone2 countertop1) (inreceptacle cellphone2 plate1) (inreceptacle cellphone3 countertop3) (inreceptacle creditcard1 countertop3) (inreceptacle cup1 microwave1) (inreceptacle dishsponge1 drawer3) (inreceptacle dishsponge2 cabinet5) (inreceptacle egg1 fridge1) (inreceptacle egg2 fridge1) (inreceptacle fork1 drawer3) (inreceptacle glassbottle1 countertop3) (inreceptacle glassbottle2 sinkbasin1) (inreceptacle glassbottle3 garbagecan1) (inreceptacle houseplant1 countertop3) (inreceptacle knife1 countertop1) (inreceptacle knife2 countertop3) (inreceptacle lettuce1 countertop1) (inreceptacle lettuce2 countertop1) (inreceptacle mug1 fridge1) (inreceptacle pan1 countertop2) (inreceptacle pan1 stoveburner2) (inreceptacle pan1 stoveburner4) (inreceptacle papertowelroll1 countertop3) (inreceptacle peppershaker1 countertop1) (inreceptacle peppershaker2 drawer1) (inreceptacle peppershaker3 cabinet3) (inreceptacle plate1 countertop1) (inreceptacle plate2 fridge1) (inreceptacle plate3 countertop3) (inreceptacle pot1 stoveburner1) (inreceptacle pot1 stoveburner3) (inreceptacle potato1 fridge1) (inreceptacle potato2 sinkbasin1) (inreceptacle potato3 garbagecan1) (inreceptacle saltshaker1 drawer2) (inreceptacle soapbottle1 cabinet6) (inreceptacle soapbottle2 shelf3) (inreceptacle spatula1 countertop3) (inreceptacle spatula2 sinkbasin1) (inreceptacle spatula3 sinkbasin1) (inreceptacle spoon1 countertop2) (inreceptacle spoon2 countertop3) (inreceptacle statue1 shelf3) (inreceptacle tomato1 fridge1) (inreceptacle vase1 cabinet5) (inreceptacle vase2 shelf1) (iscool mug1) (ishot mug2) (notopenable cabinet3) (notopenable cabinet4) (notopenable cabinet6) (notopenable coffeemachine1) (notopenable countertop1) (notopenable countertop2) (notopenable countertop3) (notopenable garbagecan1) (notopenable shelf1) (notopenable shelf2) (notopenable shelf3) (notopenable sinkbasin1) (notopenable stoveburner1) (notopenable stoveburner2) (notopenable stoveburner3) (notopenable stoveburner4) (notopenable toaster1) (notvalidated) (objectatlocation apple1 location2) (objectatlocation apple2 location8) (objectatlocation bowl1 location10) (objectatlocation bowl2 location29) (objectatlocation bread1 location3) (objectatlocation butterknife1 location3) (objectatlocation cellphone1 location2) (objectatlocation cellphone2 location2) (objectatlocation cellphone3 location3) (objectatlocation chair1 location23) (objectatlocation chair2 location1) (objectatlocation creditcard1 location3) (objectatlocation cup1 location24) (objectatlocation dishsponge1 location27) (objectatlocation dishsponge2 location13) (objectatlocation egg1 location10) (objectatlocation egg2 location10) (objectatlocation fork1 location27) (objectatlocation glassbottle1 location3) (objectatlocation glassbottle2 location6) (objectatlocation glassbottle3 location8) (objectatlocation houseplant1 location3) (objectatlocation knife1 location2) (objectatlocation knife2 location3) (objectatlocation lettuce1 location2) (objectatlocation lettuce2 location2) (objectatlocation lightswitch1 location25) (objectatlocation mug1 location10) (objectatlocation pan1 location11) (objectatlocation papertowelroll1 location3) (objectatlocation peppershaker1 location2) (objectatlocation peppershaker2 location20) (objectatlocation peppershaker3 location16) (objectatlocation plate1 location2) (objectatlocation plate2 location10) (objectatlocation plate3 location3) (objectatlocation pot1 location5) (objectatlocation potato1 location10) (objectatlocation potato2 location6) (objectatlocation potato3 location8) (objectatlocation saltshaker1 location15) (objectatlocation sink1 location6) (objectatlocation soapbottle1 location4) (objectatlocation soapbottle2 location26) (objectatlocation spatula1 location3) (objectatlocation spatula2 location6) (objectatlocation spatula3 location6) (objectatlocation spoon1 location11) (objectatlocation spoon2 location3) (objectatlocation statue1 location26) (objectatlocation stoveknob1 location18) (objectatlocation stoveknob2 location12) (objectatlocation stoveknob3 location12) (objectatlocation stoveknob4 location7) (objectatlocation tomato1 location10) (objectatlocation vase1 location13) (objectatlocation vase2 location17) (objectatlocation window1 location30) (objectatlocation window2 location28) (objecttype apple1 appletype) (objecttype apple2 appletype) (objecttype bowl1 bowltype) (objecttype bowl2 bowltype) (objecttype bread1 breadtype) (objecttype butterknife1 butterknifetype) (objecttype cellphone1 cellphonetype) (objecttype cellphone2 cellphonetype) (objecttype cellphone3 cellphonetype) (objecttype chair1 chairtype) (objecttype chair2 chairtype) (objecttype creditcard1 creditcardtype) (objecttype cup1 cuptype) (objecttype dishsponge1 dishspongetype) (objecttype dishsponge2 dishspongetype) (objecttype egg1 eggtype) (objecttype egg2 eggtype) (objecttype fork1 forktype) (objecttype glassbottle1 glassbottletype) (objecttype glassbottle2 glassbottletype) (objecttype glassbottle3 glassbottletype) (objecttype houseplant1 houseplanttype) (objecttype knife1 knifetype) (objecttype knife2 knifetype) (objecttype lettuce1 lettucetype) (objecttype lettuce2 lettucetype) (objecttype lightswitch1 lightswitchtype) (objecttype mug1 mugtype) (objecttype mug2 mugtype) (objecttype pan1 pantype) (objecttype papertowelroll1 papertowelrolltype) (objecttype peppershaker1 peppershakertype) (objecttype peppershaker2 peppershakertype) (objecttype peppershaker3 peppershakertype) (objecttype plate1 platetype) (objecttype plate2 platetype) (objecttype plate3 platetype) (objecttype pot1 pottype) (objecttype potato1 potatotype) (objecttype potato2 potatotype) (objecttype potato3 potatotype) (objecttype saltshaker1 saltshakertype) (objecttype sink1 sinktype) (objecttype soapbottle1 soapbottletype) (objecttype soapbottle2 soapbottletype) (objecttype spatula1 spatulatype) (objecttype spatula2 spatulatype) (objecttype spatula3 spatulatype) (objecttype spoon1 spoontype) (objecttype spoon2 spoontype) (objecttype statue1 statuetype) (objecttype stoveknob1 stoveknobtype) (objecttype stoveknob2 stoveknobtype) (objecttype stoveknob3 stoveknobtype) (objecttype stoveknob4 stoveknobtype) (objecttype tomato1 tomatotype) (objecttype vase1 vasetype) (objecttype vase2 vasetype) (objecttype window1 windowtype) (objecttype window2 windowtype) (openable cabinet1) (openable cabinet2) (openable cabinet5) (openable drawer1) (openable drawer2) (openable drawer3) (openable fridge1) (openable microwave1) (pickupable apple1) (pickupable apple2) (pickupable bowl1) (pickupable bowl2) (pickupable bread1) (pickupable butterknife1) (pickupable cellphone1) (pickupable cellphone2) (pickupable cellphone3) (pickupable creditcard1) (pickupable cup1) (pickupable dishsponge1) (pickupable dishsponge2) (pickupable egg1) (pickupable egg2) (pickupable fork1) (pickupable glassbottle1) (pickupable glassbottle2) (pickupable glassbottle3) (pickupable knife1) (pickupable knife2) (pickupable lettuce1) (pickupable lettuce2) (pickupable mug1) (pickupable mug2) (pickupable pan1) (pickupable papertowelroll1) (pickupable peppershaker1) (pickupable peppershaker2) (pickupable peppershaker3) (pickupable plate1) (pickupable plate2) (pickupable plate3) (pickupable pot1) (pickupable potato1) (pickupable potato2) (pickupable potato3) (pickupable saltshaker1) (pickupable soapbottle1) (pickupable soapbottle2) (pickupable spatula1) (pickupable spatula2) (pickupable spatula3) (pickupable spoon1) (pickupable spoon2) (pickupable statue1) (pickupable tomato1) (pickupable vase1) (pickupable vase2) (receptacleatlocation cabinet1 location19) (receptacleatlocation cabinet2 location22) (receptacleatlocation cabinet3 location16) (receptacleatlocation cabinet4 location14) (receptacleatlocation cabinet5 location13) (receptacleatlocation cabinet6 location4) (receptacleatlocation coffeemachine1 location11) (receptacleatlocation countertop1 location2) (receptacleatlocation countertop2 location11) (receptacleatlocation countertop3 location3) (receptacleatlocation drawer1 location20) (receptacleatlocation drawer2 location15) (receptacleatlocation drawer3 location27) (receptacleatlocation fridge1 location10) (receptacleatlocation garbagecan1 location8) (receptacleatlocation microwave1 location24) (receptacleatlocation shelf1 location17) (receptacleatlocation shelf2 location29) (receptacleatlocation shelf3 location26) (receptacleatlocation sinkbasin1 location6) (receptacleatlocation stoveburner1 location5) (receptacleatlocation stoveburner2 location21) (receptacleatlocation stoveburner3 location5) (receptacleatlocation stoveburner4 location21) (receptacleatlocation toaster1 location9) (receptacletype cabinet1 cabinettype) (receptacletype cabinet2 cabinettype) (receptacletype cabinet3 cabinettype) (receptacletype cabinet4 cabinettype) (receptacletype cabinet5 cabinettype) (receptacletype cabinet6 cabinettype) (receptacletype coffeemachine1 coffeemachinetype) (receptacletype countertop1 countertoptype) (receptacletype countertop2 countertoptype) (receptacletype countertop3 countertoptype) (receptacletype drawer1 drawertype) (receptacletype drawer2 drawertype) (receptacletype drawer3 drawertype) (receptacletype fridge1 fridgetype) (receptacletype garbagecan1 garbagecantype) (receptacletype microwave1 microwavetype) (receptacletype shelf1 shelftype) (receptacletype shelf2 shelftype) (receptacletype shelf3 shelftype) (receptacletype sinkbasin1 sinkbasintype) (receptacletype stoveburner1 stoveburnertype) (receptacletype stoveburner2 stoveburnertype) (receptacletype stoveburner3 stoveburnertype) (receptacletype stoveburner4 stoveburnertype) (receptacletype toaster1 toastertype) (sliceable apple1) (sliceable apple2) (sliceable bread1) (sliceable egg1) (sliceable egg2) (sliceable lettuce1) (sliceable lettuce2) (sliceable potato1) (sliceable potato2) (sliceable potato3) (sliceable tomato1))\n    (:goal (validateheatandplace mugtype coffeemachinetype))\n)"}
{"id": 1851857999042571504, "group": "reachable_action_gen", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. garbagecan1 is at location2. desk2 is at location10. shelf3 is at location11. drawer3 is at location17. shelf2 is at location25. bed1 is at location13. safe1 is at location6. drawer2 is at location18. shelf6 is at location24. laundryhamper1 is at location8. drawer4 and drawer5 are at location12. drawer6 is at location1. shelf5 is at location22. shelf1 is at location20. shelf4 is at location23. desk1 is at location3. drawer1 is at location21. Receptacles that are neither open nor closed are not openable. Receptacles are not pickupable. Desklamps are not pickupable.  Currently, the objects are at locations as follows. cellphone1, pillow1, pillow2, laptop1, and laptop2 are at location13. blinds2 is at location15. cellphone3 is at location12. bowl1, cd1, pencil1, alarmclock1, and mug1 are at location3. pen1, pencil3, mug2, cellphone2, and cd3 are at location10. chair1 is at location21. mirror1 is at location19. cd2 is at location2. window2 is at location4. bowl3 is at location24. keychain1 and keychain2 are at location6. baseballbat1 is at location9. window1 is at location5. alarmclock2 is at location11. chair2 is at location26. laundryhamperlid1 is at location8. alarmclock3, bowl2, and desklamp1 are at location23. pencil2 and creditcard1 are at location22. blinds1 is at location16. basketball1 is at location7. lightswitch1 is at location14. agent agent1 is at location location6. The objects are in/on receptacle as follows. bowl3 is on shelf6. pen1, desklamp1, mug2, alarmclock3, pencil3, cellphone2, cd3, and bowl2 are on desk2. keychain1 and keychain2 are in safe1. cellphone3 is in drawer5. alarmclock1, bowl1, cd1, mug1, and pencil1 are on desk1. alarmclock3, desklamp1, and bowl2 are on shelf4. pillow2, cellphone1, pillow1, laptop1, and laptop2 are in bed1. creditcard1 and pencil2 are on shelf5. alarmclock2 is on shelf3. cd2 is in garbagecan1. drawer3, drawer1, safe1, and drawer6 are closed. desklamp1 is off. Nothing has been validated. agent1 is holding object book1. The available actions are: (go_to_location ?a ?lstart ?lend ?r) - agent ?a goes to receptacle ?r from the current location ?lstart to the next location ?lend, (open_receptacle ?a ?l ?r) - agent ?a that is at location ?l opens receptacle ?r, (close_receptacle ?a ?l ?r) - agent ?a closes receptacle ?r while at location ?l, (pickup_object_from_not_openable_receptacle ?a ?l ?o ?r) - agent ?a picks up object ?o from a not openable receptacle ?r that is at location ?l, (pickup_object_from_openable_receptacle ?a ?l ?o ?r) - agent ?a collects object ?o from an openable receptacle ?r while at location ?l, (put_object_on_not_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a places object ?o of type ?ot on a not openable receptacle ?r of type ?rt while at location ?l, (put_object_in_openable_receptacle ?a ?l ?o ?r ?ot ?rt) - agent ?a puts down an object ?o of type ?ot in an openable receptacle ?r of type ?rt that is at location ?l, (clean_object ?a ?l ?r ?o) - agent ?a cleans a cleanable object ?o in a sink ?r at location ?l, (heat_object ?a ?l ?r ?o) - agent ?a warms up object ?o with a microwave ?r at location ?l, (cool_object ?a ?l ?r ?o) - agent ?a chills a coolable object ?o in receptacle ?r at location ?l, (toggle_object_off ?a ?l ?o ?r) - agent ?a turns off object ?o that is on the receptacle ?r at location ?l, (toggle_object_on ?a ?l ?o ?r) - agent ?a turns on object ?o that is on the receptacle ?r at location ?l, (slice_object ?a ?l ?co ?ko) - agent ?a cuts object ?co with a knife ?ko at location ?l, (validate_pick_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is in a receptacle ?r of type ?rt, (validate_examine_in_light ?otoggle ?otogglet ?o ?ot ?r ?a ?l) - check that the togglable object ?otoggle of type ?otogglet is toggled and in receptacle ?r at location ?l while agent ?a is holding object ?o of type ?ot, (validate_clean_and_place_in_receptacle ?o ?ot ?r ?rt) - ensure that object ?o of type ?ot is clean and in receptacle ?r of type ?rt, (validate_heat_and_place_in_receptacle ?o ?ot ?r ?rt) - check that object ?o of type ?ot is hot and in receptacle ?r of type ?rt, (validate_cool_and_place_in_receptacle ?o ?ot ?r ?rt) - validate that object ?o of type ?ot is cool and in receptacle ?r of type ?rt, and (validate_pick_two_and_place_in_receptacle ?o1 ?o2 ?ot ?r ?rt) - check that the two objects ?o1 and ?o2 of the same type ?ot are in receptacle ?r of type ?rt.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(toggle_object_off agent1 location6 laptoptype laundryhamper1)"], "PDDL_domain": "(define (domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:types agent location objectorreceptacle otype rtype - object receptacle - objectorreceptacle)\n    (:constants butterknifetype knifetype sinktype - otype fridgetype microwavetype sinkbasintype - rtype)\n    (:predicates (atlocation ?a - agent ?l - location)  (cancontain ?rt - rtype ?ot - otype)  (checked ?r - receptacle)  (cleanable ?o - object)  (closed ?r - receptacle)  (coolable ?o - object)  (handempty ?a - agent)  (heatable ?o - object)  (holds ?a - agent ?o - object)  (inreceptacle ?o - object ?r - objectorreceptacle)  (isclean ?o - object)  (iscool ?o - object)  (ishot ?o - object)  (isoff ?o - object)  (ison ?o - object)  (issliced ?o - object)  (istoggled ?o - object)  (notopenable ?r - receptacle)  (notvalidated) (objectatlocation ?o - object ?l - location)  (objecttype ?o - object ?t - otype)  (openable ?r - receptacle)  (opened ?r - receptacle)  (pickupable ?o - object)  (receptacleatlocation ?r - receptacle ?l - location)  (receptacletype ?r - receptacle ?t - rtype)  (sliceable ?o - object)  (toggleable ?o - object)  (validatecleanandplace ?ot - otype ?rt - rtype)  (validatecoolandplace ?ot - otype ?rt - rtype)  (validateexamineinlight ?otogglet - otype ?ot - otype)  (validateheatandplace ?ot - otype ?rt - rtype)  (validatepickandplace ?ot - otype ?rt - rtype)  (validatepicktwoandplace ?ot - otype ?rt - rtype))\n    (:action clean_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (cleanable ?o) (receptacletype ?r sinkbasintype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (isclean ?o)\n    )\n     (:action close_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (opened ?r) (notvalidated))\n        :effect (and (not (opened ?r)) (closed ?r))\n    )\n     (:action cool_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (coolable ?o) (receptacletype ?r fridgetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (iscool ?o) (not (ishot ?o)))\n    )\n     (:action go_to_location\n        :parameters (?a - agent ?lstart - location ?lend - location ?r - receptacle)\n        :precondition (and (atlocation ?a ?lstart) (receptacleatlocation ?r ?lend) (not (= ?lstart ?lend)) (notvalidated))\n        :effect (and (not (atlocation ?a ?lstart)) (atlocation ?a ?lend))\n    )\n     (:action heat_object\n        :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n        :precondition (and (heatable ?o) (receptacletype ?r microwavetype) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (holds ?a ?o) (notvalidated))\n        :effect (and (ishot ?o) (not (iscool ?o)))\n    )\n     (:action open_receptacle\n        :parameters (?a - agent ?l - location ?r - receptacle)\n        :precondition (and (openable ?r) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (closed ?r) (notvalidated))\n        :effect (and (opened ?r) (not (closed ?r)) (checked ?r))\n    )\n     (:action pickup_object_from_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (notopenable ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action pickup_object_from_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (pickupable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (handempty ?a) (openable ?r) (opened ?r) (notvalidated))\n        :effect (and (not (inreceptacle ?o ?r)) (holds ?a ?o) (not (handempty ?a)) (not (objectatlocation ?o ?l)))\n    )\n     (:action put_object_in_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (openable ?r) (opened ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action put_object_on_not_openable_receptacle\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n        :precondition (and (holds ?a ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (notopenable ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (cancontain ?rt ?ot) (notvalidated))\n        :effect (and (inreceptacle ?o ?r) (objectatlocation ?o ?l) (not (holds ?a ?o)) (handempty ?a))\n    )\n     (:action slice_object\n        :parameters (?a - agent ?l - location ?co - object ?ko - object)\n        :precondition (and (sliceable ?co) (or (objecttype ?ko knifetype) (objecttype ?ko butterknifetype)) (atlocation ?a ?l) (objectatlocation ?co ?l) (holds ?a ?ko) (notvalidated))\n        :effect (issliced ?co)\n    )\n     (:action toggle_object_off\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (ison ?o) (notvalidated))\n        :effect (and (not (ison ?o)) (isoff ?o) (istoggled ?o))\n    )\n     (:action toggle_object_on\n        :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n        :precondition (and (toggleable ?o) (atlocation ?a ?l) (receptacleatlocation ?r ?l) (inreceptacle ?o ?r) (isoff ?o) (notvalidated))\n        :effect (and (ison ?o) (not (isoff ?o)) (istoggled ?o))\n    )\n     (:action validate_clean_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (cleanable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (isclean ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecleanandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_cool_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (coolable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (iscool ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validatecoolandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_examine_in_light\n        :parameters (?otoggle - object ?otogglet - otype ?o - object ?ot - otype ?r - receptacle ?a - agent ?l - location)\n        :precondition (and (objecttype ?otoggle ?otogglet) (toggleable ?otoggle) (istoggled ?otoggle) (inreceptacle ?otoggle ?r) (receptacleatlocation ?r ?l) (holds ?a ?o) (objecttype ?o ?ot) (atlocation ?a ?l) (notvalidated))\n        :effect (and (validateexamineinlight ?otogglet ?ot) (not (notvalidated)))\n    )\n     (:action validate_heat_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (heatable ?o) (objecttype ?o ?ot) (receptacletype ?r ?rt) (ishot ?o) (inreceptacle ?o ?r) (notvalidated))\n        :effect (and (validateheatandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_and_place_in_receptacle\n        :parameters (?o - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (inreceptacle ?o ?r) (objecttype ?o ?ot) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepickandplace ?ot ?rt) (not (notvalidated)))\n    )\n     (:action validate_pick_two_and_place_in_receptacle\n        :parameters (?o1 - object ?o2 - object ?ot - otype ?r - receptacle ?rt - rtype)\n        :precondition (and (objecttype ?o1 ?ot) (objecttype ?o2 ?ot) (inreceptacle ?o1 ?r) (inreceptacle ?o2 ?r) (not (= ?o1 ?o2)) (receptacletype ?r ?rt) (notvalidated))\n        :effect (and (validatepicktwoandplace ?ot ?rt) (not (notvalidated)))\n    )\n)", "PDDL_problem": "(define (problem plan_trial_t20190908_020029_636862)\n    (:domain alfred)\n    (:requirements :action-costs :adl :typing)\n    (:objects agent1 - agent location1 location10 location11 location12 location13 location14 location15 location16 location17 location18 location19 location2 location20 location21 location22 location23 location24 location25 location26 location27 location3 location4 location5 location6 location7 location8 location9 - location alarmclock alarmclock1 alarmclock2 alarmclock3 apple baseballbat baseballbat1 basketball basketball1 bathtub blinds blinds1 blinds2 book book1 boots bowl bowl1 bowl2 bowl3 box bread butterknife candle cd cd1 cd2 cd3 cellphone cellphone1 cellphone2 cellphone3 chair chair1 chair2 cloth creditcard creditcard1 cup curtains desklamp desklamp1 dishsponge egg floorlamp footstool fork glassbottle handtowel houseplant kettle keychain keychain1 keychain2 knife ladle laptop laptop1 laptop2 laundryhamperlid laundryhamperlid1 lettuce lightswitch lightswitch1 mirror mirror1 mug mug1 mug2 newspaper painting pan papertowel papertowelroll pen pen1 pencil pencil1 pencil2 pencil3 peppershaker pillow pillow1 pillow2 plate plunger poster pot potato remotecontrol saltshaker scrubbrush showerdoor showerglass sink soapbar soapbottle spatula spoon spraybottle statue stoveknob teddybear television tennisracket tissuebox toiletpaper toiletpaperroll tomato towel vase watch wateringcan window window1 window2 winebottle - object alarmclocktype appletype baseballbattype basketballtype bathtubtype blindstype booktype bootstype bowltype boxtype breadtype candletype cdtype cellphonetype chairtype clothtype creditcardtype cuptype curtainstype desklamptype dishspongetype eggtype floorlamptype footstooltype forktype glassbottletype handtoweltype houseplanttype kettletype keychaintype ladletype laptoptype laundryhamperlidtype lettucetype lightswitchtype mirrortype mugtype newspapertype paintingtype pantype papertowelrolltype papertoweltype penciltype pentype peppershakertype pillowtype platetype plungertype postertype potatotype pottype remotecontroltype saltshakertype scrubbrushtype showerdoortype showerglasstype soapbartype soapbottletype spatulatype spoontype spraybottletype statuetype stoveknobtype teddybeartype televisiontype tennisrackettype tissueboxtype toiletpaperrolltype toiletpapertype tomatotype toweltype vasetype watchtype wateringcantype windowtype winebottletype - otype bed1 desk1 desk2 drawer1 drawer2 drawer3 drawer4 drawer5 drawer6 garbagecan1 laundryhamper1 safe1 shelf1 shelf2 shelf3 shelf4 shelf5 shelf6 - receptacle armchairtype bathtubbasintype bedtype cabinettype carttype coffeemachinetype coffeetabletype countertoptype desktype diningtabletype drawertype dressertype garbagecantype handtowelholdertype laundryhampertype ottomantype paintinghangertype safetype shelftype sidetabletype sofatype stoveburnertype toastertype toiletpaperhangertype toilettype towelholdertype tvstandtype - rtype)\n    (:init (atlocation agent1 location6) (cancontain bedtype baseballbattype) (cancontain bedtype basketballtype) (cancontain bedtype booktype) (cancontain bedtype cellphonetype) (cancontain bedtype laptoptype) (cancontain bedtype pillowtype) (cancontain desktype alarmclocktype) (cancontain desktype basketballtype) (cancontain desktype booktype) (cancontain desktype bowltype) (cancontain desktype cdtype) (cancontain desktype cellphonetype) (cancontain desktype creditcardtype) (cancontain desktype keychaintype) (cancontain desktype laptoptype) (cancontain desktype mugtype) (cancontain desktype penciltype) (cancontain desktype pentype) (cancontain drawertype booktype) (cancontain drawertype cdtype) (cancontain drawertype cellphonetype) (cancontain drawertype creditcardtype) (cancontain drawertype keychaintype) (cancontain drawertype penciltype) (cancontain drawertype pentype) (cancontain garbagecantype cdtype) (cancontain garbagecantype penciltype) (cancontain garbagecantype pentype) (cancontain safetype cdtype) (cancontain safetype cellphonetype) (cancontain safetype creditcardtype) (cancontain safetype keychaintype) (cancontain shelftype alarmclocktype) (cancontain shelftype booktype) (cancontain shelftype bowltype) (cancontain shelftype cdtype) (cancontain shelftype cellphonetype) (cancontain shelftype creditcardtype) (cancontain shelftype keychaintype) (cancontain shelftype mugtype) (cancontain shelftype penciltype) (cancontain shelftype pentype) (cleanable bowl1) (cleanable bowl2) (cleanable bowl3) (cleanable mug1) (cleanable mug2) (closed drawer1) (closed drawer3) (closed drawer6) (closed safe1) (coolable bowl1) (coolable bowl2) (coolable bowl3) (coolable mug1) (coolable mug2) (heatable mug1) (heatable mug2) (holds agent1 book1) (inreceptacle alarmclock1 desk1) (inreceptacle alarmclock2 shelf3) (inreceptacle alarmclock3 desk2) (inreceptacle alarmclock3 shelf4) (inreceptacle bowl1 desk1) (inreceptacle bowl2 desk2) (inreceptacle bowl2 shelf4) (inreceptacle bowl3 shelf6) (inreceptacle cd1 desk1) (inreceptacle cd2 garbagecan1) (inreceptacle cd3 desk2) (inreceptacle cellphone1 bed1) (inreceptacle cellphone2 desk2) (inreceptacle cellphone3 drawer5) (inreceptacle creditcard1 shelf5) (inreceptacle desklamp1 desk2) (inreceptacle desklamp1 shelf4) (inreceptacle keychain1 safe1) (inreceptacle keychain2 safe1) (inreceptacle laptop1 bed1) (inreceptacle laptop2 bed1) (inreceptacle mug1 desk1) (inreceptacle mug2 desk2) (inreceptacle pen1 desk2) (inreceptacle pencil1 desk1) (inreceptacle pencil2 shelf5) (inreceptacle pencil3 desk2) (inreceptacle pillow1 bed1) (inreceptacle pillow2 bed1) (isoff desklamp1) (notopenable bed1) (notopenable desk1) (notopenable desk2) (notopenable drawer2) (notopenable drawer4) (notopenable drawer5) (notopenable garbagecan1) (notopenable laundryhamper1) (notopenable shelf1) (notopenable shelf2) (notopenable shelf3) (notopenable shelf4) (notopenable shelf5) (notopenable shelf6) (notvalidated) (objectatlocation alarmclock1 location3) (objectatlocation alarmclock2 location11) (objectatlocation alarmclock3 location23) (objectatlocation baseballbat1 location9) (objectatlocation basketball1 location7) (objectatlocation blinds1 location16) (objectatlocation blinds2 location15) (objectatlocation bowl1 location3) (objectatlocation bowl2 location23) (objectatlocation bowl3 location24) (objectatlocation cd1 location3) (objectatlocation cd2 location2) (objectatlocation cd3 location10) (objectatlocation cellphone1 location13) (objectatlocation cellphone2 location10) (objectatlocation cellphone3 location12) (objectatlocation chair1 location21) (objectatlocation chair2 location26) (objectatlocation creditcard1 location22) (objectatlocation desklamp1 location23) (objectatlocation keychain1 location6) (objectatlocation keychain2 location6) (objectatlocation laptop1 location13) (objectatlocation laptop2 location13) (objectatlocation laundryhamperlid1 location8) (objectatlocation lightswitch1 location14) (objectatlocation mirror1 location19) (objectatlocation mug1 location3) (objectatlocation mug2 location10) (objectatlocation pen1 location10) (objectatlocation pencil1 location3) (objectatlocation pencil2 location22) (objectatlocation pencil3 location10) (objectatlocation pillow1 location13) (objectatlocation pillow2 location13) (objectatlocation window1 location5) (objectatlocation window2 location4) (objecttype alarmclock1 alarmclocktype) (objecttype alarmclock2 alarmclocktype) (objecttype alarmclock3 alarmclocktype) (objecttype baseballbat1 baseballbattype) (objecttype basketball1 basketballtype) (objecttype blinds1 blindstype) (objecttype blinds2 blindstype) (objecttype book1 booktype) (objecttype bowl1 bowltype) (objecttype bowl2 bowltype) (objecttype bowl3 bowltype) (objecttype cd1 cdtype) (objecttype cd2 cdtype) (objecttype cd3 cdtype) (objecttype cellphone1 cellphonetype) (objecttype cellphone2 cellphonetype) (objecttype cellphone3 cellphonetype) (objecttype chair1 chairtype) (objecttype chair2 chairtype) (objecttype creditcard1 creditcardtype) (objecttype desklamp1 desklamptype) (objecttype keychain1 keychaintype) (objecttype keychain2 keychaintype) (objecttype laptop1 laptoptype) (objecttype laptop2 laptoptype) (objecttype laundryhamperlid1 laundryhamperlidtype) (objecttype lightswitch1 lightswitchtype) (objecttype mirror1 mirrortype) (objecttype mug1 mugtype) (objecttype mug2 mugtype) (objecttype pen1 pentype) (objecttype pencil1 penciltype) (objecttype pencil2 penciltype) (objecttype pencil3 penciltype) (objecttype pillow1 pillowtype) (objecttype pillow2 pillowtype) (objecttype window1 windowtype) (objecttype window2 windowtype) (openable drawer1) (openable drawer3) (openable drawer6) (openable safe1) (pickupable alarmclock1) (pickupable alarmclock2) (pickupable alarmclock3) (pickupable baseballbat1) (pickupable basketball1) (pickupable book1) (pickupable bowl1) (pickupable bowl2) (pickupable bowl3) (pickupable cd1) (pickupable cd2) (pickupable cd3) (pickupable cellphone1) (pickupable cellphone2) (pickupable cellphone3) (pickupable creditcard1) (pickupable keychain1) (pickupable keychain2) (pickupable laptop1) (pickupable laptop2) (pickupable mug1) (pickupable mug2) (pickupable pen1) (pickupable pencil1) (pickupable pencil2) (pickupable pencil3) (pickupable pillow1) (pickupable pillow2) (receptacleatlocation bed1 location13) (receptacleatlocation desk1 location3) (receptacleatlocation desk2 location10) (receptacleatlocation drawer1 location21) (receptacleatlocation drawer2 location18) (receptacleatlocation drawer3 location17) (receptacleatlocation drawer4 location12) (receptacleatlocation drawer5 location12) (receptacleatlocation drawer6 location1) (receptacleatlocation garbagecan1 location2) (receptacleatlocation laundryhamper1 location8) (receptacleatlocation safe1 location6) (receptacleatlocation shelf1 location20) (receptacleatlocation shelf2 location25) (receptacleatlocation shelf3 location11) (receptacleatlocation shelf4 location23) (receptacleatlocation shelf5 location22) (receptacleatlocation shelf6 location24) (receptacletype bed1 bedtype) (receptacletype desk1 desktype) (receptacletype desk2 desktype) (receptacletype drawer1 drawertype) (receptacletype drawer2 drawertype) (receptacletype drawer3 drawertype) (receptacletype drawer4 drawertype) (receptacletype drawer5 drawertype) (receptacletype drawer6 drawertype) (receptacletype garbagecan1 garbagecantype) (receptacletype laundryhamper1 laundryhampertype) (receptacletype safe1 safetype) (receptacletype shelf1 shelftype) (receptacletype shelf2 shelftype) (receptacletype shelf3 shelftype) (receptacletype shelf4 shelftype) (receptacletype shelf5 shelftype) (receptacletype shelf6 shelftype) (toggleable desklamp1))\n    (:goal (validateexamineinlight desklamptype booktype))\n)"}
