{"id": 6778983232697869830, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_7. The following block(s) are on the table: block_8, block_10, block_6, and block_2. The following block(s) are stacked on top of another block: block_4 is on block_9, block_3 is on block_4, block_5 is on block_2, block_1 is on block_8, and block_9 is on block_5.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robotic arm is holding block_5 and The robotic arm is holding block_7. B. The block block_4 is currently situated under the block block_10 and Block block_4 is clear. C. The block block_2 is on top of block block_3 and The block block_3 is currently situated under the block block_7. D. The block block_7 is on top of block block_10 and The block block_1 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robotic arm is holding block_5 and The robotic arm is holding block_7", "The block block_4 is currently situated under the block block_10 and Block block_4 is clear", "The block block_2 is on top of block block_3 and The block block_3 is currently situated under the block block_7", "The block block_7 is on top of block block_10 and The block block_1 is currently being held by the robotic arm"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -62621284872717015, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_6. The following block(s) are on the table: block_8, block_10, block_2, and block_1. The following block(s) are stacked on top of another block: block_4 is on block_9, block_3 is on block_7, block_5 is on block_2, block_7 is on block_10, and block_9 is on block_5.", "question": "Which of the following options can hold in a state that can potentially be reached? A. block_9 is not obstructed by any other blocks and Block block_6 is clear. B. The block block_6 is currently situated under the block block_3 and The block block_8 is on top of block block_6. C. The robotic arm is holding block_10 and The robotic arm is empty. D. The robotic arm is holding block_6 and The block block_10 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["block_9 is not obstructed by any other blocks and Block block_6 is clear", "The block block_6 is currently situated under the block block_3 and The block block_8 is on top of block block_6", "The robotic arm is holding block_10 and The robotic arm is empty", "The robotic arm is holding block_6 and The block block_10 is currently being held by the robotic arm"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -2212005186588612364, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_8, block_10, block_6, and block_2. The following block(s) are stacked on top of another block: block_5 is on block_9, block_4 is on block_5, block_9 is on block_1, block_1 is on block_8, and block_7 is on block_10.", "question": "Which of the following options can hold in a state that can potentially be reached? A. block_5 is not obstructed by any other blocks and Block block_3 is on the table. B. The block block_8 is currently situated above the block block_7 and Block block_8 is located on the table. C. The block block_8 is on top of block block_1 and No blocks are placed on top of block_1. D. The block block_1 is on top of block block_4 and The block block_4 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["block_5 is not obstructed by any other blocks and Block block_3 is on the table", "The block block_8 is currently situated above the block block_7 and Block block_8 is located on the table", "The block block_8 is on top of block block_1 and No blocks are placed on top of block_1", "The block block_1 is on top of block block_4 and The block block_4 is currently being held by the robotic arm"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 4669369140635682769, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_15. The following block(s) are on the table: block_6, block_20, block_9, block_10, and block_13. The following block(s) are stacked on top of another block: block_1 is on block_17, block_3 is on block_13, block_8 is on block_4, block_7 is on block_10, block_16 is on block_11, block_4 is on block_6, block_17 is on block_12, block_2 is on block_14, block_19 is on block_16, block_11 is on block_8, block_14 is on block_20, block_18 is on block_5, block_12 is on block_7, and block_5 is on block_1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The block block_4 is on top of block block_13 and The robotic arm is holding block_4. B. block_14 is not obstructed by any other blocks and The block block_2 is currently being held by the robotic arm. C. The block block_19 is on top of block block_20 and The block block_6 is currently situated under the block block_19. D. The robotic arm is holding block_4 and The robotic arm is holding block_3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_4 is on top of block block_13 and The robotic arm is holding block_4", "block_14 is not obstructed by any other blocks and The block block_2 is currently being held by the robotic arm", "The block block_19 is on top of block block_20 and The block block_6 is currently situated under the block block_19", "The robotic arm is holding block_4 and The robotic arm is holding block_3"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -3299265913143099877, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_3 and block_2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. block_1 is not obstructed by any other blocks and Block block_1 is on the table. B. The block block_1 is currently situated under the block block_1 and The block block_1 is currently being held by the robotic arm. C. The block block_3 is currently situated under the block block_3 and The robotic arm is holding block_3. D. The block block_2 is on top of block block_2 and Block block_2 is on the table.", "choices": {"label": ["A", "B", "C", "D"], "text": ["block_1 is not obstructed by any other blocks and Block block_1 is on the table", "The block block_1 is currently situated under the block block_1 and The block block_1 is currently being held by the robotic arm", "The block block_3 is currently situated under the block block_3 and The robotic arm is holding block_3", "The block block_2 is on top of block block_2 and Block block_2 is on the table"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 8516534063391446966, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_18, block_14, block_7, block_6, block_1, block_20, block_12, block_10, and block_16. The following block(s) are stacked on top of another block: block_2 is on block_18, block_4 is on block_11, block_11 is on block_1, block_3 is on block_13, block_9 is on block_2, block_5 is on block_14, block_15 is on block_9, block_17 is on block_12, block_13 is on block_4, block_19 is on block_16, and block_8 is on block_7.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The block block_2 is currently situated above the block block_20 and The block block_15 is currently situated above the block block_20. B. The block block_20 is currently situated under the block block_8 and No blocks are placed on top of block_7. C. The block block_2 is currently situated under the block block_6 and The block block_2 is on top of block block_2. D. The block block_15 is on top of block block_18 and The block block_10 is currently situated above the block block_18.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_2 is currently situated above the block block_20 and The block block_15 is currently situated above the block block_20", "The block block_20 is currently situated under the block block_8 and No blocks are placed on top of block_7", "The block block_2 is currently situated under the block block_6 and The block block_2 is on top of block block_2", "The block block_15 is on top of block block_18 and The block block_10 is currently situated above the block block_18"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -677443437841026435, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_3 and block_5. The following block(s) are stacked on top of another block: block_1 is on block_3 and block_2 is on block_1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Block block_1 is clear and The block block_5 is currently situated under the block block_4. B. The block block_2 is on top of block block_4 and Block block_2 is located on the table. C. The block block_1 is currently being held by the robotic arm and The robotic arm is empty. D. The block block_3 is currently situated under the block block_2 and The block block_3 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Block block_1 is clear and The block block_5 is currently situated under the block block_4", "The block block_2 is on top of block block_4 and Block block_2 is located on the table", "The block block_1 is currently being held by the robotic arm and The robotic arm is empty", "The block block_3 is currently situated under the block block_2 and The block block_3 is currently being held by the robotic arm"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -5338147461821950878, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_3, block_5, and block_1. The following block(s) are stacked on top of another block: block_4 is on block_5 and block_2 is on block_3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The block block_4 is currently situated above the block block_1 and The block block_4 is currently being held by the robotic arm. B. The robotic arm is holding block_4 and The robotic arm is not holding anything. C. The block block_3 is currently being held by the robotic arm and The robotic arm is not holding anything. D. No blocks are placed on top of block_3 and The block block_1 is currently situated under the block block_2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_4 is currently situated above the block block_1 and The block block_4 is currently being held by the robotic arm", "The robotic arm is holding block_4 and The robotic arm is not holding anything", "The block block_3 is currently being held by the robotic arm and The robotic arm is not holding anything", "No blocks are placed on top of block_3 and The block block_1 is currently situated under the block block_2"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 6521555625121049564, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_4 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_5 and block_3 is on block_4.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The block block_1 is currently being held by the robotic arm and The robotic arm is not holding anything. B. The block block_3 is on top of block block_2 and The block block_2 is currently being held by the robotic arm. C. block_1 is not obstructed by any other blocks and The block block_2 is currently situated under the block block_1. D. The block block_2 is currently being held by the robotic arm and The robotic arm is empty.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_1 is currently being held by the robotic arm and The robotic arm is not holding anything", "The block block_3 is on top of block block_2 and The block block_2 is currently being held by the robotic arm", "block_1 is not obstructed by any other blocks and The block block_2 is currently situated under the block block_1", "The block block_2 is currently being held by the robotic arm and The robotic arm is empty"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 3200330902639271941, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_4, block_8, block_10, block_6, and block_2. The following block(s) are stacked on top of another block: block_7 is on block_10, block_3 is on block_7, block_9 is on block_1, block_5 is on block_2, and block_1 is on block_8.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robotic arm is holding block_10 and The robotic arm is holding block_5. B. The block block_7 is currently situated under the block block_2 and block_7 is not obstructed by any other blocks. C. The block block_9 is currently situated under the block block_4. D. The robotic arm is holding block_2 and The block block_1 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robotic arm is holding block_10 and The robotic arm is holding block_5", "The block block_7 is currently situated under the block block_2 and block_7 is not obstructed by any other blocks", "The block block_9 is currently situated under the block block_4", "The robotic arm is holding block_2 and The block block_1 is currently being held by the robotic arm"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -4078892956067116030, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_1. The following block(s) are stacked on top of another block: block_3 is on block_5, block_5 is on block_2, and block_4 is on block_1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The block block_1 is currently situated under the block block_1 and The block block_1 is on top of block block_4. B. The block block_3 is on top of block block_3 and The robotic arm is holding block_3. C. The block block_2 is currently being held by the robotic arm and The robotic arm is not holding anything. D. Block block_5 is clear and Block block_3 is placed on the table.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_1 is currently situated under the block block_1 and The block block_1 is on top of block block_4", "The block block_3 is on top of block block_3 and The robotic arm is holding block_3", "The block block_2 is currently being held by the robotic arm and The robotic arm is not holding anything", "Block block_5 is clear and Block block_3 is placed on the table"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 967973761642248792, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_4, block_3, and block_2. The following block(s) are stacked on top of another block: block_1 is on block_5 and block_5 is on block_4.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The block block_4 is currently situated under the block block_1 and The robotic arm is holding block_4. B. The block block_3 is on top of block block_2. C. The block block_1 is currently being held by the robotic arm and The robotic arm is not holding anything. D. The robotic arm is holding block_5 and The robotic arm is empty.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_4 is currently situated under the block block_1 and The robotic arm is holding block_4", "The block block_3 is on top of block block_2", "The block block_1 is currently being held by the robotic arm and The robotic arm is not holding anything", "The robotic arm is holding block_5 and The robotic arm is empty"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -6943404682678957601, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_4, block_2, and block_1. The following block(s) is stacked on top of another block: block_5 is on block_1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The block block_2 is currently situated above the block block_3 and Block block_2 is on the table. B. The block block_4 is currently situated above the block block_3 and The block block_1 is on top of block block_3. C. No blocks are placed on top of block_3 and The block block_2 is currently situated under the block block_3. D. The robotic arm is holding block_4 and The block block_2 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_2 is currently situated above the block block_3 and Block block_2 is on the table", "The block block_4 is currently situated above the block block_3 and The block block_1 is on top of block block_3", "No blocks are placed on top of block_3 and The block block_2 is currently situated under the block block_3", "The robotic arm is holding block_4 and The block block_2 is currently being held by the robotic arm"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -8065048182490831850, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_5 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_5 and block_3 is on block_1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The block block_1 is currently situated under the block block_2 and The block block_2 is on top of block block_2. B. The block block_1 is on top of block block_1 and Block block_1 is placed on the table. C. The robotic arm is holding block_4 and Block block_2 is located on the table. D. The robotic arm is holding block_4 and The robotic arm is empty.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_1 is currently situated under the block block_2 and The block block_2 is on top of block block_2", "The block block_1 is on top of block block_1 and Block block_1 is placed on the table", "The robotic arm is holding block_4 and Block block_2 is located on the table", "The robotic arm is holding block_4 and The robotic arm is empty"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 1703069710521058487, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_4 and block_2. The following block(s) are stacked on top of another block: block_1 is on block_5, block_3 is on block_4, and block_5 is on block_2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The block block_3 is on top of block block_1 and No blocks are placed on top of block_4. B. The block block_5 is currently situated under the block block_1 and The block block_5 is currently situated under the block block_3. C. The block block_5 is currently situated above the block block_4 and Block block_5 is located on the table. D. The block block_4 is on top of block block_3 and Block block_3 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_3 is on top of block block_1 and No blocks are placed on top of block_4", "The block block_5 is currently situated under the block block_1 and The block block_5 is currently situated under the block block_3", "The block block_5 is currently situated above the block block_4 and Block block_5 is located on the table", "The block block_4 is on top of block block_3 and Block block_3 is clear"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
