{"id": -8354883169866086539, "group": "reachable_action_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 9 hoists, 9 pallets, 2 distributors, 7 depots, 2 crates, numbered consecutively. Currently, pallet1, pallet7, pallet5, pallet4, pallet0, crate1, pallet2, crate0, and pallet6 are clear; hoist1, hoist0, hoist4, hoist2, hoist5, hoist3, hoist7, hoist8, and hoist6 are available; hoist6 is at depot6, hoist5 is at depot5, hoist7 is at distributor0, pallet4 is at depot4, hoist3 is at depot3, pallet0 is at depot0, crate1 is at depot3, hoist2 is at depot2, crate0 is at distributor1, hoist0 is at depot0, pallet5 is at depot5, truck0 is at distributor1, hoist1 is at depot1, pallet6 is at depot6, hoist8 is at distributor1, pallet3 is at depot3, pallet2 is at depot2, pallet8 is at distributor1, pallet7 is at distributor0, hoist4 is at depot4, pallet1 is at depot1, and truck1 is at depot4; crate1 is on pallet3 and crate0 is on pallet8. The available actions are: (drive ?x ?y ?z) - drive truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the ground ?z at position ?p using the hoist ?x, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x and place it on the surface ?z at location ?p, (load ?x ?y ?z ?p) - load the crate ?y into the truck ?z at the place ?p with the hoist ?x, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(lift hoist7 crate0 pallet0 distributor0)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-7-2-2-9-9-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 - pallet truck0 truck1 - truck)\n    (:init (at crate0 distributor1) (at crate1 depot3) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 distributor0) (at hoist8 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 distributor0) (at pallet8 distributor1) (at truck0 distributor1) (at truck1 depot4) (available hoist0) (available hoist1) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (clear crate0) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (on crate0 pallet8) (on crate1 pallet3))\n    (:goal (and (on crate0 pallet8) (on crate1 pallet3)))\n)"}
{"id": -1502084099440528518, "group": "reachable_action_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 12 hoists, 12 pallets, 2 distributors, 10 depots, 2 crates, numbered consecutively. Currently, pallet1, pallet7, pallet5, pallet4, pallet3, pallet0, crate1, pallet10, pallet2, pallet8, pallet11, and pallet6 are clear; hoist1, hoist0, hoist4, hoist2, hoist5, hoist10, hoist3, hoist7, hoist9, hoist8, and hoist6 are available; hoist6 is at depot6, pallet10 is at distributor0, hoist5 is at depot5, pallet9 is at depot9, hoist8 is at depot8, pallet7 is at depot7, hoist7 is at depot7, pallet4 is at depot4, hoist3 is at depot3, pallet0 is at depot0, pallet8 is at depot8, hoist10 is at distributor0, hoist2 is at depot2, hoist0 is at depot0, crate1 is at depot9, pallet5 is at depot5, truck0 is at distributor1, pallet11 is at distributor1, hoist1 is at depot1, pallet6 is at depot6, hoist9 is at depot9, pallet3 is at depot3, pallet2 is at depot2, hoist11 is at distributor1, hoist4 is at depot4, pallet1 is at depot1, and truck1 is at depot4; crate1 is on pallet9; hoist11 is lifting crate0. The available actions are: (drive ?x ?y ?z) - drive truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - use hoist ?x to lift crate ?y from surface ?z at place ?p, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x and place it on the surface ?z at location ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(lift hoist8 crate1 pallet0 depot8)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-10-2-2-12-12-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 depot7 depot8 depot9 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist10 hoist11 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 hoist9 - hoist pallet0 pallet1 pallet10 pallet11 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 pallet9 - pallet truck0 truck1 - truck)\n    (:init (at crate1 depot9) (at hoist0 depot0) (at hoist1 depot1) (at hoist10 distributor0) (at hoist11 distributor1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 depot7) (at hoist8 depot8) (at hoist9 depot9) (at pallet0 depot0) (at pallet1 depot1) (at pallet10 distributor0) (at pallet11 distributor1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 depot7) (at pallet8 depot8) (at pallet9 depot9) (at truck0 distributor1) (at truck1 depot4) (available hoist0) (available hoist1) (available hoist10) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (available hoist9) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet10) (clear pallet11) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (lifting hoist11 crate0) (on crate1 pallet9))\n    (:goal (and (on crate0 pallet11) (on crate1 pallet9)))\n)"}
{"id": 4128250663720039480, "group": "reachable_action_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 12 hoists, 12 pallets, 2 distributors, 10 depots, 2 crates, numbered consecutively. Currently, pallet1, pallet9, pallet7, pallet5, pallet4, pallet3, pallet0, crate1, pallet10, pallet8, pallet11, and pallet6 are clear; hoist1, hoist0, hoist4, hoist2, hoist5, hoist10, hoist3, hoist11, hoist7, hoist9, hoist8, and hoist6 are available; hoist6 is at depot6, pallet10 is at distributor0, hoist5 is at depot5, pallet9 is at depot9, hoist8 is at depot8, pallet7 is at depot7, hoist7 is at depot7, pallet4 is at depot4, hoist3 is at depot3, pallet0 is at depot0, pallet8 is at depot8, hoist10 is at distributor0, hoist2 is at depot2, hoist0 is at depot0, pallet5 is at depot5, truck0 is at distributor1, pallet11 is at distributor1, truck1 is at depot2, crate1 is at depot2, hoist1 is at depot1, pallet6 is at depot6, hoist9 is at depot9, pallet3 is at depot3, pallet2 is at depot2, hoist11 is at distributor1, hoist4 is at depot4, and pallet1 is at depot1; crate1 is on pallet2; crate0 is in truck0. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from the place ?y to the place ?z, (lift ?x ?y ?z ?p) - lift crate ?y from surface ?z at place ?p using hoist ?x, (drop ?x ?y ?z ?p) - drop crate ?y from hoist ?x onto surface ?z at place ?p, (load ?x ?y ?z ?p) - load the crate ?y from place ?p with hoist ?x into the truck ?z, and (unload ?x ?y ?z ?p) - unload the crate ?y from the truck ?z at the place ?p using the hoist ?x.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(lift hoist8 crate1 pallet0 depot8)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-10-2-2-12-12-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 depot7 depot8 depot9 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist10 hoist11 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 hoist9 - hoist pallet0 pallet1 pallet10 pallet11 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 pallet9 - pallet truck0 truck1 - truck)\n    (:init (at crate1 depot2) (at hoist0 depot0) (at hoist1 depot1) (at hoist10 distributor0) (at hoist11 distributor1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 depot7) (at hoist8 depot8) (at hoist9 depot9) (at pallet0 depot0) (at pallet1 depot1) (at pallet10 distributor0) (at pallet11 distributor1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 depot7) (at pallet8 depot8) (at pallet9 depot9) (at truck0 distributor1) (at truck1 depot2) (available hoist0) (available hoist1) (available hoist10) (available hoist11) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (available hoist9) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet10) (clear pallet11) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (clear pallet9) (in crate0 truck0) (on crate1 pallet2))\n    (:goal (and (on crate0 pallet11) (on crate1 pallet9)))\n)"}
{"id": 970909168843136154, "group": "reachable_action_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 5 hoists, 5 pallets, 2 distributors, 3 depots, 2 crates, numbered consecutively. Currently, pallet1, pallet4, pallet3, pallet0, and crate1 are clear; hoist0, hoist4, hoist2, and hoist3 are available; truck1 is at distributor1, hoist4 is at distributor1, pallet3 is at distributor0, pallet0 is at depot0, hoist2 is at depot2, pallet4 is at distributor1, hoist0 is at depot0, truck0 is at distributor1, crate1 is at depot2, hoist3 is at distributor0, hoist1 is at depot1, pallet2 is at depot2, and pallet1 is at depot1; crate1 is on pallet2; hoist1 is lifting crate0. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from place ?y to place ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the surface ?z at place ?p using the hoist ?x, (drop ?x ?y ?z ?p) - place the crate ?y on the surface ?z at the place ?p using the hoist ?x, (load ?x ?y ?z ?p) - load crate ?y into truck ?z at place ?p with hoist ?x, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(lift hoist3 crate1 pallet0 distributor0)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-3-2-2-5-5-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 - pallet truck0 truck1 - truck)\n    (:init (at crate1 depot2) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 distributor0) (at hoist4 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 distributor0) (at pallet4 distributor1) (at truck0 distributor1) (at truck1 distributor1) (available hoist0) (available hoist2) (available hoist3) (available hoist4) (clear crate1) (clear pallet0) (clear pallet1) (clear pallet3) (clear pallet4) (lifting hoist1 crate0) (on crate1 pallet2))\n    (:goal (and (on crate0 pallet1) (on crate1 pallet2)))\n)"}
{"id": 6667007202357828203, "group": "reachable_action_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 6 hoists, 6 pallets, 2 distributors, 4 depots, 3 crates, numbered consecutively. Currently, pallet1, pallet5, pallet4, pallet3, pallet0, and pallet2 are clear; hoist0, hoist4, hoist2, hoist5, and hoist3 are available; hoist5 is at distributor1, hoist4 is at distributor0, truck0 is at depot3, pallet5 is at distributor1, hoist3 is at depot3, pallet0 is at depot0, hoist2 is at depot2, hoist0 is at depot0, truck1 is at depot2, hoist1 is at depot1, pallet4 is at distributor0, pallet3 is at depot3, pallet2 is at depot2, and pallet1 is at depot1; crate0 is in truck0 and crate1 is in truck0; hoist1 is lifting crate2. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from the place ?y to the place ?z, (lift ?x ?y ?z ?p) - lift crate ?y from surface ?z at place ?p using hoist ?x, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x and place it on the surface ?z at location ?p, (load ?x ?y ?z ?p) - load crate ?y into truck ?z at place ?p with hoist ?x, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(lift hoist4 crate1 crate1 distributor0)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - crate depot0 depot1 depot2 depot3 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - pallet truck0 truck1 - truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 depot3) (at truck1 depot2) (available hoist0) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (in crate0 truck0) (in crate1 truck0) (lifting hoist1 crate2))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": -4822583174260039323, "group": "reachable_action_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 12 hoists, 12 pallets, 2 distributors, 10 depots, 2 crates, numbered consecutively. Currently, pallet1, pallet9, pallet7, pallet5, pallet4, pallet3, pallet0, pallet10, pallet2, pallet8, pallet11, and pallet6 are clear; hoist1, hoist0, hoist4, hoist5, hoist10, hoist3, hoist11, hoist7, hoist9, hoist8, and hoist6 are available; hoist6 is at depot6, pallet10 is at distributor0, hoist5 is at depot5, pallet9 is at depot9, hoist8 is at depot8, pallet7 is at depot7, hoist7 is at depot7, pallet4 is at depot4, truck0 is at depot0, hoist3 is at depot3, pallet0 is at depot0, pallet8 is at depot8, truck1 is at depot8, hoist10 is at distributor0, hoist2 is at depot2, hoist0 is at depot0, pallet5 is at depot5, pallet11 is at distributor1, hoist1 is at depot1, pallet6 is at depot6, hoist9 is at depot9, pallet3 is at depot3, pallet2 is at depot2, hoist11 is at distributor1, hoist4 is at depot4, and pallet1 is at depot1; crate0 is in truck1; hoist2 is lifting crate1. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from location ?y to location ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the surface ?z at place ?p using the hoist ?x, (drop ?x ?y ?z ?p) - drop crate ?y from hoist ?x onto surface ?z at place ?p, (load ?x ?y ?z ?p) - load the crate ?y into the truck ?z at the place ?p with the hoist ?x, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(lift hoist8 crate1 pallet0 depot8)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-10-2-2-12-12-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 depot7 depot8 depot9 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist10 hoist11 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 hoist9 - hoist pallet0 pallet1 pallet10 pallet11 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 pallet9 - pallet truck0 truck1 - truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist10 distributor0) (at hoist11 distributor1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 depot7) (at hoist8 depot8) (at hoist9 depot9) (at pallet0 depot0) (at pallet1 depot1) (at pallet10 distributor0) (at pallet11 distributor1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 depot7) (at pallet8 depot8) (at pallet9 depot9) (at truck0 depot0) (at truck1 depot8) (available hoist0) (available hoist1) (available hoist10) (available hoist11) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (available hoist9) (clear pallet0) (clear pallet1) (clear pallet10) (clear pallet11) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet8) (clear pallet9) (in crate0 truck1) (lifting hoist2 crate1))\n    (:goal (and (on crate0 pallet11) (on crate1 pallet9)))\n)"}
{"id": 5737122068991743952, "group": "reachable_action_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 5 hoists, 5 pallets, 2 distributors, 3 depots, 2 crates, numbered consecutively. Currently, pallet4, pallet3, pallet0, pallet2, and crate0 are clear; hoist1, hoist0, hoist4, and hoist3 are available; truck1 is at distributor1, hoist4 is at distributor1, truck0 is at depot2, pallet3 is at distributor0, pallet0 is at depot0, crate0 is at depot1, hoist2 is at depot2, pallet4 is at distributor1, hoist0 is at depot0, hoist3 is at distributor0, hoist1 is at depot1, pallet2 is at depot2, and pallet1 is at depot1; crate0 is on pallet1; hoist2 is lifting crate1. The available actions are: (drive ?x ?y ?z) - drive the truck ?x from ?y to ?z, (lift ?x ?y ?z ?p) - use hoist ?x to lift crate ?y from surface ?z at place ?p, (drop ?x ?y ?z ?p) - drop crate ?y from hoist ?x onto surface ?z at place ?p, (load ?x ?y ?z ?p) - use hoist ?x to load crate ?y into truck ?z at place ?p, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(lift hoist3 crate1 pallet0 distributor0)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-3-2-2-5-5-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 - pallet truck0 truck1 - truck)\n    (:init (at crate0 depot1) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 distributor0) (at hoist4 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 distributor0) (at pallet4 distributor1) (at truck0 depot2) (at truck1 distributor1) (available hoist0) (available hoist1) (available hoist3) (available hoist4) (clear crate0) (clear pallet0) (clear pallet2) (clear pallet3) (clear pallet4) (lifting hoist2 crate1) (on crate0 pallet1))\n    (:goal (and (on crate0 pallet1) (on crate1 pallet2)))\n)"}
{"id": -8612443193742777751, "group": "reachable_action_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 6 hoists, 6 pallets, 2 distributors, 4 depots, 3 crates, numbered consecutively. Currently, pallet1, pallet5, pallet4, pallet3, pallet0, and pallet2 are clear; hoist0, hoist2, and hoist3 are available; truck1 is at distributor0, hoist5 is at distributor1, hoist4 is at distributor0, pallet5 is at distributor1, hoist3 is at depot3, pallet0 is at depot0, hoist2 is at depot2, hoist0 is at depot0, truck0 is at distributor1, hoist1 is at depot1, pallet4 is at distributor0, pallet3 is at depot3, pallet2 is at depot2, and pallet1 is at depot1; hoist1 is lifting crate0, hoist5 is lifting crate1, and hoist4 is lifting crate2. The available actions are: (drive ?x ?y ?z) - navigate the truck ?x from the place ?y to the place ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the surface ?z at place ?p using the hoist ?x, (drop ?x ?y ?z ?p) - place the crate ?y on the surface ?z at the place ?p using the hoist ?x, (load ?x ?y ?z ?p) - load the crate ?y into the truck ?z at the place ?p with the hoist ?x, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(lift hoist4 crate1 crate1 distributor0)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - crate depot0 depot1 depot2 depot3 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - pallet truck0 truck1 - truck)\n    (:init (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 distributor1) (at truck1 distributor0) (available hoist0) (available hoist2) (available hoist3) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (lifting hoist1 crate0) (lifting hoist4 crate2) (lifting hoist5 crate1))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": 140047170288551942, "group": "reachable_action_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 6 hoists, 6 pallets, 2 distributors, 4 depots, 3 crates, numbered consecutively. Currently, pallet1, pallet5, pallet3, crate2, pallet0, and pallet2 are clear; hoist0, hoist4, hoist2, hoist5, and hoist3 are available; hoist5 is at distributor1, hoist4 is at distributor0, truck1 is at depot1, crate2 is at distributor0, pallet5 is at distributor1, hoist3 is at depot3, pallet0 is at depot0, hoist2 is at depot2, hoist0 is at depot0, truck0 is at distributor1, hoist1 is at depot1, pallet4 is at distributor0, pallet3 is at depot3, pallet2 is at depot2, and pallet1 is at depot1; crate2 is on pallet4; crate1 is in truck0; hoist1 is lifting crate0. The available actions are: (drive ?x ?y ?z) - drive the truck ?x from ?y to ?z, (lift ?x ?y ?z ?p) - use the hoist ?x to lift the crate ?y from the surface ?z at location ?p, (drop ?x ?y ?z ?p) - drop crate ?y from hoist ?x onto surface ?z at place ?p, (load ?x ?y ?z ?p) - use hoist ?x to load crate ?y into truck ?z at place ?p, and (unload ?x ?y ?z ?p) - use the hoist ?x to unload the crate ?y from the truck ?z at location ?p.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(lift hoist4 crate1 crate1 distributor0)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-4-2-2-6-6-3)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 crate2 - crate depot0 depot1 depot2 depot3 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist2 hoist3 hoist4 hoist5 - hoist pallet0 pallet1 pallet2 pallet3 pallet4 pallet5 - pallet truck0 truck1 - truck)\n    (:init (at crate2 distributor0) (at hoist0 depot0) (at hoist1 depot1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 distributor0) (at hoist5 distributor1) (at pallet0 depot0) (at pallet1 depot1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 distributor0) (at pallet5 distributor1) (at truck0 distributor1) (at truck1 depot1) (available hoist0) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (clear crate2) (clear pallet0) (clear pallet1) (clear pallet2) (clear pallet3) (clear pallet5) (in crate1 truck0) (lifting hoist1 crate0) (on crate2 pallet4))\n    (:goal (and (on crate0 pallet1) (on crate1 crate2) (on crate2 pallet4)))\n)"}
{"id": -3638779677208655227, "group": "reachable_action_gen", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 12 hoists, 12 pallets, 2 distributors, 10 depots, 2 crates, numbered consecutively. Currently, pallet1, pallet9, pallet7, pallet5, pallet4, pallet3, pallet0, pallet10, pallet2, crate0, pallet11, and pallet6 are clear; hoist1, hoist0, hoist4, hoist2, hoist5, hoist10, hoist3, hoist11, hoist7, hoist9, hoist8, and hoist6 are available; crate0 is at depot8, hoist6 is at depot6, pallet10 is at distributor0, hoist5 is at depot5, truck0 is at depot9, pallet9 is at depot9, hoist8 is at depot8, pallet7 is at depot7, hoist7 is at depot7, pallet4 is at depot4, hoist3 is at depot3, pallet0 is at depot0, pallet8 is at depot8, hoist10 is at distributor0, hoist2 is at depot2, hoist0 is at depot0, pallet5 is at depot5, pallet11 is at distributor1, hoist1 is at depot1, pallet6 is at depot6, hoist9 is at depot9, pallet3 is at depot3, pallet2 is at depot2, hoist11 is at distributor1, hoist4 is at depot4, pallet1 is at depot1, and truck1 is at depot4; crate0 is on pallet8; crate1 is in truck0. The available actions are: (drive ?x ?y ?z) - drive the truck ?x from ?y to ?z, (lift ?x ?y ?z ?p) - lift the crate ?y from the surface ?z at place ?p using the hoist ?x, (drop ?x ?y ?z ?p) - lower the crate ?y from the hoist ?x and place it on the surface ?z at location ?p, (load ?x ?y ?z ?p) - use the hoist ?x to lift and place the crate ?y from place ?p into the truck ?z, and (unload ?x ?y ?z ?p) - unload crate ?y from truck ?z at place ?p using hoist ?x.", "question": "What action can never become applicable, in any state reachable from the current state?", "answer": ["(lift hoist8 crate1 pallet0 depot8)"], "PDDL_domain": "(define (domain depots)\n(:requirements :strips :typing)\n(:types place locatable - object\n\tDepot Distributor - place\n        Truck Hoist Surface - locatable\n        Pallet Crate - Surface)\n\n(:predicates (at ?x - locatable ?y - place)\n             (on ?x - Crate ?y - Surface)\n             (in ?x - Crate ?y - Truck)\n             (lifting ?x - Hoist ?y - Crate)\n             (available ?x - Hoist)\n             (clear ?x - Surface))\n\n(:action drive\n  :parameters (?x - Truck ?y - place ?z - place)\n  :precondition (and (at ?x ?y))\n  :effect (and (not (at ?x ?y)) (at ?x ?z)))\n\n(:action lift\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (available ?x) (at ?y ?p) (on ?y ?z) (clear ?y))\n  :effect (and (not (at ?y ?p)) (lifting ?x ?y) (not (clear ?y)) (not (available ?x)) (clear ?z) (not (on ?y ?z))))\n\n(:action drop\n  :parameters (?x - Hoist ?y - Crate ?z - Surface ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (clear ?z) (lifting ?x ?y))\n  :effect (and (available ?x) (not (lifting ?x ?y)) (at ?y ?p) (not (clear ?z)) (clear ?y)(on ?y ?z)))\n\n(:action load\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (lifting ?x ?y))\n  :effect (and (not (lifting ?x ?y)) (in ?y ?z) (available ?x)))\n\n(:action unload\n  :parameters (?x - Hoist ?y - Crate ?z - Truck ?p - place)\n  :precondition (and (at ?x ?p) (at ?z ?p) (available ?x) (in ?y ?z))\n  :effect (and (not (in ?y ?z)) (not (available ?x)) (lifting ?x ?y)))\n)", "PDDL_problem": "(define (problem depot-10-2-2-12-12-2)\n    (:domain depots)\n    (:requirements :strips :typing)\n    (:objects crate0 crate1 - crate depot0 depot1 depot2 depot3 depot4 depot5 depot6 depot7 depot8 depot9 - depot distributor0 distributor1 - distributor hoist0 hoist1 hoist10 hoist11 hoist2 hoist3 hoist4 hoist5 hoist6 hoist7 hoist8 hoist9 - hoist pallet0 pallet1 pallet10 pallet11 pallet2 pallet3 pallet4 pallet5 pallet6 pallet7 pallet8 pallet9 - pallet truck0 truck1 - truck)\n    (:init (at crate0 depot8) (at hoist0 depot0) (at hoist1 depot1) (at hoist10 distributor0) (at hoist11 distributor1) (at hoist2 depot2) (at hoist3 depot3) (at hoist4 depot4) (at hoist5 depot5) (at hoist6 depot6) (at hoist7 depot7) (at hoist8 depot8) (at hoist9 depot9) (at pallet0 depot0) (at pallet1 depot1) (at pallet10 distributor0) (at pallet11 distributor1) (at pallet2 depot2) (at pallet3 depot3) (at pallet4 depot4) (at pallet5 depot5) (at pallet6 depot6) (at pallet7 depot7) (at pallet8 depot8) (at pallet9 depot9) (at truck0 depot9) (at truck1 depot4) (available hoist0) (available hoist1) (available hoist10) (available hoist11) (available hoist2) (available hoist3) (available hoist4) (available hoist5) (available hoist6) (available hoist7) (available hoist8) (available hoist9) (clear crate0) (clear pallet0) (clear pallet1) (clear pallet10) (clear pallet11) (clear pallet2) (clear pallet3) (clear pallet4) (clear pallet5) (clear pallet6) (clear pallet7) (clear pallet9) (in crate1 truck0) (on crate0 pallet8))\n    (:goal (and (on crate0 pallet11) (on crate1 pallet9)))\n)"}
