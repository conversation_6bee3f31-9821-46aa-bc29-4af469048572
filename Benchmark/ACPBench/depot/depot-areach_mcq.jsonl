{"id": 3749773197430832624, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 6 hoists, 3 crates, 2 trucks, 4 depots, 6 pallets, numbered consecutively. Currently, pallet4, pallet2, pallet1, pallet3, pallet5, and pallet0 are clear; hoist2, hoist0, hoist3, hoist1, hoist5, and hoist4 are available; hoist1 is at depot1, pallet0 is at depot0, hoist2 is at depot2, pallet5 is at distributor1, pallet3 is at depot3, hoist3 is at depot3, pallet4 is at distributor0, truck1 is at depot1, hoist0 is at depot0, hoist5 is at distributor1, pallet1 is at depot1, pallet2 is at depot2, truck0 is at depot1, and hoist4 is at distributor0; crate2 is in truck0, crate1 is in truck0, and crate0 is in truck1.", "question": "Which of the following actions can eventually be applied? A. drop crate crate2 from hoist hoist5 onto surface crate2 at place distributor1. B. navigate the truck truck0 from the place distributor0 to the place depot0. C. drop crate crate0 from hoist hoist0 onto surface crate0 at place depot0. D. drop crate crate0 from hoist hoist3 onto surface crate0 at place depot3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drop crate crate2 from hoist hoist5 onto surface crate2 at place distributor1", "navigate the truck truck0 from the place distributor0 to the place depot0", "drop crate crate0 from hoist hoist0 onto surface crate0 at place depot0", "drop crate crate0 from hoist hoist3 onto surface crate0 at place depot3"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -8538385564574127774, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 5 hoists, 2 crates, 2 trucks, 3 depots, 5 pallets, numbered consecutively. Currently, pallet4, pallet2, pallet3, crate0, and pallet0 are clear; hoist2, hoist0, hoist3, hoist1, and hoist4 are available; hoist1 is at depot1, pallet0 is at depot0, hoist2 is at depot2, truck0 is at depot2, pallet4 is at distributor1, hoist4 is at distributor1, hoist0 is at depot0, hoist3 is at distributor0, crate0 is at depot1, pallet3 is at distributor0, pallet1 is at depot1, truck1 is at distributor0, and pallet2 is at depot2; crate0 is on pallet1; crate1 is in truck0.", "question": "Which of the following actions can eventually be applied? A. lift the crate crate0 from the ground crate0 at position distributor0 using the hoist hoist3. B. drive the truck truck0 from depot1 to depot0. C. lift the crate crate1 from the ground crate1 at position distributor1 using the hoist hoist4. D. place the crate crate0 on the surface crate0 at the place depot0 using the hoist hoist0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["lift the crate crate0 from the ground crate0 at position distributor0 using the hoist hoist3", "drive the truck truck0 from depot1 to depot0", "lift the crate crate1 from the ground crate1 at position distributor1 using the hoist hoist4", "place the crate crate0 on the surface crate0 at the place depot0 using the hoist hoist0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 6127648645863522488, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 5 hoists, 2 crates, 2 trucks, 3 depots, 5 pallets, numbered consecutively. Currently, pallet4, pallet2, pallet3, crate0, and pallet0 are clear; hoist2, hoist0, hoist3, hoist1, and hoist4 are available; hoist1 is at depot1, pallet0 is at depot0, hoist2 is at depot2, pallet4 is at distributor1, hoist4 is at distributor1, truck1 is at depot2, hoist0 is at depot0, hoist3 is at distributor0, crate0 is at depot1, pallet3 is at distributor0, truck0 is at distributor1, pallet1 is at depot1, and pallet2 is at depot2; crate0 is on pallet1; crate1 is in truck0.", "question": "Which of the following actions can eventually be applied? A. drop the crate crate0 from the hoist hoist3 onto the surface crate0 at the place distributor0. B. lift the crate crate1 from the ground crate1 at position depot2 using the hoist hoist2. C. navigate the truck truck0 from place depot2 to place depot1. D. lift the crate crate1 from the ground crate1 at position distributor0 using the hoist hoist3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drop the crate crate0 from the hoist hoist3 onto the surface crate0 at the place distributor0", "lift the crate crate1 from the ground crate1 at position depot2 using the hoist hoist2", "navigate the truck truck0 from place depot2 to place depot1", "lift the crate crate1 from the ground crate1 at position distributor0 using the hoist hoist3"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -2607126123297445134, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 4 hoists, 2 crates, 2 trucks, 2 depots, 4 pallets, numbered consecutively. Currently, pallet2, pallet1, pallet3, and crate0 are clear; hoist2, hoist0, hoist3, and hoist1 are available; hoist1 is at depot1, pallet0 is at depot0, hoist2 is at distributor0, pallet3 is at distributor1, hoist3 is at distributor1, hoist0 is at depot0, truck0 is at depot0, pallet2 is at distributor0, pallet1 is at depot1, crate0 is at depot0, and truck1 is at distributor1; crate0 is on pallet0; crate1 is in truck0.", "question": "Which of the following actions can eventually be applied? A. lift the crate crate0 from the surface crate0 at place depot1 using the hoist hoist1. B. lift the crate crate0 from the surface crate0 at place distributor1 using the hoist hoist3. C. drop crate crate0 from hoist hoist1 onto surface crate0 at place depot1. D. drive the truck truck1 from distributor0 to depot1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["lift the crate crate0 from the surface crate0 at place depot1 using the hoist hoist1", "lift the crate crate0 from the surface crate0 at place distributor1 using the hoist hoist3", "drop crate crate0 from hoist hoist1 onto surface crate0 at place depot1", "drive the truck truck1 from distributor0 to depot1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 836843583898960047, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 6 hoists, 3 crates, 2 trucks, 4 depots, 6 pallets, numbered consecutively. Currently, pallet4, pallet2, pallet1, pallet3, pallet5, and pallet0 are clear; hoist2, hoist0, hoist3, hoist5, and hoist4 are available; hoist1 is at depot1, pallet0 is at depot0, hoist2 is at depot2, pallet5 is at distributor1, pallet3 is at depot3, hoist3 is at depot3, pallet4 is at distributor0, hoist0 is at depot0, truck1 is at depot3, hoist5 is at distributor1, pallet1 is at depot1, pallet2 is at depot2, truck0 is at depot1, and hoist4 is at distributor0; crate1 is in truck0 and crate0 is in truck0; hoist1 is lifting crate2.", "question": "Which of the following actions can eventually be applied? A. lower the crate crate2 from the hoist hoist1 onto the surface crate2 at the place depot1. B. drive the truck truck1 from distributor1 to depot2. C. lower the crate crate1 from the hoist hoist2 onto the surface crate1 at the place depot2. D. lower the crate crate0 from the hoist hoist4 onto the surface crate0 at the place distributor0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["lower the crate crate2 from the hoist hoist1 onto the surface crate2 at the place depot1", "drive the truck truck1 from distributor1 to depot2", "lower the crate crate1 from the hoist hoist2 onto the surface crate1 at the place depot2", "lower the crate crate0 from the hoist hoist4 onto the surface crate0 at the place distributor0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 1685508010509923358, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 4 hoists, 2 crates, 2 trucks, 2 depots, 4 pallets, numbered consecutively. Currently, pallet2, pallet1, pallet3, and pallet0 are clear; hoist2, hoist0, hoist3, and hoist1 are available; hoist1 is at depot1, truck1 is at depot0, pallet0 is at depot0, hoist2 is at distributor0, pallet3 is at distributor1, hoist3 is at distributor1, hoist0 is at depot0, truck0 is at depot0, pallet2 is at distributor0, and pallet1 is at depot1; crate1 is in truck1 and crate0 is in truck0.", "question": "Which of the following actions can eventually be applied? A. place the crate crate0 on the surface crate0 at the place distributor0 using the hoist hoist2. B. place the crate crate0 on the surface crate0 at the place distributor1 using the hoist hoist3. C. lift crate crate1 from surface crate1 at place depot0 using hoist hoist0. D. navigate the truck truck0 from place depot1 to place distributor1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the crate crate0 on the surface crate0 at the place distributor0 using the hoist hoist2", "place the crate crate0 on the surface crate0 at the place distributor1 using the hoist hoist3", "lift crate crate1 from surface crate1 at place depot0 using hoist hoist0", "navigate the truck truck0 from place depot1 to place distributor1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -39542021048999593, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 4 hoists, 2 crates, 2 trucks, 2 depots, 4 pallets, numbered consecutively. Currently, crate1, pallet2, pallet1, and crate0 are clear; hoist2, hoist0, hoist3, and hoist1 are available; hoist1 is at depot1, truck1 is at depot0, pallet0 is at depot0, hoist2 is at distributor0, pallet3 is at distributor1, hoist3 is at distributor1, hoist0 is at depot0, truck0 is at depot0, pallet2 is at distributor0, crate1 is at distributor1, pallet1 is at depot1, and crate0 is at depot0; crate1 is on pallet3 and crate0 is on pallet0.", "question": "Which of the following actions can eventually be applied? A. drop crate crate1 from hoist hoist0 onto surface crate1 at place depot0. B. lift the crate crate0 from the ground crate0 at position depot0 using the hoist hoist0. C. lift the crate crate1 from the ground crate1 at position depot1 using the hoist hoist1. D. drive truck truck0 from place distributor1 to place depot1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drop crate crate1 from hoist hoist0 onto surface crate1 at place depot0", "lift the crate crate0 from the ground crate0 at position depot0 using the hoist hoist0", "lift the crate crate1 from the ground crate1 at position depot1 using the hoist hoist1", "drive truck truck0 from place distributor1 to place depot1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 2892472738699186528, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 9 hoists, 2 crates, 2 trucks, 7 depots, 9 pallets, numbered consecutively. Currently, crate1, pallet6, pallet4, pallet7, pallet2, pallet1, pallet8, pallet5, and pallet0 are clear; hoist7, hoist2, hoist0, hoist3, hoist5, hoist6, hoist8, and hoist4 are available; hoist1 is at depot1, truck1 is at depot0, pallet0 is at depot0, hoist2 is at depot2, pallet5 is at depot5, pallet3 is at depot3, pallet6 is at depot6, hoist3 is at depot3, hoist5 is at depot5, hoist6 is at depot6, hoist4 is at depot4, hoist0 is at depot0, truck0 is at depot6, pallet7 is at distributor0, hoist7 is at distributor0, hoist8 is at distributor1, pallet1 is at depot1, pallet4 is at depot4, crate1 is at depot3, pallet8 is at distributor1, and pallet2 is at depot2; crate1 is on pallet3; hoist1 is lifting crate0.", "question": "Which of the following actions can eventually be applied? A. drop the crate crate0 from the hoist hoist2 onto the surface crate0 at the place depot2. B. navigate the truck truck0 from the place depot4 to the place depot1. C. lift the crate crate0 from the ground crate0 at position depot2 using the hoist hoist2. D. drop the crate crate0 from the hoist hoist3 onto the surface crate0 at the place depot3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drop the crate crate0 from the hoist hoist2 onto the surface crate0 at the place depot2", "navigate the truck truck0 from the place depot4 to the place depot1", "lift the crate crate0 from the ground crate0 at position depot2 using the hoist hoist2", "drop the crate crate0 from the hoist hoist3 onto the surface crate0 at the place depot3"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -3897161493366048111, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 12 hoists, 2 crates, 2 trucks, 10 depots, 12 pallets, numbered consecutively. Currently, pallet6, pallet4, pallet7, pallet11, pallet9, pallet10, pallet2, pallet1, pallet8, pallet3, pallet5, and pallet0 are clear; hoist7, hoist2, hoist0, hoist3, hoist1, hoist5, hoist10, hoist6, hoist8, and hoist4 are available; hoist1 is at depot1, pallet0 is at depot0, hoist2 is at depot2, hoist7 is at depot7, pallet7 is at depot7, hoist10 is at distributor0, pallet5 is at depot5, pallet3 is at depot3, pallet9 is at depot9, pallet10 is at distributor0, hoist9 is at depot9, hoist3 is at depot3, pallet6 is at depot6, hoist5 is at depot5, hoist6 is at depot6, truck0 is at depot4, pallet11 is at distributor1, hoist8 is at depot8, hoist4 is at depot4, hoist0 is at depot0, truck1 is at depot6, hoist11 is at distributor1, pallet1 is at depot1, pallet4 is at depot4, pallet8 is at depot8, and pallet2 is at depot2; hoist11 is lifting crate0 and hoist9 is lifting crate1.", "question": "Which of the following actions can eventually be applied? A. lower the crate crate1 from the hoist hoist9 and place it on the surface crate1 at location depot9. B. lower the crate crate1 from the hoist hoist10 and place it on the surface crate1 at location distributor0. C. lift crate crate1 from surface crate1 at place depot3 using hoist hoist3. D. drive the truck truck0 from depot8 to depot6.", "choices": {"label": ["A", "B", "C", "D"], "text": ["lower the crate crate1 from the hoist hoist9 and place it on the surface crate1 at location depot9", "lower the crate crate1 from the hoist hoist10 and place it on the surface crate1 at location distributor0", "lift crate crate1 from surface crate1 at place depot3 using hoist hoist3", "drive the truck truck0 from depot8 to depot6"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 6903786467067770430, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 6 hoists, 3 crates, 2 trucks, 4 depots, 6 pallets, numbered consecutively. Currently, pallet4, pallet2, pallet1, pallet3, pallet5, and pallet0 are clear; hoist2, hoist0, hoist3, hoist5, and hoist4 are available; hoist1 is at depot1, pallet0 is at depot0, hoist2 is at depot2, pallet5 is at distributor1, pallet3 is at depot3, hoist3 is at depot3, pallet4 is at distributor0, hoist0 is at depot0, truck0 is at distributor1, hoist5 is at distributor1, pallet1 is at depot1, truck1 is at distributor0, pallet2 is at depot2, and hoist4 is at distributor0; crate2 is in truck1 and crate1 is in truck1; hoist1 is lifting crate0.", "question": "Which of the following actions can eventually be applied? A. lower the crate crate1 from the hoist hoist3 and place it on the surface crate1 at location depot3. B. lower the crate crate2 from the hoist hoist2 and place it on the surface crate2 at location depot2. C. lift the crate crate0 from the ground crate0 at position depot3 using the hoist hoist3. D. unload the crate crate2 from the truck truck1 at the place depot0 using the hoist hoist0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["lower the crate crate1 from the hoist hoist3 and place it on the surface crate1 at location depot3", "lower the crate crate2 from the hoist hoist2 and place it on the surface crate2 at location depot2", "lift the crate crate0 from the ground crate0 at position depot3 using the hoist hoist3", "unload the crate crate2 from the truck truck1 at the place depot0 using the hoist hoist0"]}, "query": "Which action is reachable from this state?", "answer": "D"}
