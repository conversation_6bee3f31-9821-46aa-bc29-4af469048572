{"id": -1507287662825823531, "group": "action_justification_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 12 pallets, 12 hoists, 2 trucks, 10 depots, numbered consecutively. Currently, pallet5, pallet11, pallet7, pallet0, pallet6, pallet4, pallet1, crate1, pallet3, crate0, pallet10, and pallet9 are clear; hoist2, hoist5, hoist1, hoist4, hoist3, hoist7, hoist11, hoist6, hoist0, hoist8, hoist9, and hoist10 are available; pallet3 is at depot3, hoist6 is at depot6, hoist9 is at depot9, hoist7 is at depot7, pallet7 is at depot7, pallet8 is at depot8, hoist11 is at distributor1, hoist2 is at depot2, pallet4 is at depot4, pallet11 is at distributor1, crate1 is at depot2, pallet1 is at depot1, pallet2 is at depot2, pallet9 is at depot9, truck0 is at depot0, hoist3 is at depot3, pallet5 is at depot5, pallet6 is at depot6, pallet10 is at distributor0, hoist10 is at distributor0, hoist1 is at depot1, truck1 is at depot4, hoist5 is at depot5, hoist0 is at depot0, pallet0 is at depot0, hoist4 is at depot4, hoist8 is at depot8, and crate0 is at depot8; crate0 is on pallet8 and crate1 is on pallet2. The goal is to reach a state where the following facts hold: crate0 is on pallet11 and crate1 is on pallet9.", "question": "Given the plan: \"navigate the truck truck0 from location depot0 to location depot6, navigate the truck truck0 from location depot6 to location depot0, navigate the truck truck0 from location depot0 to location depot2, lift the crate crate1 from the ground pallet2 at position depot2 using the hoist hoist2, load the crate crate1 from place depot2 with hoist hoist2 into the truck truck0, navigate the truck truck0 from location depot2 to location depot8, lift the crate crate0 from the ground pallet8 at position depot8 using the hoist hoist8, load the crate crate0 from place depot8 with hoist hoist8 into the truck truck0, navigate the truck truck0 from location depot8 to location distributor1, use the hoist hoist11 to unload the crate crate0 from the truck truck0 at location distributor1, navigate the truck truck0 from location distributor1 to location depot8, drop the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1, navigate the truck truck0 from location depot8 to location depot9, use the hoist hoist9 to unload the crate crate1 from the truck truck0 at location depot9, drop the crate crate1 from the hoist hoist9 onto the surface pallet9 at the place depot9\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. navigate the truck truck0 from location depot8 to location depot9 and use the hoist hoist9 to unload the crate crate1 from the truck truck0 at location depot9. B. load the crate crate1 from place depot2 with hoist hoist2 into the truck truck0 and navigate the truck truck0 from location depot2 to location depot8. C. navigate the truck truck0 from location depot0 to location depot6 and navigate the truck truck0 from location depot6 to location depot0. D. lift the crate crate0 from the ground pallet8 at position depot8 using the hoist hoist8 and load the crate crate0 from place depot8 with hoist hoist8 into the truck truck0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["navigate the truck truck0 from location depot8 to location depot9 and use the hoist hoist9 to unload the crate crate1 from the truck truck0 at location depot9", "load the crate crate1 from place depot2 with hoist hoist2 into the truck truck0 and navigate the truck truck0 from location depot2 to location depot8", "navigate the truck truck0 from location depot0 to location depot6 and navigate the truck truck0 from location depot6 to location depot0", "lift the crate crate0 from the ground pallet8 at position depot8 using the hoist hoist8 and load the crate crate0 from place depot8 with hoist hoist8 into the truck truck0"]}, "query": "Given the plan: \"navigate the truck truck0 from location depot0 to location depot6, navigate the truck truck0 from location depot6 to location depot0, navigate the truck truck0 from location depot0 to location depot2, lift the crate crate1 from the ground pallet2 at position depot2 using the hoist hoist2, load the crate crate1 from place depot2 with hoist hoist2 into the truck truck0, navigate the truck truck0 from location depot2 to location depot8, lift the crate crate0 from the ground pallet8 at position depot8 using the hoist hoist8, load the crate crate0 from place depot8 with hoist hoist8 into the truck truck0, navigate the truck truck0 from location depot8 to location distributor1, use the hoist hoist11 to unload the crate crate0 from the truck truck0 at location distributor1, navigate the truck truck0 from location distributor1 to location depot8, drop the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1, navigate the truck truck0 from location depot8 to location depot9, use the hoist hoist9 to unload the crate crate1 from the truck truck0 at location depot9, drop the crate crate1 from the hoist hoist9 onto the surface pallet9 at the place depot9\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": 5064311595620686267, "group": "action_justification_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 9 pallets, 9 hoists, 2 trucks, 7 depots, numbered consecutively. Currently, pallet5, pallet7, pallet0, pallet2, pallet8, pallet6, pallet4, crate1, and crate0 are clear; hoist2, hoist5, hoist1, hoist4, hoist3, hoist7, hoist6, hoist0, and hoist8 are available; pallet3 is at depot3, hoist6 is at depot6, crate1 is at depot3, pallet8 is at distributor1, hoist2 is at depot2, pallet4 is at depot4, hoist8 is at distributor1, pallet1 is at depot1, pallet2 is at depot2, hoist7 is at distributor0, crate0 is at depot1, truck0 is at depot2, hoist3 is at depot3, pallet5 is at depot5, pallet6 is at depot6, hoist1 is at depot1, hoist5 is at depot5, hoist0 is at depot0, pallet0 is at depot0, hoist4 is at depot4, truck1 is at depot0, and pallet7 is at distributor0; crate1 is on pallet3 and crate0 is on pallet1. The goal is to reach a state where the following facts hold: crate0 is on pallet8 and crate1 is on pallet3.", "question": "Given the plan: \"drive truck truck1 from place depot0 to place depot1, lift the crate crate0 from the surface pallet1 at place depot1 using the hoist hoist1, use the hoist hoist1 to lift and place the crate crate0 from place depot1 into the truck truck1, drive truck truck1 from place depot1 to place distributor1, unload crate crate0 from truck truck1 at place distributor1 using hoist hoist8, drive truck truck1 from place distributor1 to place distributor0, lower the crate crate0 from the hoist hoist8 onto the surface pallet8 at the place distributor1\"; which of the following actions can be removed from this plan and still have a valid plan? A. drive truck truck1 from place depot0 to place depot1. B. drive truck truck1 from place distributor1 to place distributor0. C. use the hoist hoist1 to lift and place the crate crate0 from place depot1 into the truck truck1. D. unload crate crate0 from truck truck1 at place distributor1 using hoist hoist8.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drive truck truck1 from place depot0 to place depot1", "drive truck truck1 from place distributor1 to place distributor0", "use the hoist hoist1 to lift and place the crate crate0 from place depot1 into the truck truck1", "unload crate crate0 from truck truck1 at place distributor1 using hoist hoist8"]}, "query": "Given the plan: \"drive truck truck1 from place depot0 to place depot1, lift the crate crate0 from the surface pallet1 at place depot1 using the hoist hoist1, use the hoist hoist1 to lift and place the crate crate0 from place depot1 into the truck truck1, drive truck truck1 from place depot1 to place distributor1, unload crate crate0 from truck truck1 at place distributor1 using hoist hoist8, drive truck truck1 from place distributor1 to place distributor0, lower the crate crate0 from the hoist hoist8 onto the surface pallet8 at the place distributor1\"; which action can be removed from this plan?", "answer": "B"}
{"id": 5055649430651887027, "group": "action_justification_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 9 pallets, 9 hoists, 2 trucks, 7 depots, numbered consecutively. Currently, pallet5, pallet7, pallet0, pallet2, pallet8, pallet6, pallet4, crate1, and crate0 are clear; hoist2, hoist5, hoist1, hoist4, hoist3, hoist7, hoist6, hoist0, and hoist8 are available; pallet3 is at depot3, hoist6 is at depot6, crate1 is at depot3, pallet8 is at distributor1, hoist2 is at depot2, pallet4 is at depot4, hoist8 is at distributor1, pallet1 is at depot1, pallet2 is at depot2, hoist7 is at distributor0, crate0 is at depot1, truck0 is at depot2, hoist3 is at depot3, pallet5 is at depot5, pallet6 is at depot6, hoist1 is at depot1, hoist5 is at depot5, hoist0 is at depot0, pallet0 is at depot0, hoist4 is at depot4, truck1 is at depot0, and pallet7 is at distributor0; crate1 is on pallet3 and crate0 is on pallet1. The goal is to reach a state where the following facts hold: crate0 is on pallet8 and crate1 is on pallet3.", "question": "Given the plan: \"drive truck truck1 from place depot0 to place depot4, drive truck truck1 from place depot4 to place depot1, lift the crate crate0 from the ground pallet1 at position depot1 using the hoist hoist1, drive truck truck0 from place depot2 to place depot4, use hoist hoist1 to load crate crate0 into truck truck1 at place depot1, drive truck truck1 from place depot1 to place distributor1, use the hoist hoist8 to unload the crate crate0 from the truck truck1 at location distributor1, drop crate crate0 from hoist hoist8 onto surface pallet8 at place distributor1\"; which of the following actions can be removed from this plan and still have a valid plan? A. drive truck truck0 from place depot2 to place depot4. B. use hoist hoist1 to load crate crate0 into truck truck1 at place depot1. C. drive truck truck1 from place depot1 to place distributor1. D. drive truck truck1 from place depot0 to place depot4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drive truck truck0 from place depot2 to place depot4", "use hoist hoist1 to load crate crate0 into truck truck1 at place depot1", "drive truck truck1 from place depot1 to place distributor1", "drive truck truck1 from place depot0 to place depot4"]}, "query": "Given the plan: \"drive truck truck1 from place depot0 to place depot4, drive truck truck1 from place depot4 to place depot1, lift the crate crate0 from the ground pallet1 at position depot1 using the hoist hoist1, drive truck truck0 from place depot2 to place depot4, use hoist hoist1 to load crate crate0 into truck truck1 at place depot1, drive truck truck1 from place depot1 to place distributor1, use the hoist hoist8 to unload the crate crate0 from the truck truck1 at location distributor1, drop crate crate0 from hoist hoist8 onto surface pallet8 at place distributor1\"; which action can be removed from this plan?", "answer": "A"}
{"id": 7520377624277012975, "group": "action_justification_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 9 pallets, 9 hoists, 2 trucks, 7 depots, numbered consecutively. Currently, pallet5, pallet7, pallet0, pallet2, pallet8, pallet6, pallet4, crate1, and crate0 are clear; hoist2, hoist5, hoist1, hoist4, hoist3, hoist7, hoist6, hoist0, and hoist8 are available; pallet3 is at depot3, hoist6 is at depot6, crate1 is at depot3, pallet8 is at distributor1, hoist2 is at depot2, pallet4 is at depot4, hoist8 is at distributor1, pallet1 is at depot1, pallet2 is at depot2, hoist7 is at distributor0, crate0 is at depot1, truck0 is at depot2, hoist3 is at depot3, pallet5 is at depot5, pallet6 is at depot6, hoist1 is at depot1, hoist5 is at depot5, hoist0 is at depot0, pallet0 is at depot0, hoist4 is at depot4, truck1 is at depot0, and pallet7 is at distributor0; crate1 is on pallet3 and crate0 is on pallet1. The goal is to reach a state where the following facts hold: crate0 is on pallet8 and crate1 is on pallet3.", "question": "Given the plan: \"navigate the truck truck0 from place depot2 to place depot4, navigate the truck truck0 from place depot4 to place depot2, navigate the truck truck1 from place depot0 to place depot6, navigate the truck truck1 from place depot6 to place depot1, use hoist hoist1 to lift crate crate0 from surface pallet1 at place depot1, load the crate crate0 from place depot1 with hoist hoist1 into the truck truck1, navigate the truck truck1 from place depot1 to place distributor1, unload crate crate0 from truck truck1 at place distributor1 using hoist hoist8, lower the crate crate0 from the hoist hoist8 and place it on the surface pallet8 at location distributor1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. navigate the truck truck1 from place depot6 to place depot1 and use hoist hoist1 to lift crate crate0 from surface pallet1 at place depot1. B. navigate the truck truck1 from place depot0 to place depot6 and navigate the truck truck1 from place depot6 to place depot1. C. unload crate crate0 from truck truck1 at place distributor1 using hoist hoist8 and lower the crate crate0 from the hoist hoist8 and place it on the surface pallet8 at location distributor1. D. navigate the truck truck0 from place depot2 to place depot4 and navigate the truck truck0 from place depot4 to place depot2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["navigate the truck truck1 from place depot6 to place depot1 and use hoist hoist1 to lift crate crate0 from surface pallet1 at place depot1", "navigate the truck truck1 from place depot0 to place depot6 and navigate the truck truck1 from place depot6 to place depot1", "unload crate crate0 from truck truck1 at place distributor1 using hoist hoist8 and lower the crate crate0 from the hoist hoist8 and place it on the surface pallet8 at location distributor1", "navigate the truck truck0 from place depot2 to place depot4 and navigate the truck truck0 from place depot4 to place depot2"]}, "query": "Given the plan: \"navigate the truck truck0 from place depot2 to place depot4, navigate the truck truck0 from place depot4 to place depot2, navigate the truck truck1 from place depot0 to place depot6, navigate the truck truck1 from place depot6 to place depot1, use hoist hoist1 to lift crate crate0 from surface pallet1 at place depot1, load the crate crate0 from place depot1 with hoist hoist1 into the truck truck1, navigate the truck truck1 from place depot1 to place distributor1, unload crate crate0 from truck truck1 at place distributor1 using hoist hoist8, lower the crate crate0 from the hoist hoist8 and place it on the surface pallet8 at location distributor1\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -8056994914240577023, "group": "action_justification_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 12 pallets, 12 hoists, 2 trucks, 10 depots, numbered consecutively. Currently, pallet5, pallet11, pallet7, pallet0, pallet6, pallet4, pallet1, crate1, pallet3, crate0, pallet10, and pallet9 are clear; hoist2, hoist5, hoist1, hoist4, hoist3, hoist7, hoist11, hoist6, hoist0, hoist8, hoist9, and hoist10 are available; pallet3 is at depot3, hoist6 is at depot6, hoist9 is at depot9, hoist7 is at depot7, pallet7 is at depot7, pallet8 is at depot8, hoist11 is at distributor1, hoist2 is at depot2, pallet4 is at depot4, pallet11 is at distributor1, crate1 is at depot2, pallet1 is at depot1, pallet2 is at depot2, pallet9 is at depot9, truck0 is at depot0, hoist3 is at depot3, pallet5 is at depot5, pallet6 is at depot6, pallet10 is at distributor0, hoist10 is at distributor0, hoist1 is at depot1, truck1 is at depot4, hoist5 is at depot5, hoist0 is at depot0, pallet0 is at depot0, hoist4 is at depot4, hoist8 is at depot8, and crate0 is at depot8; crate0 is on pallet8 and crate1 is on pallet2. The goal is to reach a state where the following facts hold: crate0 is on pallet11 and crate1 is on pallet9.", "question": "Given the plan: \"drive truck truck0 from place depot0 to place depot6, drive truck truck0 from place depot6 to place depot0, drive truck truck1 from place depot4 to place depot2, lift the crate crate1 from the surface pallet2 at place depot2 using the hoist hoist2, use the hoist hoist2 to lift and place the crate crate1 from place depot2 into the truck truck1, drive truck truck1 from place depot2 to place depot8, lift the crate crate0 from the surface pallet8 at place depot8 using the hoist hoist8, use the hoist hoist8 to lift and place the crate crate0 from place depot8 into the truck truck1, drive truck truck1 from place depot8 to place distributor1, use the hoist hoist11 to unload the crate crate0 from the truck truck1 at location distributor1, drop crate crate0 from hoist hoist11 onto surface pallet11 at place distributor1, drive truck truck1 from place distributor1 to place depot9, use the hoist hoist9 to unload the crate crate1 from the truck truck1 at location depot9, drop crate crate1 from hoist hoist9 onto surface pallet9 at place depot9\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. lift the crate crate1 from the surface pallet2 at place depot2 using the hoist hoist2 and use the hoist hoist2 to lift and place the crate crate1 from place depot2 into the truck truck1. B. use the hoist hoist8 to lift and place the crate crate0 from place depot8 into the truck truck1 and drive truck truck1 from place depot8 to place distributor1. C. drive truck truck0 from place depot0 to place depot6 and drive truck truck0 from place depot6 to place depot0. D. drive truck truck0 from place depot6 to place depot0 and drive truck truck1 from place depot4 to place depot2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["lift the crate crate1 from the surface pallet2 at place depot2 using the hoist hoist2 and use the hoist hoist2 to lift and place the crate crate1 from place depot2 into the truck truck1", "use the hoist hoist8 to lift and place the crate crate0 from place depot8 into the truck truck1 and drive truck truck1 from place depot8 to place distributor1", "drive truck truck0 from place depot0 to place depot6 and drive truck truck0 from place depot6 to place depot0", "drive truck truck0 from place depot6 to place depot0 and drive truck truck1 from place depot4 to place depot2"]}, "query": "Given the plan: \"drive truck truck0 from place depot0 to place depot6, drive truck truck0 from place depot6 to place depot0, drive truck truck1 from place depot4 to place depot2, lift the crate crate1 from the surface pallet2 at place depot2 using the hoist hoist2, use the hoist hoist2 to lift and place the crate crate1 from place depot2 into the truck truck1, drive truck truck1 from place depot2 to place depot8, lift the crate crate0 from the surface pallet8 at place depot8 using the hoist hoist8, use the hoist hoist8 to lift and place the crate crate0 from place depot8 into the truck truck1, drive truck truck1 from place depot8 to place distributor1, use the hoist hoist11 to unload the crate crate0 from the truck truck1 at location distributor1, drop crate crate0 from hoist hoist11 onto surface pallet11 at place distributor1, drive truck truck1 from place distributor1 to place depot9, use the hoist hoist9 to unload the crate crate1 from the truck truck1 at location depot9, drop crate crate1 from hoist hoist9 onto surface pallet9 at place depot9\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": 7515784876075722772, "group": "action_justification_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 4 pallets, 4 hoists, 2 trucks, 2 depots, numbered consecutively. Currently, pallet2, pallet1, crate1, and crate0 are clear; hoist2, hoist1, hoist3, and hoist0 are available; crate0 is at depot0, crate1 is at distributor1, pallet2 is at distributor0, pallet1 is at depot1, pallet3 is at distributor1, truck0 is at distributor0, hoist1 is at depot1, hoist3 is at distributor1, hoist0 is at depot0, pallet0 is at depot0, truck1 is at depot0, and hoist2 is at distributor0; crate1 is on pallet3 and crate0 is on pallet0. The goal is to reach a state where the following facts hold: crate1 is on pallet0 and crate0 is on crate1.", "question": "Given the plan: \"drive the truck truck1 from depot0 to distributor1, drive the truck truck1 from distributor1 to depot0, drive the truck truck1 from depot0 to distributor1, lift crate crate1 from surface pallet3 at place distributor1 using hoist hoist3, load crate crate1 into truck truck1 at place distributor1 with hoist hoist3, drive the truck truck1 from distributor1 to depot0, lift crate crate0 from surface pallet0 at place depot0 using hoist hoist0, load crate crate0 into truck truck1 at place depot0 with hoist hoist0, use the hoist hoist0 to unload the crate crate1 from the truck truck1 at location depot0, place the crate crate1 on the surface pallet0 at the place depot0 using the hoist hoist0, use the hoist hoist0 to unload the crate crate0 from the truck truck1 at location depot0, place the crate crate0 on the surface crate1 at the place depot0 using the hoist hoist0\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. drive the truck truck1 from depot0 to distributor1 and drive the truck truck1 from distributor1 to depot0. B. load crate crate0 into truck truck1 at place depot0 with hoist hoist0 and use the hoist hoist0 to unload the crate crate1 from the truck truck1 at location depot0. C. drive the truck truck1 from depot0 to distributor1 and lift crate crate1 from surface pallet3 at place distributor1 using hoist hoist3. D. load crate crate1 into truck truck1 at place distributor1 with hoist hoist3 and drive the truck truck1 from distributor1 to depot0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drive the truck truck1 from depot0 to distributor1 and drive the truck truck1 from distributor1 to depot0", "load crate crate0 into truck truck1 at place depot0 with hoist hoist0 and use the hoist hoist0 to unload the crate crate1 from the truck truck1 at location depot0", "drive the truck truck1 from depot0 to distributor1 and lift crate crate1 from surface pallet3 at place distributor1 using hoist hoist3", "load crate crate1 into truck truck1 at place distributor1 with hoist hoist3 and drive the truck truck1 from distributor1 to depot0"]}, "query": "Given the plan: \"drive the truck truck1 from depot0 to distributor1, drive the truck truck1 from distributor1 to depot0, drive the truck truck1 from depot0 to distributor1, lift crate crate1 from surface pallet3 at place distributor1 using hoist hoist3, load crate crate1 into truck truck1 at place distributor1 with hoist hoist3, drive the truck truck1 from distributor1 to depot0, lift crate crate0 from surface pallet0 at place depot0 using hoist hoist0, load crate crate0 into truck truck1 at place depot0 with hoist hoist0, use the hoist hoist0 to unload the crate crate1 from the truck truck1 at location depot0, place the crate crate1 on the surface pallet0 at the place depot0 using the hoist hoist0, use the hoist hoist0 to unload the crate crate0 from the truck truck1 at location depot0, place the crate crate0 on the surface crate1 at the place depot0 using the hoist hoist0\"; which pair of consecutive actions can be removed from this plan?", "answer": "A"}
{"id": 1614167797727197184, "group": "action_justification_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 12 pallets, 12 hoists, 2 trucks, 10 depots, numbered consecutively. Currently, pallet5, pallet11, pallet7, pallet0, pallet6, pallet4, pallet1, crate1, pallet3, crate0, pallet10, and pallet9 are clear; hoist2, hoist5, hoist1, hoist4, hoist3, hoist7, hoist11, hoist6, hoist0, hoist8, hoist9, and hoist10 are available; pallet3 is at depot3, hoist6 is at depot6, hoist9 is at depot9, hoist7 is at depot7, pallet7 is at depot7, pallet8 is at depot8, hoist11 is at distributor1, hoist2 is at depot2, pallet4 is at depot4, pallet11 is at distributor1, crate1 is at depot2, pallet1 is at depot1, pallet2 is at depot2, pallet9 is at depot9, truck0 is at depot0, hoist3 is at depot3, pallet5 is at depot5, pallet6 is at depot6, pallet10 is at distributor0, hoist10 is at distributor0, hoist1 is at depot1, truck1 is at depot4, hoist5 is at depot5, hoist0 is at depot0, pallet0 is at depot0, hoist4 is at depot4, hoist8 is at depot8, and crate0 is at depot8; crate0 is on pallet8 and crate1 is on pallet2. The goal is to reach a state where the following facts hold: crate0 is on pallet11 and crate1 is on pallet9.", "question": "Given the plan: \"navigate the truck truck0 from place depot0 to place depot6, navigate the truck truck0 from place depot6 to place depot0, navigate the truck truck0 from place depot0 to place depot8, lift the crate crate0 from the surface pallet8 at place depot8 using the hoist hoist8, load the crate crate0 from place depot8 with hoist hoist8 into the truck truck0, navigate the truck truck0 from place depot8 to place depot2, lift the crate crate1 from the surface pallet2 at place depot2 using the hoist hoist2, load the crate crate1 from place depot2 with hoist hoist2 into the truck truck0, navigate the truck truck0 from place depot2 to place distributor0, navigate the truck truck0 from place distributor0 to place depot9, unload the crate crate1 from the truck truck0 at the place depot9 using the hoist hoist9, navigate the truck truck0 from place depot9 to place distributor1, unload the crate crate0 from the truck truck0 at the place distributor1 using the hoist hoist11, drop the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1, drop the crate crate1 from the hoist hoist9 onto the surface pallet9 at the place depot9\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. load the crate crate0 from place depot8 with hoist hoist8 into the truck truck0 and navigate the truck truck0 from place depot8 to place depot2. B. unload the crate crate0 from the truck truck0 at the place distributor1 using the hoist hoist11 and drop the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1. C. navigate the truck truck0 from place distributor0 to place depot9 and unload the crate crate1 from the truck truck0 at the place depot9 using the hoist hoist9. D. navigate the truck truck0 from place depot0 to place depot6 and navigate the truck truck0 from place depot6 to place depot0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load the crate crate0 from place depot8 with hoist hoist8 into the truck truck0 and navigate the truck truck0 from place depot8 to place depot2", "unload the crate crate0 from the truck truck0 at the place distributor1 using the hoist hoist11 and drop the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1", "navigate the truck truck0 from place distributor0 to place depot9 and unload the crate crate1 from the truck truck0 at the place depot9 using the hoist hoist9", "navigate the truck truck0 from place depot0 to place depot6 and navigate the truck truck0 from place depot6 to place depot0"]}, "query": "Given the plan: \"navigate the truck truck0 from place depot0 to place depot6, navigate the truck truck0 from place depot6 to place depot0, navigate the truck truck0 from place depot0 to place depot8, lift the crate crate0 from the surface pallet8 at place depot8 using the hoist hoist8, load the crate crate0 from place depot8 with hoist hoist8 into the truck truck0, navigate the truck truck0 from place depot8 to place depot2, lift the crate crate1 from the surface pallet2 at place depot2 using the hoist hoist2, load the crate crate1 from place depot2 with hoist hoist2 into the truck truck0, navigate the truck truck0 from place depot2 to place distributor0, navigate the truck truck0 from place distributor0 to place depot9, unload the crate crate1 from the truck truck0 at the place depot9 using the hoist hoist9, navigate the truck truck0 from place depot9 to place distributor1, unload the crate crate0 from the truck truck0 at the place distributor1 using the hoist hoist11, drop the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1, drop the crate crate1 from the hoist hoist9 onto the surface pallet9 at the place depot9\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -424178941741699538, "group": "action_justification_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 12 pallets, 12 hoists, 2 trucks, 10 depots, numbered consecutively. Currently, pallet5, pallet11, pallet7, pallet0, pallet6, pallet4, pallet1, crate1, pallet3, crate0, pallet10, and pallet9 are clear; hoist2, hoist5, hoist1, hoist4, hoist3, hoist7, hoist11, hoist6, hoist0, hoist8, hoist9, and hoist10 are available; pallet3 is at depot3, hoist6 is at depot6, hoist9 is at depot9, hoist7 is at depot7, pallet7 is at depot7, pallet8 is at depot8, hoist11 is at distributor1, hoist2 is at depot2, pallet4 is at depot4, pallet11 is at distributor1, crate1 is at depot2, pallet1 is at depot1, pallet2 is at depot2, pallet9 is at depot9, truck0 is at depot0, hoist3 is at depot3, pallet5 is at depot5, pallet6 is at depot6, pallet10 is at distributor0, hoist10 is at distributor0, hoist1 is at depot1, truck1 is at depot4, hoist5 is at depot5, hoist0 is at depot0, pallet0 is at depot0, hoist4 is at depot4, hoist8 is at depot8, and crate0 is at depot8; crate0 is on pallet8 and crate1 is on pallet2. The goal is to reach a state where the following facts hold: crate0 is on pallet11 and crate1 is on pallet9.", "question": "Given the plan: \"drive truck truck0 from place depot0 to place depot6, drive truck truck0 from place depot6 to place depot0, drive truck truck0 from place depot0 to place depot8, use the hoist hoist8 to lift the crate crate0 from the surface pallet8 at location depot8, load the crate crate0 into the truck truck0 at the place depot8 with the hoist hoist8, drive truck truck0 from place depot8 to place depot2, use the hoist hoist2 to lift the crate crate1 from the surface pallet2 at location depot2, load the crate crate1 into the truck truck0 at the place depot2 with the hoist hoist2, drive truck truck0 from place depot2 to place depot4, drive truck truck0 from place depot4 to place distributor1, unload crate crate0 from truck truck0 at place distributor1 using hoist hoist11, drive truck truck0 from place distributor1 to place depot9, unload crate crate1 from truck truck0 at place depot9 using hoist hoist9, drop the crate crate1 from the hoist hoist9 onto the surface pallet9 at the place depot9, drop the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. drop the crate crate1 from the hoist hoist9 onto the surface pallet9 at the place depot9 and drop the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1. B. drive truck truck0 from place depot0 to place depot6 and drive truck truck0 from place depot6 to place depot0. C. drive truck truck0 from place depot0 to place depot8 and use the hoist hoist8 to lift the crate crate0 from the surface pallet8 at location depot8. D. load the crate crate0 into the truck truck0 at the place depot8 with the hoist hoist8 and drive truck truck0 from place depot8 to place depot2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drop the crate crate1 from the hoist hoist9 onto the surface pallet9 at the place depot9 and drop the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1", "drive truck truck0 from place depot0 to place depot6 and drive truck truck0 from place depot6 to place depot0", "drive truck truck0 from place depot0 to place depot8 and use the hoist hoist8 to lift the crate crate0 from the surface pallet8 at location depot8", "load the crate crate0 into the truck truck0 at the place depot8 with the hoist hoist8 and drive truck truck0 from place depot8 to place depot2"]}, "query": "Given the plan: \"drive truck truck0 from place depot0 to place depot6, drive truck truck0 from place depot6 to place depot0, drive truck truck0 from place depot0 to place depot8, use the hoist hoist8 to lift the crate crate0 from the surface pallet8 at location depot8, load the crate crate0 into the truck truck0 at the place depot8 with the hoist hoist8, drive truck truck0 from place depot8 to place depot2, use the hoist hoist2 to lift the crate crate1 from the surface pallet2 at location depot2, load the crate crate1 into the truck truck0 at the place depot2 with the hoist hoist2, drive truck truck0 from place depot2 to place depot4, drive truck truck0 from place depot4 to place distributor1, unload crate crate0 from truck truck0 at place distributor1 using hoist hoist11, drive truck truck0 from place distributor1 to place depot9, unload crate crate1 from truck truck0 at place depot9 using hoist hoist9, drop the crate crate1 from the hoist hoist9 onto the surface pallet9 at the place depot9, drop the crate crate0 from the hoist hoist11 onto the surface pallet11 at the place distributor1\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": 7375187068890107422, "group": "action_justification_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 crates, 2 distributors, 12 pallets, 12 hoists, 2 trucks, 10 depots, numbered consecutively. Currently, pallet5, pallet11, pallet7, pallet0, pallet6, pallet4, pallet1, crate1, pallet3, crate0, pallet10, and pallet9 are clear; hoist2, hoist5, hoist1, hoist4, hoist3, hoist7, hoist11, hoist6, hoist0, hoist8, hoist9, and hoist10 are available; pallet3 is at depot3, hoist6 is at depot6, hoist9 is at depot9, hoist7 is at depot7, pallet7 is at depot7, pallet8 is at depot8, hoist11 is at distributor1, hoist2 is at depot2, pallet4 is at depot4, pallet11 is at distributor1, crate1 is at depot2, pallet1 is at depot1, pallet2 is at depot2, pallet9 is at depot9, truck0 is at depot0, hoist3 is at depot3, pallet5 is at depot5, pallet6 is at depot6, pallet10 is at distributor0, hoist10 is at distributor0, hoist1 is at depot1, truck1 is at depot4, hoist5 is at depot5, hoist0 is at depot0, pallet0 is at depot0, hoist4 is at depot4, hoist8 is at depot8, and crate0 is at depot8; crate0 is on pallet8 and crate1 is on pallet2. The goal is to reach a state where the following facts hold: crate0 is on pallet11 and crate1 is on pallet9.", "question": "Given the plan: \"navigate the truck truck0 from location depot0 to location depot6, navigate the truck truck0 from location depot6 to location depot0, navigate the truck truck0 from location depot0 to location depot8, lift the crate crate0 from the ground pallet8 at position depot8 using the hoist hoist8, use hoist hoist8 to load crate crate0 into truck truck0 at place depot8, navigate the truck truck0 from location depot8 to location depot2, lift the crate crate1 from the ground pallet2 at position depot2 using the hoist hoist2, use hoist hoist2 to load crate crate1 into truck truck0 at place depot2, navigate the truck truck0 from location depot2 to location depot9, use the hoist hoist9 to unload the crate crate1 from the truck truck0 at location depot9, navigate the truck truck0 from location depot9 to location depot4, navigate the truck truck0 from location depot4 to location distributor1, use the hoist hoist11 to unload the crate crate0 from the truck truck0 at location distributor1, place the crate crate0 on the surface pallet11 at the place distributor1 using the hoist hoist11, place the crate crate1 on the surface pallet9 at the place depot9 using the hoist hoist9\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. navigate the truck truck0 from location depot0 to location depot8 and lift the crate crate0 from the ground pallet8 at position depot8 using the hoist hoist8. B. use hoist hoist8 to load crate crate0 into truck truck0 at place depot8 and navigate the truck truck0 from location depot8 to location depot2. C. navigate the truck truck0 from location depot0 to location depot6 and navigate the truck truck0 from location depot6 to location depot0. D. place the crate crate0 on the surface pallet11 at the place distributor1 using the hoist hoist11 and place the crate crate1 on the surface pallet9 at the place depot9 using the hoist hoist9.", "choices": {"label": ["A", "B", "C", "D"], "text": ["navigate the truck truck0 from location depot0 to location depot8 and lift the crate crate0 from the ground pallet8 at position depot8 using the hoist hoist8", "use hoist hoist8 to load crate crate0 into truck truck0 at place depot8 and navigate the truck truck0 from location depot8 to location depot2", "navigate the truck truck0 from location depot0 to location depot6 and navigate the truck truck0 from location depot6 to location depot0", "place the crate crate0 on the surface pallet11 at the place distributor1 using the hoist hoist11 and place the crate crate1 on the surface pallet9 at the place depot9 using the hoist hoist9"]}, "query": "Given the plan: \"navigate the truck truck0 from location depot0 to location depot6, navigate the truck truck0 from location depot6 to location depot0, navigate the truck truck0 from location depot0 to location depot8, lift the crate crate0 from the ground pallet8 at position depot8 using the hoist hoist8, use hoist hoist8 to load crate crate0 into truck truck0 at place depot8, navigate the truck truck0 from location depot8 to location depot2, lift the crate crate1 from the ground pallet2 at position depot2 using the hoist hoist2, use hoist hoist2 to load crate crate1 into truck truck0 at place depot2, navigate the truck truck0 from location depot2 to location depot9, use the hoist hoist9 to unload the crate crate1 from the truck truck0 at location depot9, navigate the truck truck0 from location depot9 to location depot4, navigate the truck truck0 from location depot4 to location distributor1, use the hoist hoist11 to unload the crate crate0 from the truck truck0 at location distributor1, place the crate crate0 on the surface pallet11 at the place distributor1 using the hoist hoist11, place the crate crate1 on the surface pallet9 at the place depot9 using the hoist hoist9\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": 5080748363618165514, "group": "action_justification_mcq", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 3 crates, 2 distributors, 6 pallets, 6 hoists, 2 trucks, 4 depots, numbered consecutively. Currently, crate2, pallet0, pallet2, pallet4, crate1, and crate0 are clear; hoist2, hoist5, hoist1, hoist4, hoist3, and hoist0 are available; pallet3 is at depot3, truck0 is at distributor1, hoist2 is at depot2, crate1 is at distributor1, crate2 is at depot1, pallet1 is at depot1, pallet2 is at depot2, crate0 is at depot3, hoist3 is at depot3, hoist4 is at distributor0, pallet4 is at distributor0, hoist5 is at distributor1, pallet5 is at distributor1, hoist1 is at depot1, hoist0 is at depot0, pallet0 is at depot0, and truck1 is at depot0; crate1 is on pallet5, crate2 is on pallet1, and crate0 is on pallet3. The goal is to reach a state where the following facts hold: crate0 is on pallet1, crate2 is on pallet4, and crate1 is on crate2.", "question": "Given the plan: \"use hoist hoist1 to lift crate crate2 from surface pallet1 at place depot1, use hoist hoist5 to lift crate crate1 from surface pallet5 at place distributor1, use the hoist hoist5 to lift and place the crate crate1 from place distributor1 into the truck truck0, navigate the truck truck0 from location distributor1 to location depot1, use the hoist hoist1 to lift and place the crate crate2 from place depot1 into the truck truck0, navigate the truck truck0 from location depot1 to location depot3, use hoist hoist3 to lift crate crate0 from surface pallet3 at place depot3, use the hoist hoist3 to lift and place the crate crate0 from place depot3 into the truck truck0, navigate the truck truck0 from location depot3 to location depot1, use the hoist hoist1 to unload the crate crate0 from the truck truck0 at location depot1, navigate the truck truck0 from location depot1 to location distributor0, use the hoist hoist4 to unload the crate crate2 from the truck truck0 at location distributor0, drop the crate crate0 from the hoist hoist1 onto the surface pallet1 at the place depot1, drop the crate crate2 from the hoist hoist4 onto the surface pallet4 at the place distributor0, use the hoist hoist4 to unload the crate crate1 from the truck truck0 at location distributor0, drop the crate crate1 from the hoist hoist4 onto the surface crate2 at the place distributor0, navigate the truck truck0 from location distributor0 to location depot3\"; which of the following actions can be removed from this plan and still have a valid plan? A. navigate the truck truck0 from location distributor0 to location depot3. B. navigate the truck truck0 from location depot3 to location depot1. C. use the hoist hoist3 to lift and place the crate crate0 from place depot3 into the truck truck0. D. navigate the truck truck0 from location depot1 to location depot3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["navigate the truck truck0 from location distributor0 to location depot3", "navigate the truck truck0 from location depot3 to location depot1", "use the hoist hoist3 to lift and place the crate crate0 from place depot3 into the truck truck0", "navigate the truck truck0 from location depot1 to location depot3"]}, "query": "Given the plan: \"use hoist hoist1 to lift crate crate2 from surface pallet1 at place depot1, use hoist hoist5 to lift crate crate1 from surface pallet5 at place distributor1, use the hoist hoist5 to lift and place the crate crate1 from place distributor1 into the truck truck0, navigate the truck truck0 from location distributor1 to location depot1, use the hoist hoist1 to lift and place the crate crate2 from place depot1 into the truck truck0, navigate the truck truck0 from location depot1 to location depot3, use hoist hoist3 to lift crate crate0 from surface pallet3 at place depot3, use the hoist hoist3 to lift and place the crate crate0 from place depot3 into the truck truck0, navigate the truck truck0 from location depot3 to location depot1, use the hoist hoist1 to unload the crate crate0 from the truck truck0 at location depot1, navigate the truck truck0 from location depot1 to location distributor0, use the hoist hoist4 to unload the crate crate2 from the truck truck0 at location distributor0, drop the crate crate0 from the hoist hoist1 onto the surface pallet1 at the place depot1, drop the crate crate2 from the hoist hoist4 onto the surface pallet4 at the place distributor0, use the hoist hoist4 to unload the crate crate1 from the truck truck0 at location distributor0, drop the crate crate1 from the hoist hoist4 onto the surface crate2 at the place distributor0, navigate the truck truck0 from location distributor0 to location depot3\"; which action can be removed from this plan?", "answer": "A"}
