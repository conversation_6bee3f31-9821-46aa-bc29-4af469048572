{"id": 8090289752681232634, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2, with the car c8 on board. The cars are at locations as follows: c5, c3, and c6 are at l2; c4, c7, and c9 are at l0; c1, c0, and c2 are at l1. The goal is to reach a state where the following facts hold: Car c5 is at location l2, Car c4 is at location l0, Car c1 is at location l1, Car c7 is at location l0, Car c0 is at location l2, Car c2 is at location l1, Car c3 is at location l2, Car c6 is at location l2, Car c8 is at location l2, and Car c9 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Car ?c is on board the ferry.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c9 l1)", "(at c8 l1)", "(at c3 l1)", "(at c3 l0)", "(on c9)", "(at c0 l0)", "(at c5 l0)", "(at c2 l0)", "(on c4)", "(at c6 l0)", "(at c1 l2)", "(at c1 l0)", "(at-ferry l0)", "(at c7 l2)", "(at c2 l2)", "(at c4 l2)", "(on c2)", "(at c8 l0)", "(on c6)", "(at c5 l1)", "(at c9 l2)", "(at c6 l1)", "(at c4 l1)", "(on c1)", "(on c5)", "(on c7)", "(at c7 l1)", "(on c3)"], "yes": ["(on c0)", "(empty-ferry)", "(at-ferry l1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l1) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c9 l0) (at-ferry l2) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1) (on c8))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": -3909215648118117091, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2, with the car c2 on board. The cars are at locations as follows: c5, c1, c8, c0, and c7 are at l1; c4, c6, and c9 are at l0; c3 is at l2. The goal is to reach a state where the following facts hold: Car c5 is at location l2, Car c4 is at location l0, Car c1 is at location l1, Car c7 is at location l0, Car c0 is at location l2, Car c2 is at location l1, Car c3 is at location l2, Car c6 is at location l2, Car c8 is at location l2, and Car c9 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Ferry has car ?c on board.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c9 l1)", "(at c3 l1)", "(at c3 l0)", "(on c9)", "(at c0 l0)", "(at c5 l0)", "(at c2 l0)", "(on c4)", "(at c1 l2)", "(at c1 l0)", "(at c7 l2)", "(at c2 l2)", "(at c4 l2)", "(at c8 l0)", "(at c9 l2)", "(at c6 l1)", "(at c4 l1)", "(on c1)", "(on c3)"], "yes": ["(on c0)", "(at-ferry l1)", "(on c5)", "(on c6)", "(on c7)", "(at-ferry l0)", "(on c8)", "(empty-ferry)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l1) (at c1 l1) (at c3 l2) (at c4 l0) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l1) (at c9 l0) (at-ferry l2) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1) (on c2))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": 4400618544721988322, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c12 on board. The cars are at locations as follows: c25, c39, c46, c24, c36, c40, c22, c37, c1, c48, c41, c27, c38, c43, c34, c42, c32, c13, c31, c47, c21, c49, c5, c4, c45, c19, c7, and c28 are at l1; c44, c30, c3, c0, c29, c17, c15, c16, c2, c6, c33, c26, c35, c9, c10, c11, c18, c20, c8, c23, and c14 are at l0. The goal is to reach a state where the following facts hold: Car c33 is at location l1, Car c46 is at location l0, Car c21 is at location l0, Car c35 is at location l1, Car c44 is at location l0, Car c10 is at location l1, Car c3 is at location l1, Car c30 is at location l0, Car c39 is at location l1, Car c24 is at location l1, Car c0 is at location l0, Car c29 is at location l0, Car c13 is at location l0, Car c2 is at location l0, Car c12 is at location l0, Car c25 is at location l0, Car c45 is at location l0, Car c1 is at location l1, Car c48 is at location l1, Car c41 is at location l1, Car c17 is at location l1, Car c27 is at location l1, Car c26 is at location l0, Car c36 is at location l0, Car c23 is at location l1, Car c34 is at location l1, Car c9 is at location l0, Car c32 is at location l1, Car c42 is at location l1, Car c31 is at location l1, Car c37 is at location l0, Car c47 is at location l1, Car c11 is at location l0, Car c20 is at location l0, Car c38 is at location l0, Car c43 is at location l0, Car c8 is at location l0, Car c49 is at location l1, Car c5 is at location l1, Car c6 is at location l1, Car c4 is at location l1, Car c18 is at location l1, Car c22 is at location l0, Car c19 is at location l0, Car c40 is at location l0, Car c7 is at location l1, Car c15 is at location l1, Car c28 is at location l1, Car c16 is at location l1, and Car c14 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on board the ferry.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c9 l1)", "(at c31 l0)", "(at c4 l0)", "(on c27)", "(at c8 l1)", "(on c39)", "(on c20)", "(at c48 l0)", "(at c2 l1)", "(on c48)", "(on c9)", "(at c30 l1)", "(at c5 l0)", "(on c42)", "(on c49)", "(at c42 l0)", "(on c4)", "(at c49 l0)", "(at c34 l0)", "(on c26)", "(at c29 l1)", "(at c1 l0)", "(at c14 l1)", "(at c28 l0)", "(at c0 l1)", "(on c14)", "(at c41 l0)", "(on c24)", "(on c0)", "(at c11 l1)", "(at c12 l1)", "(at c26 l1)", "(at c44 l1)", "(on c2)", "(on c11)", "(on c41)", "(at c27 l0)", "(at c47 l0)", "(on c28)", "(on c30)", "(on c34)", "(at c7 l0)", "(on c1)", "(on c31)", "(on c5)", "(at c32 l0)", "(on c7)", "(on c44)", "(at c24 l0)", "(on c47)", "(at c39 l0)", "(on c29)", "(on c8)", "(at c20 l1)", "(on c32)"], "yes": ["(on c10)", "(at-ferry l0)", "(on c13)", "(on c15)", "(on c16)", "(on c17)", "(on c18)", "(on c19)", "(on c21)", "(on c22)", "(on c23)", "(on c25)", "(on c3)", "(on c33)", "(on c35)", "(on c36)", "(on c37)", "(on c38)", "(on c40)", "(on c43)", "(on c45)", "(on c46)", "(on c6)", "(empty-ferry)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l0) (at c11 l0) (at c13 l1) (at c14 l0) (at c15 l0) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c2 l0) (at c20 l0) (at c21 l1) (at c22 l1) (at c23 l0) (at c24 l1) (at c25 l1) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l0) (at c34 l1) (at c35 l0) (at c36 l1) (at c37 l1) (at c38 l1) (at c39 l1) (at c4 l1) (at c40 l1) (at c41 l1) (at c42 l1) (at c43 l1) (at c44 l0) (at c45 l1) (at c46 l1) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c12))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": 8653717468368268605, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c22 on board. The cars are at locations as follows: c25, c10, c39, c24, c46, c36, c40, c37, c1, c48, c41, c27, c38, c43, c34, c42, c32, c12, c31, c26, c47, c49, c5, c4, c45, c7, c15, and c28 are at l1; c21, c44, c3, c30, c0, c29, c13, c17, c2, c16, c6, c33, c35, c9, c11, c18, c20, c8, c23, c19, and c14 are at l0. The goal is to reach a state where the following facts hold: Car c33 is at location l1, Car c46 is at location l0, Car c21 is at location l0, Car c35 is at location l1, Car c44 is at location l0, Car c10 is at location l1, Car c3 is at location l1, Car c30 is at location l0, Car c39 is at location l1, Car c24 is at location l1, Car c0 is at location l0, Car c29 is at location l0, Car c13 is at location l0, Car c2 is at location l0, Car c12 is at location l0, Car c25 is at location l0, Car c45 is at location l0, Car c1 is at location l1, Car c48 is at location l1, Car c41 is at location l1, Car c17 is at location l1, Car c27 is at location l1, Car c26 is at location l0, Car c36 is at location l0, Car c23 is at location l1, Car c34 is at location l1, Car c9 is at location l0, Car c32 is at location l1, Car c42 is at location l1, Car c31 is at location l1, Car c37 is at location l0, Car c47 is at location l1, Car c11 is at location l0, Car c20 is at location l0, Car c38 is at location l0, Car c43 is at location l0, Car c8 is at location l0, Car c49 is at location l1, Car c5 is at location l1, Car c6 is at location l1, Car c4 is at location l1, Car c18 is at location l1, Car c22 is at location l0, Car c19 is at location l0, Car c40 is at location l0, Car c7 is at location l1, Car c15 is at location l1, Car c28 is at location l1, Car c16 is at location l1, and Car c14 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on the ferry.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c9 l1)", "(at c31 l0)", "(at c4 l0)", "(on c27)", "(at c8 l1)", "(on c39)", "(on c20)", "(at c48 l0)", "(at c2 l1)", "(on c48)", "(on c9)", "(at c30 l1)", "(at c5 l0)", "(on c42)", "(on c49)", "(at c42 l0)", "(at c22 l1)", "(at c15 l0)", "(on c15)", "(on c4)", "(at c49 l0)", "(at c34 l0)", "(at c1 l0)", "(at c29 l1)", "(at c14 l1)", "(at c28 l0)", "(at c0 l1)", "(on c14)", "(at c41 l0)", "(on c24)", "(on c0)", "(at c11 l1)", "(at c13 l1)", "(on c21)", "(at c10 l0)", "(at c44 l1)", "(at c21 l1)", "(on c2)", "(on c11)", "(on c41)", "(at c27 l0)", "(at c47 l0)", "(on c28)", "(on c30)", "(on c34)", "(at c7 l0)", "(on c1)", "(on c31)", "(on c5)", "(at c32 l0)", "(on c19)", "(on c7)", "(on c10)", "(on c44)", "(at c19 l1)", "(at c24 l0)", "(at c39 l0)", "(on c47)", "(on c29)", "(on c13)", "(on c8)", "(at c20 l1)", "(on c32)"], "yes": ["(on c12)", "(at-ferry l0)", "(on c16)", "(on c17)", "(on c18)", "(on c23)", "(on c25)", "(on c26)", "(on c3)", "(on c33)", "(on c35)", "(on c36)", "(on c37)", "(on c38)", "(on c40)", "(on c43)", "(on c45)", "(on c46)", "(on c6)", "(empty-ferry)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l1) (at c11 l0) (at c12 l1) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l0) (at c2 l0) (at c20 l0) (at c21 l0) (at c23 l0) (at c24 l1) (at c25 l1) (at c26 l1) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l0) (at c34 l1) (at c35 l0) (at c36 l1) (at c37 l1) (at c38 l1) (at c39 l1) (at c4 l1) (at c40 l1) (at c41 l1) (at c42 l1) (at c43 l1) (at c44 l0) (at c45 l1) (at c46 l1) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c22))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": -8380327764684907176, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c38 on board. The cars are at locations as follows: c21, c44, c30, c0, c29, c2, c12, c25, c6, c33, c26, c36, c35, c9, c37, c11, c20, c8, c22, c19, and c14 are at l0; c10, c3, c39, c24, c46, c40, c1, c48, c41, c17, c27, c43, c23, c34, c42, c32, c13, c31, c47, c49, c5, c4, c18, c45, c7, c15, c28, and c16 are at l1. The goal is to reach a state where the following facts hold: Car c33 is at location l1, Car c46 is at location l0, Car c21 is at location l0, Car c35 is at location l1, Car c44 is at location l0, Car c10 is at location l1, Car c3 is at location l1, Car c30 is at location l0, Car c39 is at location l1, Car c24 is at location l1, Car c0 is at location l0, Car c29 is at location l0, Car c13 is at location l0, Car c2 is at location l0, Car c12 is at location l0, Car c25 is at location l0, Car c45 is at location l0, Car c1 is at location l1, Car c48 is at location l1, Car c41 is at location l1, Car c17 is at location l1, Car c27 is at location l1, Car c26 is at location l0, Car c36 is at location l0, Car c23 is at location l1, Car c34 is at location l1, Car c9 is at location l0, Car c32 is at location l1, Car c42 is at location l1, Car c31 is at location l1, Car c37 is at location l0, Car c47 is at location l1, Car c11 is at location l0, Car c20 is at location l0, Car c38 is at location l0, Car c43 is at location l0, Car c8 is at location l0, Car c49 is at location l1, Car c5 is at location l1, Car c6 is at location l1, Car c4 is at location l1, Car c18 is at location l1, Car c22 is at location l0, Car c19 is at location l0, Car c40 is at location l0, Car c7 is at location l1, Car c15 is at location l1, Car c28 is at location l1, Car c16 is at location l1, and Car c14 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Car ?c is on the ferry.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c8 l1)", "(on c39)", "(on c20)", "(at c2 l1)", "(at c15 l0)", "(on c22)", "(at c49 l0)", "(at c34 l0)", "(on c26)", "(at c29 l1)", "(on c14)", "(on c23)", "(at c10 l0)", "(at c44 l1)", "(on c41)", "(on c36)", "(on c34)", "(on c16)", "(on c44)", "(at c39 l0)", "(at c20 l1)", "(at c9 l1)", "(at c31 l0)", "(at c4 l0)", "(on c48)", "(at c36 l1)", "(on c49)", "(at c17 l0)", "(at c42 l0)", "(at c14 l1)", "(at c28 l0)", "(on c24)", "(on c17)", "(on c21)", "(at c18 l0)", "(at c27 l0)", "(at c23 l0)", "(on c19)", "(at c19 l1)", "(at c24 l0)", "(on c47)", "(on c29)", "(at c25 l1)", "(on c27)", "(at c3 l0)", "(at c30 l1)", "(at c37 l1)", "(at c16 l0)", "(at c0 l1)", "(at c11 l1)", "(on c37)", "(at c12 l1)", "(at c26 l1)", "(at c21 l1)", "(on c11)", "(at c47 l0)", "(on c31)", "(on c25)", "(on c8)", "(on c18)", "(on c3)", "(at c48 l0)", "(on c9)", "(at c5 l0)", "(on c42)", "(at c22 l1)", "(on c15)", "(on c4)", "(at c1 l0)", "(at c38 l1)", "(on c12)", "(on c0)", "(on c2)", "(on c28)", "(on c30)", "(at c7 l0)", "(on c1)", "(on c5)", "(at c32 l0)", "(on c7)", "(on c10)", "(at c41 l0)", "(on c32)"], "yes": ["(on c13)", "(on c33)", "(at-ferry l1)", "(on c35)", "(on c40)", "(on c43)", "(on c45)", "(on c46)", "(on c6)", "(empty-ferry)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l1) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c2 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l1) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l0) (at c34 l1) (at c35 l0) (at c36 l0) (at c37 l0) (at c39 l1) (at c4 l1) (at c40 l1) (at c41 l1) (at c42 l1) (at c43 l1) (at c44 l0) (at c45 l1) (at c46 l1) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l0) (not-eq l0 l1) (not-eq l1 l0) (on c38))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": 1148689272652995785, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l2, with the car c1 on board. The cars are at locations as follows: c2 and c0 are at l4. The goal is to reach a state where the following facts hold: Car c1 is at location l3, Car c2 is at location l3, and Car c0 is at location l3. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Ferry has car ?c on board.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c2 l0)", "(at c2 l2)", "(at c1 l1)", "(at c1 l2)", "(at c1 l0)", "(at c0 l2)", "(at-ferry l1)", "(at c0 l1)", "(at-ferry l0)", "(at c2 l1)", "(at c1 l4)", "(at c0 l0)"], "yes": ["(on c0)", "(at-ferry l3)", "(on c2)", "(empty-ferry)", "(at-ferry l4)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c2 l4) (at-ferry l2) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3) (on c1))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": 7701899687050763623, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c2, c6, and c9 are at l0; c5, c1, c8, c4, c0, and c7 are at l1; c3 is at l2. The goal is to reach a state where the following facts hold: Car c5 is at location l2, Car c4 is at location l0, Car c1 is at location l1, Car c7 is at location l0, Car c0 is at location l2, Car c2 is at location l1, Car c3 is at location l2, Car c6 is at location l2, Car c8 is at location l2, and Car c9 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Ferry has car ?c on board.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c2 l2)", "(at c9 l1)", "(at c4 l2)", "(at c9 l2)", "(at c1 l2)", "(at c1 l0)", "(at c6 l1)", "(on c1)", "(at c3 l1)", "(at c3 l0)", "(on c9)", "(at c7 l2)", "(at c0 l0)", "(at c5 l0)", "(at c8 l0)", "(on c3)"], "yes": ["(on c0)", "(on c2)", "(at-ferry l1)", "(on c4)", "(at-ferry l0)", "(on c5)", "(on c6)", "(on c7)", "(on c8)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l1) (at c1 l1) (at c2 l0) (at c3 l2) (at c4 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l1) (at c9 l0) (at-ferry l2) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": 960394967342664581, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2, with the car c8 on board. The cars are at locations as follows: c5 and c3 are at l2; c1, c4, c0, c2, and c7 are at l1; c6 and c9 are at l0. The goal is to reach a state where the following facts hold: Car c5 is at location l2, Car c4 is at location l0, Car c1 is at location l1, Car c7 is at location l0, Car c0 is at location l2, Car c2 is at location l1, Car c3 is at location l2, Car c6 is at location l2, Car c8 is at location l2, and Car c9 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Ferry has car ?c on board.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c9 l1)", "(at c8 l1)", "(at c3 l1)", "(at c3 l0)", "(on c9)", "(at c0 l0)", "(at c5 l0)", "(at c2 l0)", "(at c1 l2)", "(at c1 l0)", "(at c7 l2)", "(at c2 l2)", "(at c4 l2)", "(on c2)", "(at c8 l0)", "(at c5 l1)", "(at c9 l2)", "(at c6 l1)", "(on c1)", "(on c5)", "(on c3)"], "yes": ["(on c0)", "(on c4)", "(at-ferry l0)", "(on c6)", "(on c7)", "(empty-ferry)", "(at-ferry l1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l1) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l1) (at c5 l2) (at c6 l0) (at c7 l1) (at c9 l0) (at-ferry l2) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1) (on c8))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": 3802243055596032636, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c46 on board. The cars are at locations as follows: c33, c35, c10, c3, c39, c24, c1, c48, c41, c17, c27, c38, c23, c34, c42, c32, c31, c47, c49, c5, c6, c4, c18, c7, c15, c28, and c16 are at l1; c21, c44, c30, c0, c29, c13, c2, c12, c25, c45, c26, c36, c9, c37, c11, c20, c43, c8, c22, c19, c40, and c14 are at l0. The goal is to reach a state where the following facts hold: Car c33 is at location l1, Car c46 is at location l0, Car c21 is at location l0, Car c35 is at location l1, Car c44 is at location l0, Car c10 is at location l1, Car c3 is at location l1, Car c30 is at location l0, Car c39 is at location l1, Car c24 is at location l1, Car c0 is at location l0, Car c29 is at location l0, Car c13 is at location l0, Car c2 is at location l0, Car c12 is at location l0, Car c25 is at location l0, Car c45 is at location l0, Car c1 is at location l1, Car c48 is at location l1, Car c41 is at location l1, Car c17 is at location l1, Car c27 is at location l1, Car c26 is at location l0, Car c36 is at location l0, Car c23 is at location l1, Car c34 is at location l1, Car c9 is at location l0, Car c32 is at location l1, Car c42 is at location l1, Car c31 is at location l1, Car c37 is at location l0, Car c47 is at location l1, Car c11 is at location l0, Car c20 is at location l0, Car c38 is at location l0, Car c43 is at location l0, Car c8 is at location l0, Car c49 is at location l1, Car c5 is at location l1, Car c6 is at location l1, Car c4 is at location l1, Car c18 is at location l1, Car c22 is at location l0, Car c19 is at location l0, Car c40 is at location l0, Car c7 is at location l1, Car c15 is at location l1, Car c28 is at location l1, Car c16 is at location l1, and Car c14 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Ferry has car ?c on board.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c8 l1)", "(on c39)", "(on c20)", "(at c2 l1)", "(at c15 l0)", "(on c22)", "(at c49 l0)", "(at c34 l0)", "(on c26)", "(at c29 l1)", "(on c14)", "(on c23)", "(at c10 l0)", "(at c44 l1)", "(on c33)", "(on c41)", "(on c36)", "(on c34)", "(on c16)", "(on c44)", "(at c39 l0)", "(at c20 l1)", "(at c9 l1)", "(at c31 l0)", "(at c4 l0)", "(at c46 l1)", "(on c48)", "(at c36 l1)", "(on c49)", "(at c17 l0)", "(at c42 l0)", "(on c45)", "(at c6 l0)", "(at c33 l0)", "(at c14 l1)", "(at c28 l0)", "(on c24)", "(at c35 l0)", "(on c17)", "(on c21)", "(at c18 l0)", "(at c27 l0)", "(on c6)", "(at c23 l0)", "(on c19)", "(at c45 l1)", "(at c19 l1)", "(at c24 l0)", "(on c47)", "(on c29)", "(on c13)", "(at c25 l1)", "(on c27)", "(at c3 l0)", "(at c30 l1)", "(at c40 l1)", "(at c37 l1)", "(at c16 l0)", "(at c0 l1)", "(at c43 l1)", "(at c11 l1)", "(on c37)", "(at c12 l1)", "(at c26 l1)", "(on c43)", "(at c21 l1)", "(on c11)", "(at c47 l0)", "(on c31)", "(on c25)", "(on c8)", "(on c18)", "(on c3)", "(at c48 l0)", "(on c9)", "(at c5 l0)", "(on c42)", "(on c40)", "(at c22 l1)", "(on c15)", "(on c4)", "(at c1 l0)", "(on c12)", "(on c0)", "(on c35)", "(at c13 l1)", "(on c2)", "(on c28)", "(on c30)", "(at c7 l0)", "(on c1)", "(on c5)", "(at c32 l0)", "(on c7)", "(on c10)", "(at c41 l0)", "(on c32)"], "yes": ["(on c38)", "(empty-ferry)", "(at-ferry l1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c2 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l1) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l1) (at c39 l1) (at c4 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l0) (not-eq l0 l1) (not-eq l1 l0) (on c46))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": -6619222775897535924, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c5, c3, c6, and c8 are at l2; c4, c7, and c9 are at l0; c1, c0, and c2 are at l1. The goal is to reach a state where the following facts hold: Car c5 is at location l2, Car c4 is at location l0, Car c1 is at location l1, Car c7 is at location l0, Car c0 is at location l2, Car c2 is at location l1, Car c3 is at location l2, Car c6 is at location l2, Car c8 is at location l2, and Car c9 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Car ?c is on board the ferry.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c9 l1)", "(at c8 l1)", "(at c3 l1)", "(at c3 l0)", "(on c9)", "(at c0 l0)", "(at c5 l0)", "(at c2 l0)", "(on c4)", "(at c6 l0)", "(at c1 l2)", "(at c1 l0)", "(at-ferry l0)", "(at c7 l2)", "(at c2 l2)", "(at c4 l2)", "(on c2)", "(at c8 l0)", "(on c6)", "(at c5 l1)", "(at c9 l2)", "(at c6 l1)", "(at c4 l1)", "(on c1)", "(on c5)", "(on c7)", "(at c7 l1)", "(on c8)", "(on c3)"], "yes": ["(on c0)", "(at-ferry l2)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l1) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": 5196274110229243987, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l1, with the car c6 on board. The cars are at locations as follows: c9, c4, and c2 are at l1; c1, c7, c3, c0, c5, and c8 are at l0. The goal is to reach a state where the following facts hold: Car c9 is at location l1, Car c4 is at location l0, Car c6 is at location l0, Car c1 is at location l0, Car c7 is at location l0, Car c2 is at location l1, Car c3 is at location l1, Car c0 is at location l0, Car c5 is at location l0, and Car c8 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on board the ferry.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c2 l0)", "(at c5 l1)", "(at c1 l1)", "(at c8 l1)", "(at c6 l1)", "(on c1)", "(at c0 l1)", "(on c5)", "(on c9)", "(on c2)", "(on c7)", "(at c7 l1)", "(on c0)", "(on c8)", "(at c9 l0)"], "yes": ["(on c3)", "(on c4)", "(at-ferry l0)", "(empty-ferry)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l1) (at c5 l0) (at c7 l0) (at c8 l0) (at c9 l1) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c6))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
{"id": 4772210962720336132, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 5 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c4, c3, and c0 are at l0; c1 and c2 are at l1. The goal is to reach a state where the following facts hold: Car c4 is at location l0, Car c1 is at location l0, Car c0 is at location l1, Car c2 is at location l1, and Car c3 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Ferry has car ?c on board.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c2 l0)", "(on c4)", "(at c4 l1)", "(at c3 l1)", "(on c2)", "(on c3)"], "yes": ["(on c0)", "(at-ferry l1)", "(on c1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c5)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c2 l1) (at c3 l0) (at c4 l0) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l0)))\n)"}
{"id": 4040494672687253167, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c6, c1, c3, c0, c5, c8, and c9 are at l0; c4, c2, and c7 are at l1. The goal is to reach a state where the following facts hold: Car c9 is at location l1, Car c4 is at location l0, Car c6 is at location l0, Car c1 is at location l0, Car c7 is at location l0, Car c2 is at location l1, Car c3 is at location l1, Car c0 is at location l0, Car c5 is at location l0, and Car c8 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on the ferry.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c2 l0)", "(at c5 l1)", "(at c1 l1)", "(at c8 l1)", "(at c6 l1)", "(on c1)", "(at c0 l1)", "(on c5)", "(on c2)", "(on c0)", "(on c8)", "(on c6)"], "yes": ["(on c3)", "(at-ferry l1)", "(on c4)", "(on c7)", "(on c9)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l1) (at c5 l0) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
{"id": 8140728862242584052, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c6, c1, c7, c5, c8, and c9 are at l0; c4, c0, c2, and c3 are at l1. The goal is to reach a state where the following facts hold: Car c9 is at location l1, Car c4 is at location l0, Car c6 is at location l0, Car c1 is at location l0, Car c7 is at location l0, Car c2 is at location l1, Car c3 is at location l1, Car c0 is at location l0, Car c5 is at location l0, and Car c8 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on board the ferry.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c2 l0)", "(at c5 l1)", "(at c1 l1)", "(at c8 l1)", "(at c6 l1)", "(on c1)", "(on c5)", "(at c3 l0)", "(on c2)", "(on c7)", "(at c7 l1)", "(on c8)", "(on c6)", "(on c3)"], "yes": ["(on c0)", "(at-ferry l0)", "(on c4)", "(on c9)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l1) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l0) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
{"id": -7265803470829972654, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l1, with the car c5 on board. The cars are at locations as follows: c9, c4, and c2 are at l1; c6, c1, c7, c3, c0, and c8 are at l0. The goal is to reach a state where the following facts hold: Car c9 is at location l1, Car c4 is at location l0, Car c6 is at location l0, Car c1 is at location l0, Car c7 is at location l0, Car c2 is at location l1, Car c3 is at location l1, Car c0 is at location l0, Car c5 is at location l0, and Car c8 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on the ferry.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c2 l0)", "(at c5 l1)", "(at c1 l1)", "(at c8 l1)", "(at c6 l1)", "(on c1)", "(at c0 l1)", "(on c9)", "(on c2)", "(on c7)", "(at c7 l1)", "(on c0)", "(on c8)", "(on c6)", "(at c9 l0)"], "yes": ["(on c3)", "(on c4)", "(at-ferry l0)", "(empty-ferry)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l1) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c5))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
