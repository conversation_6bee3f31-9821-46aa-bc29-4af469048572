{"id": 3720623087852070209, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1, with the car c4 on board. The cars are at locations as follows: c5, c8, c0, c7, and c1 are at l1; c9, c6, and c2 are at l0; c3 is at l2. The goal is to reach a state where the following facts hold: Car c9 is at location l0, Car c3 is at location l2, Car c8 is at location l2, Car c0 is at location l2, Car c4 is at location l0, Car c7 is at location l0, Car c5 is at location l2, Car c1 is at location l1, Car c6 is at location l2, and Car c2 is at location l1. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc, and (debark ?car ?loc) - debark the car ?car to location ?loc from the ferry.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(debark c4 l1)", "(sail l1 l2)"], "opt": "23", "yes": ["(sail l1 l0)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l1) (at c1 l1) (at c2 l0) (at c3 l2) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l1) (at c9 l0) (at-ferry l1) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1) (on c4))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": 5232995746465929066, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l3 location and it is empty. The cars are at locations as follows: c0 is at l3; c2 is at l4; c1 is at l0. The goal is to reach a state where the following facts hold: Car c1 is at location l3, Car c0 is at location l3, and Car c2 is at location l3. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(board c0 l3)", "(sail l3 l2)", "(sail l3 l1)"], "opt": "8", "yes": ["(sail l3 l4)", "(sail l3 l0)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l3) (at c1 l0) (at c2 l4) (at-ferry l3) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": 3986102885302007823, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c33 on board. The cars are at locations as follows: c9, c36, c21, c22, c6, c26, c8, c0, c25, c20, c14, c37, c19, c13, c44, c30, c2, c29, c35, c11, and c38 are at l0; c46, c16, c18, c45, c48, c3, c23, c31, c32, c1, c34, c42, c49, c7, c12, c41, c15, c17, c39, c47, c27, c24, c43, c5, c40, c28, c4, and c10 are at l1. The goal is to reach a state where the following facts hold: Car c9 is at location l0, Car c16 is at location l1, Car c18 is at location l1, Car c36 is at location l0, Car c21 is at location l0, Car c48 is at location l1, Car c3 is at location l1, Car c22 is at location l0, Car c23 is at location l1, Car c31 is at location l1, Car c32 is at location l1, Car c1 is at location l1, Car c45 is at location l0, Car c46 is at location l0, Car c6 is at location l1, Car c8 is at location l0, Car c26 is at location l0, Car c0 is at location l0, Car c34 is at location l1, Car c42 is at location l1, Car c49 is at location l1, Car c7 is at location l1, Car c25 is at location l0, Car c20 is at location l0, Car c14 is at location l0, Car c37 is at location l0, Car c41 is at location l1, Car c19 is at location l0, Car c12 is at location l0, Car c13 is at location l0, Car c15 is at location l1, Car c44 is at location l0, Car c17 is at location l1, Car c39 is at location l1, Car c47 is at location l1, Car c27 is at location l1, Car c35 is at location l1, Car c24 is at location l1, Car c30 is at location l0, Car c40 is at location l0, Car c2 is at location l0, Car c29 is at location l0, Car c5 is at location l1, Car c4 is at location l1, Car c28 is at location l1, Car c11 is at location l0, Car c43 is at location l0, Car c33 is at location l1, Car c38 is at location l0, and Car c10 is at location l1. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at the location ?loc, and (debark ?car ?loc) - debark the car ?car to location ?loc from the ferry.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(debark c33 l0)"], "opt": "25", "yes": ["(sail l0 l1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l1) (at c11 l0) (at c12 l1) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c2 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l1) (at c30 l0) (at c31 l1) (at c32 l1) (at c34 l1) (at c35 l0) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c4 l1) (at c40 l1) (at c41 l1) (at c42 l1) (at c43 l1) (at c44 l0) (at c45 l1) (at c46 l1) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l0) (not-eq l0 l1) (not-eq l1 l0) (on c33))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": 3796039508516239775, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c5, c1, and c2 are at l1; c9, c4, and c7 are at l0; c3, c8, c0, and c6 are at l2. The goal is to reach a state where the following facts hold: Car c9 is at location l0, Car c3 is at location l2, Car c8 is at location l2, Car c0 is at location l2, Car c4 is at location l0, Car c7 is at location l0, Car c5 is at location l2, Car c1 is at location l1, Car c6 is at location l2, and Car c2 is at location l1. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board car ?car at location ?loc, and (debark ?car ?loc) - debark car ?car to location ?loc from the ferry.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(board c1 l1)"], "no": ["(board c2 l1)", "(sail l1 l0)", "(sail l1 l2)"], "opt": "3", "yes": ["(board c5 l1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l1) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": -2144893140010862288, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c46 on board. The cars are at locations as follows: c9, c21, c22, c45, c26, c8, c0, c25, c20, c14, c37, c19, c12, c13, c44, c30, c40, c2, c29, c11, c43, and c38 are at l0; c16, c18, c48, c3, c23, c31, c32, c1, c36, c6, c34, c42, c49, c7, c41, c15, c17, c39, c47, c27, c35, c24, c5, c28, c4, c33, and c10 are at l1. The goal is to reach a state where the following facts hold: Car c9 is at location l0, Car c16 is at location l1, Car c18 is at location l1, Car c36 is at location l0, Car c21 is at location l0, Car c48 is at location l1, Car c3 is at location l1, Car c22 is at location l0, Car c23 is at location l1, Car c31 is at location l1, Car c32 is at location l1, Car c1 is at location l1, Car c45 is at location l0, Car c46 is at location l0, Car c6 is at location l1, Car c8 is at location l0, Car c26 is at location l0, Car c0 is at location l0, Car c34 is at location l1, Car c42 is at location l1, Car c49 is at location l1, Car c7 is at location l1, Car c25 is at location l0, Car c20 is at location l0, Car c14 is at location l0, Car c37 is at location l0, Car c41 is at location l1, Car c19 is at location l0, Car c12 is at location l0, Car c13 is at location l0, Car c15 is at location l1, Car c44 is at location l0, Car c17 is at location l1, Car c39 is at location l1, Car c47 is at location l1, Car c27 is at location l1, Car c35 is at location l1, Car c24 is at location l1, Car c30 is at location l0, Car c40 is at location l0, Car c2 is at location l0, Car c29 is at location l0, Car c5 is at location l1, Car c4 is at location l1, Car c28 is at location l1, Car c11 is at location l0, Car c43 is at location l0, Car c33 is at location l1, Car c38 is at location l0, and Car c10 is at location l1. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - embark the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark car ?car to location ?loc from the ferry.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(debark c46 l1)"], "opt": "6", "yes": ["(sail l1 l0)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c2 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l1) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l1) (at c37 l0) (at c38 l0) (at c39 l1) (at c4 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c46))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": 9032593359809429399, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c36 on board. The cars are at locations as follows: c9, c21, c22, c33, c6, c26, c8, c0, c25, c20, c14, c19, c12, c13, c44, c30, c2, c29, c35, c11, and c38 are at l0; c46, c16, c18, c45, c48, c3, c23, c31, c32, c1, c34, c42, c49, c7, c41, c15, c17, c39, c47, c27, c24, c37, c43, c5, c40, c28, c4, and c10 are at l1. The goal is to reach a state where the following facts hold: Car c9 is at location l0, Car c16 is at location l1, Car c18 is at location l1, Car c36 is at location l0, Car c21 is at location l0, Car c48 is at location l1, Car c3 is at location l1, Car c22 is at location l0, Car c23 is at location l1, Car c31 is at location l1, Car c32 is at location l1, Car c1 is at location l1, Car c45 is at location l0, Car c46 is at location l0, Car c6 is at location l1, Car c8 is at location l0, Car c26 is at location l0, Car c0 is at location l0, Car c34 is at location l1, Car c42 is at location l1, Car c49 is at location l1, Car c7 is at location l1, Car c25 is at location l0, Car c20 is at location l0, Car c14 is at location l0, Car c37 is at location l0, Car c41 is at location l1, Car c19 is at location l0, Car c12 is at location l0, Car c13 is at location l0, Car c15 is at location l1, Car c44 is at location l0, Car c17 is at location l1, Car c39 is at location l1, Car c47 is at location l1, Car c27 is at location l1, Car c35 is at location l1, Car c24 is at location l1, Car c30 is at location l0, Car c40 is at location l0, Car c2 is at location l0, Car c29 is at location l0, Car c5 is at location l1, Car c4 is at location l1, Car c28 is at location l1, Car c11 is at location l0, Car c43 is at location l0, Car c33 is at location l1, Car c38 is at location l0, and Car c10 is at location l1. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark the car ?car to location ?loc from the ferry.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(sail l0 l1)"], "opt": "27", "yes": ["(debark c36 l0)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c2 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l1) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l0) (at c34 l1) (at c35 l0) (at c37 l1) (at c38 l0) (at c39 l1) (at c4 l1) (at c40 l1) (at c41 l1) (at c42 l1) (at c43 l1) (at c44 l0) (at c45 l1) (at c46 l1) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l0) (not-eq l0 l1) (not-eq l1 l0) (on c36))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": 5728847629945548148, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2, with the car c6 on board. The cars are at locations as follows: c5, c8, c0, c7, and c1 are at l1; c9, c4, and c2 are at l0; c3 is at l2. The goal is to reach a state where the following facts hold: Car c9 is at location l0, Car c3 is at location l2, Car c8 is at location l2, Car c0 is at location l2, Car c4 is at location l0, Car c7 is at location l0, Car c5 is at location l2, Car c1 is at location l1, Car c6 is at location l2, and Car c2 is at location l1. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(sail l2 l1)", "(sail l2 l0)"], "opt": "19", "yes": ["(debark c6 l2)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l1) (at c1 l1) (at c2 l0) (at c3 l2) (at c4 l0) (at c5 l1) (at c7 l1) (at c8 l1) (at c9 l0) (at-ferry l2) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1) (on c6))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": 7406490636261056033, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l4 location and it is empty. The cars are at locations as follows: c2 and c0 are at l4; c1 is at l0. The goal is to reach a state where the following facts hold: Car c1 is at location l3, Car c0 is at location l3, and Car c2 is at location l3. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - load the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(sail l4 l0)"], "no": ["(sail l4 l2)", "(sail l4 l1)", "(sail l4 l3)"], "opt": "11", "yes": ["(board c0 l4)", "(board c2 l4)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c2 l4) (at-ferry l4) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": 5072641698198347651, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c9, c15, c33, c6, c3, c8, c0, c20, c14, c37, c17, c12, c44, c16, c30, c2, c29, c35, c11, c18, and c23 are at l0; c46, c45, c26, c48, c22, c31, c13, c32, c38, c1, c36, c34, c42, c49, c7, c25, c21, c41, c39, c47, c27, c24, c43, c5, c40, c28, c4, c19, and c10 are at l1. The goal is to reach a state where the following facts hold: Car c9 is at location l0, Car c16 is at location l1, Car c18 is at location l1, Car c36 is at location l0, Car c21 is at location l0, Car c48 is at location l1, Car c3 is at location l1, Car c22 is at location l0, Car c23 is at location l1, Car c31 is at location l1, Car c32 is at location l1, Car c1 is at location l1, Car c45 is at location l0, Car c46 is at location l0, Car c6 is at location l1, Car c8 is at location l0, Car c26 is at location l0, Car c0 is at location l0, Car c34 is at location l1, Car c42 is at location l1, Car c49 is at location l1, Car c7 is at location l1, Car c25 is at location l0, Car c20 is at location l0, Car c14 is at location l0, Car c37 is at location l0, Car c41 is at location l1, Car c19 is at location l0, Car c12 is at location l0, Car c13 is at location l0, Car c15 is at location l1, Car c44 is at location l0, Car c17 is at location l1, Car c39 is at location l1, Car c47 is at location l1, Car c27 is at location l1, Car c35 is at location l1, Car c24 is at location l1, Car c30 is at location l0, Car c40 is at location l0, Car c2 is at location l0, Car c29 is at location l0, Car c5 is at location l1, Car c4 is at location l1, Car c28 is at location l1, Car c11 is at location l0, Car c43 is at location l0, Car c33 is at location l1, Car c38 is at location l0, and Car c10 is at location l1. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(board c40 l1)", "(board c36 l1)", "(board c28 l1)", "(board c49 l1)", "(board c7 l1)", "(board c41 l1)", "(sail l1 l0)", "(board c10 l1)", "(board c43 l1)", "(board c31 l1)", "(board c46 l1)", "(board c5 l1)", "(board c45 l1)", "(board c22 l1)", "(board c1 l1)", "(board c42 l1)", "(board c21 l1)", "(board c26 l1)", "(board c4 l1)", "(board c48 l1)", "(board c39 l1)", "(board c27 l1)", "(board c25 l1)", "(board c32 l1)"], "no": ["(board c24 l1)", "(board c34 l1)", "(board c47 l1)"], "opt": "65", "yes": ["(board c38 l1)", "(board c19 l1)", "(board c13 l1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l1) (at c14 l0) (at c15 l0) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c2 l0) (at c20 l0) (at c21 l1) (at c22 l1) (at c23 l0) (at c24 l1) (at c25 l1) (at c26 l1) (at c27 l1) (at c28 l1) (at c29 l0) (at c3 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l0) (at c34 l1) (at c35 l0) (at c36 l1) (at c37 l0) (at c38 l1) (at c39 l1) (at c4 l1) (at c40 l1) (at c41 l1) (at c42 l1) (at c43 l1) (at c44 l0) (at c45 l1) (at c46 l1) (at c47 l1) (at c48 l1) (at c49 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l0) (at c3 l1) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l0) (at c9 l0) (at c10 l1) (at c11 l0) (at c12 l0) (at c13 l0) (at c14 l0) (at c15 l1) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l0) (at c20 l0) (at c21 l0) (at c22 l0) (at c23 l1) (at c24 l1) (at c25 l0) (at c26 l0) (at c27 l1) (at c28 l1) (at c29 l0) (at c30 l0) (at c31 l1) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l1) (at c36 l0) (at c37 l0) (at c38 l0) (at c39 l1) (at c40 l0) (at c41 l1) (at c42 l1) (at c43 l0) (at c44 l0) (at c45 l0) (at c46 l0) (at c47 l1) (at c48 l1) (at c49 l1)))\n)"}
{"id": 2563224274857898527, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1, with the car c0 on board. The cars are at locations as follows: c5, c8, c7, and c1 are at l1; c9, c4, and c2 are at l0; c3 and c6 are at l2. The goal is to reach a state where the following facts hold: Car c9 is at location l0, Car c3 is at location l2, Car c8 is at location l2, Car c0 is at location l2, Car c4 is at location l0, Car c7 is at location l0, Car c5 is at location l2, Car c1 is at location l1, Car c6 is at location l2, and Car c2 is at location l1. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark the car ?car to location ?loc from the ferry.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(debark c0 l1)", "(sail l1 l0)"], "opt": "16", "yes": ["(sail l1 l2)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c1 l1) (at c2 l0) (at c3 l2) (at c4 l0) (at c5 l1) (at c6 l2) (at c7 l1) (at c8 l1) (at c9 l0) (at-ferry l1) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1) (on c0))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": -6003545580663971528, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 5 cars, numbered consecutively. \nCurrently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0, c4, and c1 are at l0; c3 and c2 are at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l1, Car c4 is at location l0, Car c1 is at location l0, Car c3 is at location l0, and Car c2 is at location l1. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board the car ?car at the location ?loc, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(board c2 l1)", "(sail l1 l0)"], "opt": "6", "yes": ["(board c3 l1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c5)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l0)))\n)"}
{"id": 833470380712381140, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l1, with the car c7 on board. The cars are at locations as follows: c5, c3, and c2 are at l1; c9, c8, c0, c4, c1, and c6 are at l0. The goal is to reach a state where the following facts hold: Car c9 is at location l1, Car c8 is at location l0, Car c5 is at location l0, Car c0 is at location l0, Car c3 is at location l1, Car c4 is at location l0, Car c1 is at location l0, Car c6 is at location l0, Car c7 is at location l0, and Car c2 is at location l1. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - load the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark car ?car to location ?loc from the ferry.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(debark c7 l1)"], "opt": "8", "yes": ["(sail l1 l0)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l1) (at c6 l0) (at c8 l0) (at c9 l0) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c7))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
{"id": 1438971234652946092, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 20 cars, numbered consecutively. \nCurrently, the ferry is at l1, with the car c16 on board. The cars are at locations as follows: c9, c15, c6, c3, c8, c0, c14, c17, c13, c2, c18, and c10 are at l0; c1, c7, c12, c11, c5, c4, and c19 are at l1. The goal is to reach a state where the following facts hold: Car c9 is at location l0, Car c16 is at location l1, Car c18 is at location l1, Car c15 is at location l0, Car c1 is at location l1, Car c3 is at location l0, Car c6 is at location l1, Car c0 is at location l0, Car c7 is at location l1, Car c12 is at location l1, Car c11 is at location l1, Car c13 is at location l0, Car c14 is at location l1, Car c17 is at location l1, Car c2 is at location l1, Car c5 is at location l1, Car c8 is at location l1, Car c4 is at location l1, Car c19 is at location l1, and Car c10 is at location l0. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(sail l1 l0)"], "opt": "25", "yes": ["(debark c16 l1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c20)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l0) (at c11 l1) (at c12 l1) (at c13 l0) (at c14 l0) (at c15 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c2 l0) (at c3 l0) (at c4 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c16))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l1) (at c3 l0) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l1) (at c9 l0) (at c10 l0) (at c11 l1) (at c12 l1) (at c13 l0) (at c14 l1) (at c15 l0) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l1)))\n)"}
{"id": -1598347868139746874, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 20 cars, numbered consecutively. \nCurrently, the ferry is at l0, with the car c8 on board. The cars are at locations as follows: c9, c15, c3, c0, c13, c18, and c10 are at l0; c16, c1, c6, c7, c12, c11, c14, c17, c2, c5, c4, and c19 are at l1. The goal is to reach a state where the following facts hold: Car c9 is at location l0, Car c16 is at location l1, Car c18 is at location l1, Car c15 is at location l0, Car c1 is at location l1, Car c3 is at location l0, Car c6 is at location l1, Car c0 is at location l0, Car c7 is at location l1, Car c12 is at location l1, Car c11 is at location l1, Car c13 is at location l0, Car c14 is at location l1, Car c17 is at location l1, Car c2 is at location l1, Car c5 is at location l1, Car c8 is at location l1, Car c4 is at location l1, Car c19 is at location l1, and Car c10 is at location l0. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - load the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark car ?car to location ?loc from the ferry.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(debark c8 l0)"], "opt": "6", "yes": ["(sail l0 l1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c20)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c10 l0) (at c11 l1) (at c12 l1) (at c13 l0) (at c14 l1) (at c15 l0) (at c16 l1) (at c17 l1) (at c18 l0) (at c19 l1) (at c2 l1) (at c3 l0) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c9 l0) (at-ferry l0) (not-eq l0 l1) (not-eq l1 l0) (on c8))\n    (:goal (and (at c0 l0) (at c1 l1) (at c2 l1) (at c3 l0) (at c4 l1) (at c5 l1) (at c6 l1) (at c7 l1) (at c8 l1) (at c9 l0) (at c10 l0) (at c11 l1) (at c12 l1) (at c13 l0) (at c14 l1) (at c15 l0) (at c16 l1) (at c17 l1) (at c18 l1) (at c19 l1)))\n)"}
{"id": -6101245395997345052, "group": "goal_closer_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 3 locations and 2 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 is at l0; c1 is at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l0 and Car c1 is at location l2. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": [], "no": ["(sail l0 l2)", "(board c0 l0)"], "opt": "4", "yes": ["(sail l0 l1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c2)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 - car l0 l1 l2 - location)\n    (:init (at c0 l0) (at c1 l1) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1))\n    (:goal (and (at c0 l0) (at c1 l2)))\n)"}
