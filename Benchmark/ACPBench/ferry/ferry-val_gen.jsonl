{"id": 7083926670825615192, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2 and c0 are at l4; c1 is at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c1 is at location l3, and Car c0 is at location l3. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark the car ?car to location ?loc from the ferry.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c1 l0) (sail l0 l2) (sail l2 l0) (sail l0 l3) (sail l0 l4) (sail l3 l4) (board c2 l4) (sail l4 l3) (debark c2 l3) (sail l3 l4) (board c0 l4) (sail l4 l3) (debark c0 l3) (sail l3 l4)\"?", "answer": 4, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c2 l4) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": -3121206730712270586, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c1 and c0 are at l0; c2 is at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l1, Car c2 is at location l1, and Car c1 is at location l0. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at the location ?loc, and (debark ?car ?loc) - debark the car ?car to location ?loc from the ferry.", "question": "What is the first inapplicable action in the next sequence of actions: \"(sail l1 l0) (debark c2 l1) (debark c0 l0) (board c0 l0) (debark c0 l0) (board c0 l0) (sail l0 l1) (debark c0 l1) (board c0 l1) (debark c0 l1)\"?", "answer": 1, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1)))\n)"}
{"id": 1594039080993898328, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c44, c48, c32, c46, c21, c33, c24, c19, c34, and c13 are at l1; c5, c39, c7, c22, c43, c4, c37, c15, c3, c29, c42, c23, c49, and c31 are at l3; c18, c11, c6, c17, c9, c25, c1, c41, c40, and c27 are at l0; c38, c47, c0, c26, c14, c20, c10, c2, c45, c35, c28, and c36 are at l4; c8, c12, c16, and c30 are at l2. The goal is to reach a state where the following facts hold: Car c22 is at location l2, Car c46 is at location l4, Car c18 is at location l0, Car c35 is at location l2, Car c42 is at location l0, Car c15 is at location l1, Car c38 is at location l4, Car c6 is at location l3, Car c49 is at location l1, Car c27 is at location l4, Car c0 is at location l2, Car c43 is at location l2, Car c10 is at location l0, Car c12 is at location l3, Car c36 is at location l0, Car c39 is at location l2, Car c17 is at location l0, Car c37 is at location l3, Car c40 is at location l2, Car c16 is at location l0, Car c26 is at location l2, Car c3 is at location l2, Car c8 is at location l4, Car c20 is at location l3, Car c9 is at location l4, Car c32 is at location l1, Car c13 is at location l3, Car c48 is at location l0, Car c30 is at location l4, Car c11 is at location l1, Car c1 is at location l0, Car c29 is at location l4, Car c21 is at location l2, Car c5 is at location l4, Car c41 is at location l4, Car c34 is at location l3, Car c23 is at location l3, Car c25 is at location l3, Car c4 is at location l0, Car c31 is at location l3, Car c33 is at location l1, Car c44 is at location l0, Car c7 is at location l0, Car c2 is at location l1, Car c28 is at location l4, Car c19 is at location l1, Car c47 is at location l0, Car c45 is at location l0, Car c24 is at location l0, and Car c14 is at location l4. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board the car ?car at the location ?loc, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c13 l1) (sail l1 l3) (debark c13 l3) (board c22 l3) (sail l3 l2) (debark c22 l2) (board c16 l2) (sail l2 l0) (debark c16 l0) (board c27 l0) (sail l0 l4) (debark c27 l4) (board c10 l4) (sail l4 l0) (debark c10 l0) (board c40 l0) (sail l0 l2) (debark c40 l2) (sail l2 l4) (board c2 l4) (sail l4 l1) (debark c2 l1) (board c21 l1) (sail l1 l2) (debark c21 l2) (sail l2 l4) (board c20 l4) (sail l4 l3) (debark c20 l3) (board c15 l3) (sail l3 l0) (sail l0 l1) (debark c15 l1) (board c24 l1) (sail l1 l0) (debark c24 l0) (board c41 l0) (sail l0 l4) (debark c41 l4) (board c36 l4) (sail l4 l0) (board c16 l3) (board c9 l0) (sail l0 l4) (debark c9 l4) (board c45 l4) (sail l4 l0) (debark c45 l0) (board c11 l0) (sail l0 l1) (debark c11 l1) (board c34 l1) (sail l1 l0) (sail l0 l3) (debark c34 l3) (board c29 l3) (sail l3 l0) (sail l0 l4) (debark c29 l4) (board c47 l4) (sail l4 l0) (debark c47 l0) (board c25 l0) (sail l0 l1) (sail l1 l3) (debark c25 l3) (board c39 l3) (sail l3 l1) (sail l1 l2) (debark c39 l2) (board c12 l2) (sail l2 l3) (debark c12 l3) (board c43 l3) (sail l3 l1) (sail l1 l2) (debark c43 l2) (board c30 l2) (sail l2 l3) (sail l3 l4) (debark c30 l4) (board c26 l4) (sail l4 l2) (debark c26 l2) (board c8 l2) (sail l2 l3) (sail l3 l4) (debark c8 l4) (board c35 l4) (sail l4 l2) (debark c35 l2) (sail l2 l3) (board c4 l3) (sail l3 l0) (debark c4 l0) (board c6 l0) (sail l0 l1) (sail l1 l3) (debark c6 l3) (board c42 l3) (sail l3 l0) (debark c42 l0) (sail l0 l1) (board c44 l1) (sail l1 l0) (debark c44 l0) (sail l0 l1) (board c46 l1) (sail l1 l2) (sail l2 l4) (debark c46 l4) (sail l4 l3) (board c3 l3) (sail l3 l1) (debark c3 l1) (board c48 l1) (sail l1 l0) (debark c48 l0) (sail l0 l3) (board c49 l3) (sail l3 l1) (debark c49 l1) (sail l1 l3) (board c5 l3) (sail l3 l4) (debark c5 l4) (board c0 l4) (sail l4 l0) (sail l0 l2) (debark c0 l2) (sail l2 l3) (board c7 l3) (sail l3 l0) (debark c7 l0) (sail l0 l1) (board c3 l1) (sail l1 l2) (debark c3 l2)\"?", "answer": 41, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c50)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c10 c11 c12 c13 c14 c15 c16 c17 c18 c19 c2 c20 c21 c22 c23 c24 c25 c26 c27 c28 c29 c3 c30 c31 c32 c33 c34 c35 c36 c37 c38 c39 c4 c40 c41 c42 c43 c44 c45 c46 c47 c48 c49 c5 c6 c7 c8 c9 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c10 l4) (at c11 l0) (at c12 l2) (at c13 l1) (at c14 l4) (at c15 l3) (at c16 l2) (at c17 l0) (at c18 l0) (at c19 l1) (at c2 l4) (at c20 l4) (at c21 l1) (at c22 l3) (at c23 l3) (at c24 l1) (at c25 l0) (at c26 l4) (at c27 l0) (at c28 l4) (at c29 l3) (at c3 l3) (at c30 l2) (at c31 l3) (at c32 l1) (at c33 l1) (at c34 l1) (at c35 l4) (at c36 l4) (at c37 l3) (at c38 l4) (at c39 l3) (at c4 l3) (at c40 l0) (at c41 l0) (at c42 l3) (at c43 l3) (at c44 l1) (at c45 l4) (at c46 l1) (at c47 l4) (at c48 l1) (at c49 l3) (at c5 l3) (at c6 l0) (at c7 l3) (at c8 l2) (at c9 l0) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l2) (at c1 l0) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l4) (at c6 l3) (at c7 l0) (at c8 l4) (at c9 l4) (at c10 l0) (at c11 l1) (at c12 l3) (at c13 l3) (at c14 l4) (at c15 l1) (at c16 l0) (at c17 l0) (at c18 l0) (at c19 l1) (at c20 l3) (at c21 l2) (at c22 l2) (at c23 l3) (at c24 l0) (at c25 l3) (at c26 l2) (at c27 l4) (at c28 l4) (at c29 l4) (at c30 l4) (at c31 l3) (at c32 l1) (at c33 l1) (at c34 l3) (at c35 l2) (at c36 l0) (at c37 l3) (at c38 l4) (at c39 l2) (at c40 l2) (at c41 l4) (at c42 l0) (at c43 l2) (at c44 l0) (at c45 l0) (at c46 l4) (at c47 l0) (at c48 l0) (at c49 l1)))\n)"}
{"id": -96290682638232196, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2 and c0 are at l4; c1 is at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c1 is at location l3, and Car c0 is at location l3. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c1 l0) (sail l1 l0) (sail l4 l3) (debark c1 l3) (sail l3 l4) (board c0 l4) (sail l4 l2) (sail l2 l3) (debark c0 l3) (sail l3 l4) (board c2 l4) (sail l4 l3) (debark c2 l3)\"?", "answer": 1, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c2 l4) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": -6989934738347023845, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c1 and c0 are at l0; c2 is at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l1, Car c2 is at location l1, and Car c1 is at location l0. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - load the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc.", "question": "What is the first inapplicable action in the next sequence of actions: \"(sail l0 l1) (board c0 l0) (sail l0 l1) (debark c0 l1)\"?", "answer": 0, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1)))\n)"}
{"id": 562075512090842637, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2 and c0 are at l4; c1 is at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c1 is at location l3, and Car c0 is at location l3. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark the car ?car to location ?loc from the ferry.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c1 l0) (sail l0 l3) (debark c1 l3) (sail l3 l4) (board c0 l4) (board c1 l3) (debark c0 l3) (sail l3 l4) (board c2 l4) (sail l4 l0) (sail l0 l3) (debark c2 l3) (sail l3 l2) (sail l2 l0)\"?", "answer": 5, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c2 l4) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": 8741027559757186158, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2 and c0 are at l4; c1 is at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c1 is at location l3, and Car c0 is at location l3. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark car ?car to location ?loc from the ferry.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c1 l0) (sail l0 l3) (debark c1 l3) (sail l2 l0) (board c0 l4) (sail l4 l3) (debark c0 l3) (sail l3 l4) (board c2 l4) (sail l4 l3) (debark c2 l3)\"?", "answer": 3, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c2 l4) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": -5573555009821772150, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2 and c0 are at l4; c1 is at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c1 is at location l3, and Car c0 is at location l3. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - embark the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark car ?car to location ?loc from the ferry.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c1 l0) (sail l0 l2) (sail l2 l3) (sail l1 l0) (sail l3 l4) (board c2 l4) (sail l4 l3) (debark c2 l3) (sail l3 l4) (board c0 l4) (sail l4 l3) (debark c0 l3) (sail l3 l4)\"?", "answer": 3, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c2 l4) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": -4143600109699337607, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2 and c0 are at l4; c1 is at l0. The goal is to reach a state where the following facts hold: Car c2 is at location l3, Car c1 is at location l3, and Car c0 is at location l3. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at the location ?loc, and (debark ?car ?loc) - debark the car ?car to location ?loc from the ferry.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c1 l0) (board c1 l2) (debark c1 l3) (sail l3 l1) (sail l1 l0) (sail l0 l4) (board c0 l4) (sail l4 l3) (debark c0 l3) (sail l3 l4) (board c2 l4) (sail l4 l3) (debark c2 l3)\"?", "answer": 1, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l5-c3)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 - car l0 l1 l2 l3 l4 - location)\n    (:init (at c0 l4) (at c1 l0) (at c2 l4) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l0 l3) (not-eq l0 l4) (not-eq l1 l0) (not-eq l1 l2) (not-eq l1 l3) (not-eq l1 l4) (not-eq l2 l0) (not-eq l2 l1) (not-eq l2 l3) (not-eq l2 l4) (not-eq l3 l0) (not-eq l3 l1) (not-eq l3 l2) (not-eq l3 l4) (not-eq l4 l0) (not-eq l4 l1) (not-eq l4 l2) (not-eq l4 l3))\n    (:goal (and (at c0 l3) (at c1 l3) (at c2 l3)))\n)"}
{"id": -4734096292622506916, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c7, c8, c0, c5, c4, and c1 are at l1; c3 is at l2; c9, c6, and c2 are at l0. The goal is to reach a state where the following facts hold: Car c8 is at location l2, Car c0 is at location l2, Car c4 is at location l0, Car c9 is at location l0, Car c7 is at location l0, Car c2 is at location l1, Car c6 is at location l2, Car c5 is at location l2, Car c3 is at location l2, and Car c1 is at location l1. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at the location ?loc, and (debark ?car ?loc) - debark the car ?car to location ?loc from the ferry.", "question": "What is the first inapplicable action in the next sequence of actions: \"(sail l2 l0) (sail l0 l1) (board c5 l1) (sail l1 l2) (debark c5 l2) (sail l2 l1) (board c8 l1) (sail l1 l2) (debark c8 l2) (sail l2 l1) (board c7 l1) (sail l1 l0) (debark c7 l0) (board c2 l0) (board c5 l1) (debark c2 l1) (board c4 l1) (sail l1 l0) (debark c4 l0) (board c6 l0) (sail l0 l2) (debark c6 l2) (sail l2 l1) (board c0 l1) (sail l1 l2) (debark c0 l2) (sail l2 l1)\"?", "answer": 14, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 l2 - location)\n    (:init (at c0 l1) (at c1 l1) (at c2 l0) (at c3 l2) (at c4 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l1) (at c9 l0) (at-ferry l2) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1))\n    (:goal (and (at c0 l2) (at c1 l1) (at c2 l1) (at c3 l2) (at c4 l0) (at c5 l2) (at c6 l2) (at c7 l0) (at c8 l2) (at c9 l0)))\n)"}
{"id": 4896696031890359153, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 5 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c3, c1, c0, and c2 are at l0; c4 is at l1. The goal is to reach a state where the following facts hold: Car c3 is at location l0, Car c4 is at location l0, Car c1 is at location l0, Car c0 is at location l1, and Car c2 is at location l1. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at the location ?loc, and (debark ?car ?loc) - debark the car ?car from the ferry to location ?loc.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c2 l0) (debark c2 l0) (board c2 l0) (sail l0 l1) (board c2 l1) (board c4 l1) (sail l1 l0) (debark c4 l0) (board c0 l0) (sail l0 l1) (debark c0 l1) (sail l1 l0)\"?", "answer": 4, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c5)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l0) (at c3 l0) (at c4 l1) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l0)))\n)"}
{"id": -4447316279887451134, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c3, c9, c8, c1, c6, c0, and c2 are at l0; c7, c5, and c4 are at l1. The goal is to reach a state where the following facts hold: Car c3 is at location l1, Car c9 is at location l1, Car c4 is at location l0, Car c1 is at location l0, Car c7 is at location l0, Car c2 is at location l1, Car c8 is at location l0, Car c5 is at location l0, Car c0 is at location l0, and Car c6 is at location l0. The available actions are: (sail ?from ?to) - sail from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c0 l0) (debark c0 l0) (board c9 l0) (sail l0 l1) (debark c9 l1) (board c5 l1) (sail l0 l1) (debark c5 l0) (board c2 l0) (sail l0 l1) (debark c2 l1) (board c4 l1) (sail l1 l0) (debark c4 l0) (board c3 l0) (sail l0 l1) (debark c3 l1) (board c7 l1) (sail l1 l0) (debark c7 l0)\"?", "answer": 6, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l0) (at c3 l0) (at c4 l1) (at c5 l1) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
{"id": 3468551644281451113, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 3 locations and 2 cars, numbered consecutively. \nCurrently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0 and c1 are at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l0 and Car c1 is at location l2. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - load the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - debark the car ?car to location ?loc from the ferry.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c0 l1) (sail l1 l2) (sail l2 l1) (sail l1 l0) (debark c0 l0) (board c0 l0) (sail l2 l1) (sail l0 l1) (board c1 l1) (sail l1 l2) (debark c1 l2)\"?", "answer": 6, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l3-c2)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 - car l0 l1 l2 - location)\n    (:init (at c0 l1) (at c1 l1) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l0 l2) (not-eq l1 l0) (not-eq l1 l2) (not-eq l2 l0) (not-eq l2 l1))\n    (:goal (and (at c0 l0) (at c1 l2)))\n)"}
{"id": -6291233197210223159, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 5 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c3, c1, c0, and c2 are at l0; c4 is at l1. The goal is to reach a state where the following facts hold: Car c3 is at location l0, Car c4 is at location l0, Car c1 is at location l0, Car c0 is at location l1, and Car c2 is at location l1. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc on to the ferry, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c2 l0) (sail l0 l1) (debark c2 l1) (board c2 l1) (debark c2 l1) (sail l1 l0) (debark c1 l1) (sail l0 l1) (debark c0 l1) (board c2 l1) (debark c2 l1) (board c4 l1) (sail l1 l0) (debark c4 l0)\"?", "answer": 6, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c5)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l0) (at c3 l0) (at c4 l1) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l0)))\n)"}
{"id": 7630252500073167255, "group": "validation_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 2 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 and c1 are at l0. The goal is to reach a state where the following facts hold: Car c0 is at location l1 and Car c1 is at location l1. The available actions are: (sail ?from ?to) - travel by sea from location ?from to location ?to, (board ?car ?loc) - board the car ?car at location ?loc, and (debark ?car ?loc) - unload the car ?car from the ferry to location ?loc.", "question": "What is the first inapplicable action in the next sequence of actions: \"(board c0 l0) (debark c0 l0) (board c1 l0) (sail l0 l1) (debark c1 l1) (sail l1 l0) (board c0 l0) (debark c0 l0) (board c0 l1) (debark c0 l0) (board c0 l0) (debark c0 l0) (board c0 l0) (sail l0 l1) (debark c0 l1)\"?", "answer": 8, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c2)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l1)))\n)"}
