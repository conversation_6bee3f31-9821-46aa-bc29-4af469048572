{"id": -190862746056098394, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_14 is to the right of tile_13, tile_6 is to the right of tile_5, tile_4 is to the right of tile_3, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_5 is to the right of tile_4, tile_16 is to the right of tile_15, tile_17 is to the right of tile_16, tile_9 is to the right of tile_8, tile_21 is to the right of tile_20, tile_24 is to the right of tile_23, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_22 is to the right of tile_21, tile_15 is to the right of tile_14, tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_23 is to the right of tile_22, and tile_12 is to the right of tile_11. Further, tile_17 is down from tile_23, tile_5 is down from tile_11, tile_8 is down from tile_14, tile_2 is down from tile_8, tile_1 is down from tile_7, tile_18 is down from tile_24, tile_13 is down from tile_19, tile_15 is down from tile_21, tile_10 is down from tile_16, tile_4 is down from tile_10, tile_14 is down from tile_20, tile_3 is down from tile_9, tile_7 is down from tile_13, tile_11 is down from tile_17, tile_9 is down from tile_15, tile_16 is down from tile_22, tile_12 is down from tile_18, and tile_6 is down from tile_12 Currently, robot robot2 is at tile_1 and holding color white and robot robot1 is at tile_2 and holding color white; tile_4, tile_3, tile_6, tile_5, and tile_7 are clear; tile_21 is painted white, tile_13 is painted black, tile_24 is painted black, tile_18 is painted white, tile_9 is painted white, tile_20 is painted black, tile_16 is painted white, tile_11 is painted white, tile_19 is painted white, tile_10 is painted black, tile_8 is painted black, tile_14 is painted white, tile_23 is painted white, tile_12 is painted black, tile_17 is painted black, tile_22 is painted black, and tile_15 is painted black. The goal is to reach a state where the following facts hold: Tile tile_21 is painted in white color, Tile tile_14 is painted in white color, Tile tile_13 is painted in black color, Tile tile_24 is painted in black color, Tile tile_18 is painted in white color, Tile tile_20 is painted in black color, Tile tile_16 is painted in white color, Tile tile_11 is painted in white color, Tile tile_19 is painted in white color, Tile tile_23 is painted in white color, Tile tile_10 is painted in black color, Tile tile_12 is painted in black color, Tile tile_17 is painted in black color, Tile tile_9 is painted in white color, Tile tile_22 is painted in black color, Tile tile_7 is painted in white color, Tile tile_15 is painted in black color, and Tile tile_8 is painted in black color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Tile tile_7 is painted in white color", "answer": "yes"}
{"id": 2737400362836965353, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_14 is to the right of tile_13, tile_6 is to the right of tile_5, tile_4 is to the right of tile_3, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_5 is to the right of tile_4, tile_16 is to the right of tile_15, tile_17 is to the right of tile_16, tile_9 is to the right of tile_8, tile_21 is to the right of tile_20, tile_24 is to the right of tile_23, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_22 is to the right of tile_21, tile_15 is to the right of tile_14, tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_23 is to the right of tile_22, and tile_12 is to the right of tile_11. Further, tile_17 is down from tile_23, tile_5 is down from tile_11, tile_8 is down from tile_14, tile_2 is down from tile_8, tile_1 is down from tile_7, tile_18 is down from tile_24, tile_13 is down from tile_19, tile_15 is down from tile_21, tile_10 is down from tile_16, tile_4 is down from tile_10, tile_14 is down from tile_20, tile_3 is down from tile_9, tile_7 is down from tile_13, tile_11 is down from tile_17, tile_9 is down from tile_15, tile_16 is down from tile_22, tile_12 is down from tile_18, and tile_6 is down from tile_12 Currently, robot robot1 is at tile_9 and holding color black and robot robot2 is at tile_6 and holding color white; tile_10, tile_13, tile_4, tile_3, tile_2, tile_11, tile_17, tile_5, tile_12, tile_16, tile_1, and tile_7 are clear; tile_21 is painted white, tile_24 is painted black, tile_18 is painted white, tile_20 is painted black, tile_19 is painted white, tile_8 is painted black, tile_14 is painted white, tile_23 is painted white, tile_22 is painted black, and tile_15 is painted black. The goal is to reach a state where the following facts hold: Tile tile_21 is painted in white color, Tile tile_14 is painted in white color, Tile tile_13 is painted in black color, Tile tile_24 is painted in black color, Tile tile_18 is painted in white color, Tile tile_20 is painted in black color, Tile tile_16 is painted in white color, Tile tile_11 is painted in white color, Tile tile_19 is painted in white color, Tile tile_23 is painted in white color, Tile tile_10 is painted in black color, Tile tile_12 is painted in black color, Tile tile_17 is painted in black color, Tile tile_9 is painted in white color, Tile tile_22 is painted in black color, Tile tile_7 is painted in white color, Tile tile_15 is painted in black color, and Tile tile_8 is painted in black color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? tile_9 is clear", "answer": "yes"}
{"id": 3453803709505290290, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_1 is down from tile_4, and tile_9 is down from tile_12 Currently, robot robot2 is at tile_7 and holding color white and robot robot1 is at tile_9 and holding color white; tile_5, tile_12, tile_4, tile_8, tile_1, tile_2, tile_3, tile_11, and tile_6 are clear; tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? tile_9 is clear", "answer": "yes"}
{"id": 8682061298127632426, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_14 is to the right of tile_13, tile_6 is to the right of tile_5, tile_4 is to the right of tile_3, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_5 is to the right of tile_4, tile_16 is to the right of tile_15, tile_17 is to the right of tile_16, tile_9 is to the right of tile_8, tile_21 is to the right of tile_20, tile_24 is to the right of tile_23, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_22 is to the right of tile_21, tile_15 is to the right of tile_14, tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_23 is to the right of tile_22, and tile_12 is to the right of tile_11. Further, tile_17 is down from tile_23, tile_5 is down from tile_11, tile_8 is down from tile_14, tile_2 is down from tile_8, tile_1 is down from tile_7, tile_18 is down from tile_24, tile_13 is down from tile_19, tile_15 is down from tile_21, tile_10 is down from tile_16, tile_4 is down from tile_10, tile_14 is down from tile_20, tile_3 is down from tile_9, tile_7 is down from tile_13, tile_11 is down from tile_17, tile_9 is down from tile_15, tile_16 is down from tile_22, tile_12 is down from tile_18, and tile_6 is down from tile_12 Currently, robot robot2 is at tile_5 and holding color white and robot robot1 is at tile_9 and holding color white; tile_10, tile_15, tile_4, tile_23, tile_3, tile_2, tile_11, tile_6, tile_17, tile_16, and tile_1 are clear; tile_21 is painted white, tile_13 is painted black, tile_24 is painted black, tile_18 is painted white, tile_20 is painted black, tile_7 is painted white, tile_19 is painted white, tile_8 is painted black, tile_14 is painted white, tile_12 is painted black, and tile_22 is painted black. The goal is to reach a state where the following facts hold: Tile tile_21 is painted in white color, Tile tile_14 is painted in white color, Tile tile_13 is painted in black color, Tile tile_24 is painted in black color, Tile tile_18 is painted in white color, Tile tile_20 is painted in black color, Tile tile_16 is painted in white color, Tile tile_11 is painted in white color, Tile tile_19 is painted in white color, Tile tile_23 is painted in white color, Tile tile_10 is painted in black color, Tile tile_12 is painted in black color, Tile tile_17 is painted in black color, Tile tile_9 is painted in white color, Tile tile_22 is painted in black color, Tile tile_7 is painted in white color, Tile tile_15 is painted in black color, and Tile tile_8 is painted in black color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Tile tile_17 is painted in white color", "answer": "no"}
{"id": 7908210324502434104, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_14 is to the right of tile_13, tile_6 is to the right of tile_5, tile_4 is to the right of tile_3, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_20 is to the right of tile_19, tile_5 is to the right of tile_4, tile_16 is to the right of tile_15, tile_17 is to the right of tile_16, tile_9 is to the right of tile_8, tile_21 is to the right of tile_20, tile_24 is to the right of tile_23, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_22 is to the right of tile_21, tile_15 is to the right of tile_14, tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_23 is to the right of tile_22, and tile_12 is to the right of tile_11. Further, tile_17 is down from tile_23, tile_5 is down from tile_11, tile_8 is down from tile_14, tile_2 is down from tile_8, tile_1 is down from tile_7, tile_18 is down from tile_24, tile_13 is down from tile_19, tile_15 is down from tile_21, tile_10 is down from tile_16, tile_4 is down from tile_10, tile_14 is down from tile_20, tile_3 is down from tile_9, tile_7 is down from tile_13, tile_11 is down from tile_17, tile_9 is down from tile_15, tile_16 is down from tile_22, tile_12 is down from tile_18, and tile_6 is down from tile_12 Currently, robot robot1 is at tile_3 and holding color white and robot robot2 is at tile_1 and holding color white; tile_24, tile_10, tile_4, tile_2, tile_22, tile_11, tile_6, tile_5, tile_12, tile_16, and tile_18 are clear; tile_21 is painted white, tile_13 is painted black, tile_9 is painted white, tile_20 is painted black, tile_7 is painted white, tile_19 is painted white, tile_8 is painted black, tile_14 is painted white, tile_23 is painted white, tile_17 is painted black, and tile_15 is painted black. The goal is to reach a state where the following facts hold: Tile tile_21 is painted in white color, Tile tile_14 is painted in white color, Tile tile_13 is painted in black color, Tile tile_24 is painted in black color, Tile tile_18 is painted in white color, Tile tile_20 is painted in black color, Tile tile_16 is painted in white color, Tile tile_11 is painted in white color, Tile tile_19 is painted in white color, Tile tile_23 is painted in white color, Tile tile_10 is painted in black color, Tile tile_12 is painted in black color, Tile tile_17 is painted in black color, Tile tile_9 is painted in white color, Tile tile_22 is painted in black color, Tile tile_7 is painted in white color, Tile tile_15 is painted in black color, and Tile tile_8 is painted in black color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Tile tile_22 is painted in black color", "answer": "yes"}
{"id": -7745550363688375104, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 9 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, and tile_5 is to the right of tile_4. Further, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_5 is down from tile_8, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_1 and holding color black and robot robot1 is at tile_6 and holding color black; tile_5, tile_8, tile_2, tile_3, and tile_4 are clear; tile_7 is painted black and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is at tile_8 location", "answer": "no"}
{"id": 301051975412578273, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_14 is to the right of tile_13, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_13 is to the right of tile_12, tile_8 is to the right of tile_7, tile_4 is to the right of tile_3, tile_19 is to the right of tile_18, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_20 is to the right of tile_19, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_10 is down from tile_15, tile_13 is down from tile_18, tile_1 is down from tile_6, tile_14 is down from tile_19, tile_4 is down from tile_9, tile_11 is down from tile_16, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_9 is down from tile_14, tile_5 is down from tile_10, and tile_3 is down from tile_8 Currently, robot robot1 is at tile_8 and holding color white and robot robot2 is at tile_12 and holding color black; tile_10, tile_15, tile_13, tile_4, tile_14, tile_3, tile_2, tile_11, tile_6, tile_17, tile_5, tile_19, tile_1, tile_7, and tile_9 are clear; tile_18 is painted white, tile_16 is painted white, and tile_20 is painted white. The goal is to reach a state where the following facts hold: Tile tile_14 is painted in white color, Tile tile_13 is painted in black color, Tile tile_18 is painted in white color, Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_16 is painted in white color, Tile tile_9 is painted in black color, Tile tile_7 is painted in black color, Tile tile_19 is painted in black color, Tile tile_20 is painted in white color, Tile tile_17 is painted in black color, Tile tile_8 is painted in white color, and Tile tile_15 is painted in black color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? tile_12 is clear", "answer": "yes"}
{"id": 8271286163224714230, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_14 is to the right of tile_13, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_13 is to the right of tile_12, tile_8 is to the right of tile_7, tile_4 is to the right of tile_3, tile_19 is to the right of tile_18, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_20 is to the right of tile_19, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_10 is down from tile_15, tile_13 is down from tile_18, tile_1 is down from tile_6, tile_14 is down from tile_19, tile_4 is down from tile_9, tile_11 is down from tile_16, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_9 is down from tile_14, tile_5 is down from tile_10, and tile_3 is down from tile_8 Currently, robot robot2 is at tile_2 and holding color black, robot robot3 is at tile_4 and holding color white, and robot robot1 is at tile_1 and holding color white; tile_14, tile_3, tile_5, tile_7, and tile_9 are clear; tile_13 is painted black, tile_18 is painted white, tile_19 is painted black, tile_6 is painted white, tile_10 is painted white, tile_16 is painted white, tile_11 is painted black, tile_12 is painted white, tile_20 is painted white, tile_17 is painted black, tile_8 is painted white, and tile_15 is painted black. The goal is to reach a state where the following facts hold: Tile tile_14 is painted in white color, Tile tile_13 is painted in black color, Tile tile_18 is painted in white color, Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_16 is painted in white color, Tile tile_9 is painted in black color, Tile tile_7 is painted in black color, Tile tile_19 is painted in black color, Tile tile_20 is painted in white color, Tile tile_17 is painted in black color, Tile tile_8 is painted in white color, and Tile tile_15 is painted in black color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot2 is at tile_11 location", "answer": "no"}
{"id": -7132194892185575922, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 9 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, and tile_5 is to the right of tile_4. Further, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_5 is down from tile_8, and tile_1 is down from tile_4 Currently, robot robot1 is at tile_5 and holding color white and robot robot2 is at tile_6 and holding color white; tile_1, tile_2, tile_7, tile_3, and tile_4 are clear; tile_9 is painted black and tile_8 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? tile_5 is clear", "answer": "yes"}
{"id": 2724697049619125819, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_14 is to the right of tile_13, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_18 is to the right of tile_17, tile_10 is to the right of tile_9, tile_13 is to the right of tile_12, tile_8 is to the right of tile_7, tile_4 is to the right of tile_3, tile_19 is to the right of tile_18, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_20 is to the right of tile_19, tile_17 is to the right of tile_16, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_10 is down from tile_15, tile_13 is down from tile_18, tile_1 is down from tile_6, tile_14 is down from tile_19, tile_4 is down from tile_9, tile_11 is down from tile_16, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_9 is down from tile_14, tile_5 is down from tile_10, and tile_3 is down from tile_8 Currently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_4 and holding color white; tile_14, tile_3, tile_5, tile_19, tile_1, and tile_9 are clear; tile_13 is painted black, tile_18 is painted white, tile_6 is painted white, tile_10 is painted white, tile_16 is painted white, tile_11 is painted black, tile_12 is painted white, tile_7 is painted black, tile_20 is painted white, tile_17 is painted black, tile_8 is painted white, and tile_15 is painted black. The goal is to reach a state where the following facts hold: Tile tile_14 is painted in white color, Tile tile_13 is painted in black color, Tile tile_18 is painted in white color, Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_16 is painted in white color, Tile tile_9 is painted in black color, Tile tile_7 is painted in black color, Tile tile_19 is painted in black color, Tile tile_20 is painted in white color, Tile tile_17 is painted in black color, Tile tile_8 is painted in white color, and Tile tile_15 is painted in black color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot2 is at tile_11 location", "answer": "no"}
{"id": 7178470481686639462, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_1 is down from tile_4, and tile_9 is down from tile_12 Currently, robot robot2 is at tile_1 and holding color black and robot robot1 is at tile_3 and holding color white; tile_5, tile_8, tile_2, tile_7, and tile_4 are clear; tile_11 is painted black, tile_12 is painted white, tile_6 is painted white, tile_10 is painted white, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Tile tile_4 is painted in white color", "answer": "yes"}
{"id": 3428044754392798758, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_1 is down from tile_4, and tile_9 is down from tile_12 Currently, robot robot2 is at tile_1 and holding color white and robot robot1 is at tile_2 and holding color white; tile_5, tile_8, and tile_3 are clear; tile_11 is painted black, tile_12 is painted white, tile_6 is painted white, tile_10 is painted white, tile_7 is painted black, tile_9 is painted black, and tile_4 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is at tile_8 location", "answer": "no"}
{"id": 3568421205743707471, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_1 is down from tile_4, and tile_9 is down from tile_12 Currently, robot robot1 is at tile_3 and holding color white and robot robot2 is at tile_2 and holding color white; tile_6, tile_1, and tile_9 are clear; tile_11 is painted black, tile_12 is painted white, tile_10 is painted white, tile_7 is painted black, tile_8 is painted white, tile_5 is painted black, and tile_4 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot1 is at tile_7 location", "answer": "no"}
{"id": 1106730633809432161, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_1 is down from tile_4, and tile_9 is down from tile_12 Currently, robot robot1 is at tile_2 and holding color white and robot robot2 is at tile_6 and holding color black; tile_5, tile_8, tile_1, and tile_3 are clear; tile_11 is painted black, tile_12 is painted white, tile_10 is painted white, tile_7 is painted black, tile_9 is painted black, and tile_4 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? Robot robot2 is at tile_2 location", "answer": "no"}
{"id": -8388354108906682889, "group": "landmarks_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_12 is to the right of tile_11. Further, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_1 is down from tile_4, and tile_9 is down from tile_12 Currently, robot robot2 is at tile_1 and holding color white and robot robot1 is at tile_5 and holding color black; tile_2, tile_3, tile_4, and tile_6 are clear; tile_11 is painted black, tile_12 is painted white, tile_10 is painted white, tile_7 is painted black, tile_9 is painted black, and tile_8 is painted white. The goal is to reach a state where the following facts hold: Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_8 is painted in white color, Tile tile_5 is painted in black color, and Tile tile_4 is painted in white color.", "question": "Is the following fact a landmark (must hold at some point along any plan) for the current state? tile_5 is clear", "answer": "yes"}
