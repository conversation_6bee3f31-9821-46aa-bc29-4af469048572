{"id": -816072387938004158, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_12 is to the right of tile_11, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_9 is down from tile_12, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_7 is down from tile_10, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_8 is down from tile_11 Currently, robot robot1 is at tile_2 and holding color black and robot robot2 is at tile_1 and holding color white; tile_10, tile_4, tile_7, and tile_3 are clear; tile_11 is painted black, tile_6 is painted white, tile_9 is painted black, tile_8 is painted white, tile_5 is painted black, and tile_12 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"paint tile tile_4 above tile tile_1 with color white using robot robot2\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear tile_4)"], "pos": ["(painted tile_4 white)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_10) (clear tile_3) (clear tile_4) (clear tile_7) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_11 black) (painted tile_12 white) (painted tile_5 black) (painted tile_6 white) (painted tile_8 white) (painted tile_9 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_2) (robot-at robot2 tile_1) (robot-has robot1 black) (robot-has robot2 white) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": -4655781666569632693, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_20 is to the right of tile_19, tile_6 is to the right of tile_5, tile_12 is to the right of tile_11, tile_24 is to the right of tile_23, tile_10 is to the right of tile_9, tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_16 is to the right of tile_15, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, tile_15 is to the right of tile_14, tile_14 is to the right of tile_13, tile_3 is to the right of tile_2, tile_4 is to the right of tile_3, tile_18 is to the right of tile_17, tile_22 is to the right of tile_21, tile_21 is to the right of tile_20, tile_17 is to the right of tile_16, and tile_23 is to the right of tile_22. Further, tile_10 is down from tile_16, tile_7 is down from tile_13, tile_1 is down from tile_7, tile_5 is down from tile_11, tile_8 is down from tile_14, tile_6 is down from tile_12, tile_18 is down from tile_24, tile_16 is down from tile_22, tile_11 is down from tile_17, tile_2 is down from tile_8, tile_15 is down from tile_21, tile_17 is down from tile_23, tile_4 is down from tile_10, tile_3 is down from tile_9, tile_14 is down from tile_20, tile_13 is down from tile_19, tile_12 is down from tile_18, and tile_9 is down from tile_15 Currently, robot robot1 is at tile_18 and holding color black and robot robot2 is at tile_5 and holding color white; tile_4, tile_3, tile_1, tile_2, tile_12, and tile_6 are clear; tile_15 is painted black, tile_11 is painted white, tile_9 is painted white, tile_16 is painted white, tile_13 is painted black, tile_7 is painted white, tile_10 is painted black, tile_8 is painted black, tile_21 is painted white, tile_23 is painted white, tile_24 is painted black, tile_20 is painted black, tile_14 is painted white, tile_19 is painted white, tile_22 is painted black, and tile_17 is painted black. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"paint the tile tile_12 down from tile tile_18 with color black using the robot robot1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear tile_12)"], "pos": ["(painted tile_12 black)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-6-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_21 tile_22 tile_23 tile_24 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_12) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_6) (down tile_1 tile_7) (down tile_10 tile_16) (down tile_11 tile_17) (down tile_12 tile_18) (down tile_13 tile_19) (down tile_14 tile_20) (down tile_15 tile_21) (down tile_16 tile_22) (down tile_17 tile_23) (down tile_18 tile_24) (down tile_2 tile_8) (down tile_3 tile_9) (down tile_4 tile_10) (down tile_5 tile_11) (down tile_6 tile_12) (down tile_7 tile_13) (down tile_8 tile_14) (down tile_9 tile_15) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_15 tile_16) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_20 tile_21) (left tile_21 tile_22) (left tile_22 tile_23) (left tile_23 tile_24) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 black) (painted tile_11 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_19 white) (painted tile_20 black) (painted tile_21 white) (painted tile_22 black) (painted tile_23 white) (painted tile_24 black) (painted tile_7 white) (painted tile_8 black) (painted tile_9 white) (right tile_10 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_16 tile_15) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_21 tile_20) (right tile_22 tile_21) (right tile_23 tile_22) (right tile_24 tile_23) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_18) (robot-at robot2 tile_5) (robot-has robot1 black) (robot-has robot2 white) (up tile_10 tile_4) (up tile_11 tile_5) (up tile_12 tile_6) (up tile_13 tile_7) (up tile_14 tile_8) (up tile_15 tile_9) (up tile_16 tile_10) (up tile_17 tile_11) (up tile_18 tile_12) (up tile_19 tile_13) (up tile_20 tile_14) (up tile_21 tile_15) (up tile_22 tile_16) (up tile_23 tile_17) (up tile_24 tile_18) (up tile_7 tile_1) (up tile_8 tile_2) (up tile_9 tile_3))\n    (:goal (and (painted tile_7 white) (painted tile_8 black) (painted tile_9 white) (painted tile_10 black) (painted tile_11 white) (painted tile_12 black) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 white) (painted tile_20 black) (painted tile_21 white) (painted tile_22 black) (painted tile_23 white) (painted tile_24 black)))\n)"}
{"id": -706282851612775925, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_20 is to the right of tile_19, tile_6 is to the right of tile_5, tile_12 is to the right of tile_11, tile_24 is to the right of tile_23, tile_10 is to the right of tile_9, tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_16 is to the right of tile_15, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, tile_15 is to the right of tile_14, tile_14 is to the right of tile_13, tile_3 is to the right of tile_2, tile_4 is to the right of tile_3, tile_18 is to the right of tile_17, tile_22 is to the right of tile_21, tile_21 is to the right of tile_20, tile_17 is to the right of tile_16, and tile_23 is to the right of tile_22. Further, tile_10 is down from tile_16, tile_7 is down from tile_13, tile_1 is down from tile_7, tile_5 is down from tile_11, tile_8 is down from tile_14, tile_6 is down from tile_12, tile_18 is down from tile_24, tile_16 is down from tile_22, tile_11 is down from tile_17, tile_2 is down from tile_8, tile_15 is down from tile_21, tile_17 is down from tile_23, tile_4 is down from tile_10, tile_3 is down from tile_9, tile_14 is down from tile_20, tile_13 is down from tile_19, tile_12 is down from tile_18, and tile_9 is down from tile_15 Currently, robot robot2 is at tile_18 and holding color black and robot robot1 is at tile_7 and holding color white; tile_24, tile_8, tile_13, tile_10, tile_16, tile_4, tile_17, tile_1, tile_2, tile_3, tile_14, tile_23, tile_12, tile_21, tile_22, tile_5, tile_6, tile_9, tile_15, and tile_11 are clear; tile_20 is painted black and tile_19 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"apply color white to tile tile_13 above tile tile_7 using robot robot1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear tile_13)"], "pos": ["(painted tile_13 white)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-6-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_21 tile_22 tile_23 tile_24 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_10) (clear tile_11) (clear tile_12) (clear tile_13) (clear tile_14) (clear tile_15) (clear tile_16) (clear tile_17) (clear tile_2) (clear tile_21) (clear tile_22) (clear tile_23) (clear tile_24) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_8) (clear tile_9) (down tile_1 tile_7) (down tile_10 tile_16) (down tile_11 tile_17) (down tile_12 tile_18) (down tile_13 tile_19) (down tile_14 tile_20) (down tile_15 tile_21) (down tile_16 tile_22) (down tile_17 tile_23) (down tile_18 tile_24) (down tile_2 tile_8) (down tile_3 tile_9) (down tile_4 tile_10) (down tile_5 tile_11) (down tile_6 tile_12) (down tile_7 tile_13) (down tile_8 tile_14) (down tile_9 tile_15) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_15 tile_16) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_20 tile_21) (left tile_21 tile_22) (left tile_22 tile_23) (left tile_23 tile_24) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_19 white) (painted tile_20 black) (right tile_10 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_16 tile_15) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_21 tile_20) (right tile_22 tile_21) (right tile_23 tile_22) (right tile_24 tile_23) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_7) (robot-at robot2 tile_18) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_4) (up tile_11 tile_5) (up tile_12 tile_6) (up tile_13 tile_7) (up tile_14 tile_8) (up tile_15 tile_9) (up tile_16 tile_10) (up tile_17 tile_11) (up tile_18 tile_12) (up tile_19 tile_13) (up tile_20 tile_14) (up tile_21 tile_15) (up tile_22 tile_16) (up tile_23 tile_17) (up tile_24 tile_18) (up tile_7 tile_1) (up tile_8 tile_2) (up tile_9 tile_3))\n    (:goal (and (painted tile_7 white) (painted tile_8 black) (painted tile_9 white) (painted tile_10 black) (painted tile_11 white) (painted tile_12 black) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 white) (painted tile_20 black) (painted tile_21 white) (painted tile_22 black) (painted tile_23 white) (painted tile_24 black)))\n)"}
{"id": -8118816104068263600, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_19 is to the right of tile_18, tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_14 is to the right of tile_13, tile_4 is to the right of tile_3, tile_20 is to the right of tile_19, tile_7 is to the right of tile_6, tile_18 is to the right of tile_17, tile_2 is to the right of tile_1, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, tile_15 is to the right of tile_14, tile_10 is to the right of tile_9, tile_17 is to the right of tile_16, tile_13 is to the right of tile_12, and tile_9 is to the right of tile_8. Further, tile_11 is down from tile_16, tile_3 is down from tile_8, tile_10 is down from tile_15, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_12 is down from tile_17, tile_14 is down from tile_19, tile_13 is down from tile_18, tile_6 is down from tile_11, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_5 is down from tile_10, tile_1 is down from tile_6, and tile_9 is down from tile_14 Currently, robot robot2 is at tile_8 and holding color black, robot robot1 is at tile_16 and holding color white, and robot robot3 is at tile_15 and holding color white; tile_7, tile_13, tile_10, tile_4, tile_17, tile_1, tile_2, tile_3, tile_20, tile_14, tile_12, tile_18, tile_5, tile_6, tile_19, tile_9, and tile_11 are clear. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"move the robot robot2 from tile tile_8 to the left tile tile_7\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear tile_7)", "(robot-at robot2 tile_8)"], "pos": ["(clear tile_8)", "(robot-at robot2 tile_7)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-3)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 robot3 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_10) (clear tile_11) (clear tile_12) (clear tile_13) (clear tile_14) (clear tile_17) (clear tile_18) (clear tile_19) (clear tile_2) (clear tile_20) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_9) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_16) (robot-at robot2 tile_8) (robot-at robot3 tile_15) (robot-has robot1 white) (robot-has robot2 black) (robot-has robot3 white) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": -1823554031258223372, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_20 is to the right of tile_19, tile_6 is to the right of tile_5, tile_12 is to the right of tile_11, tile_24 is to the right of tile_23, tile_10 is to the right of tile_9, tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_16 is to the right of tile_15, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, tile_15 is to the right of tile_14, tile_14 is to the right of tile_13, tile_3 is to the right of tile_2, tile_4 is to the right of tile_3, tile_18 is to the right of tile_17, tile_22 is to the right of tile_21, tile_21 is to the right of tile_20, tile_17 is to the right of tile_16, and tile_23 is to the right of tile_22. Further, tile_10 is down from tile_16, tile_7 is down from tile_13, tile_1 is down from tile_7, tile_5 is down from tile_11, tile_8 is down from tile_14, tile_6 is down from tile_12, tile_18 is down from tile_24, tile_16 is down from tile_22, tile_11 is down from tile_17, tile_2 is down from tile_8, tile_15 is down from tile_21, tile_17 is down from tile_23, tile_4 is down from tile_10, tile_3 is down from tile_9, tile_14 is down from tile_20, tile_13 is down from tile_19, tile_12 is down from tile_18, and tile_9 is down from tile_15 Currently, robot robot2 is at tile_8 and holding color black and robot robot1 is at tile_1 and holding color white; tile_7, tile_13, tile_10, tile_16, tile_4, tile_17, tile_2, tile_3, tile_14, tile_23, tile_12, tile_18, tile_21, tile_5, tile_6, tile_9, tile_15, and tile_11 are clear; tile_24 is painted black, tile_20 is painted black, tile_19 is painted white, and tile_22 is painted black. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"move the robot robot2 from tile tile_8 to the left tile tile_7\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear tile_7)", "(robot-at robot2 tile_8)"], "pos": ["(clear tile_8)", "(robot-at robot2 tile_7)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-6-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_21 tile_22 tile_23 tile_24 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_10) (clear tile_11) (clear tile_12) (clear tile_13) (clear tile_14) (clear tile_15) (clear tile_16) (clear tile_17) (clear tile_18) (clear tile_2) (clear tile_21) (clear tile_23) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_9) (down tile_1 tile_7) (down tile_10 tile_16) (down tile_11 tile_17) (down tile_12 tile_18) (down tile_13 tile_19) (down tile_14 tile_20) (down tile_15 tile_21) (down tile_16 tile_22) (down tile_17 tile_23) (down tile_18 tile_24) (down tile_2 tile_8) (down tile_3 tile_9) (down tile_4 tile_10) (down tile_5 tile_11) (down tile_6 tile_12) (down tile_7 tile_13) (down tile_8 tile_14) (down tile_9 tile_15) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_15 tile_16) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_20 tile_21) (left tile_21 tile_22) (left tile_22 tile_23) (left tile_23 tile_24) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_19 white) (painted tile_20 black) (painted tile_22 black) (painted tile_24 black) (right tile_10 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_16 tile_15) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_21 tile_20) (right tile_22 tile_21) (right tile_23 tile_22) (right tile_24 tile_23) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_1) (robot-at robot2 tile_8) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_4) (up tile_11 tile_5) (up tile_12 tile_6) (up tile_13 tile_7) (up tile_14 tile_8) (up tile_15 tile_9) (up tile_16 tile_10) (up tile_17 tile_11) (up tile_18 tile_12) (up tile_19 tile_13) (up tile_20 tile_14) (up tile_21 tile_15) (up tile_22 tile_16) (up tile_23 tile_17) (up tile_24 tile_18) (up tile_7 tile_1) (up tile_8 tile_2) (up tile_9 tile_3))\n    (:goal (and (painted tile_7 white) (painted tile_8 black) (painted tile_9 white) (painted tile_10 black) (painted tile_11 white) (painted tile_12 black) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 white) (painted tile_20 black) (painted tile_21 white) (painted tile_22 black) (painted tile_23 white) (painted tile_24 black)))\n)"}
{"id": -728972417539231201, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 9 tiles and 2 robots. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_6 is down from tile_9, and tile_5 is down from tile_8 Currently, robot robot1 is at tile_4 and holding color black and robot robot2 is at tile_1 and holding color white; tile_7, tile_3, tile_2, and tile_5 are clear; tile_6 is painted white, tile_9 is painted black, and tile_8 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"move the robot robot1 from tile tile_4 to tile tile_7 upwards\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear tile_7)", "(robot-at robot1 tile_4)"], "pos": ["(clear tile_4)", "(robot-at robot1 tile_7)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_2) (clear tile_3) (clear tile_5) (clear tile_7) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_6 white) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_4) (robot-at robot2 tile_1) (robot-has robot1 black) (robot-has robot2 white) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": -8698885980291687095, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_12 is to the right of tile_11, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_9 is down from tile_12, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_7 is down from tile_10, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_8 is down from tile_11 Currently, robot robot1 is at tile_3 and holding color black and robot robot2 is at tile_2 and holding color white; tile_1 and tile_5 are clear; tile_7 is painted black, tile_11 is painted black, tile_6 is painted white, tile_4 is painted white, tile_9 is painted black, tile_8 is painted white, tile_12 is painted white, and tile_10 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"move the robot robot2 from tile tile_2 to tile tile_5 going upwards\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear tile_5)", "(robot-at robot2 tile_2)"], "pos": ["(clear tile_2)", "(robot-at robot2 tile_5)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_5) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_4 white) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_3) (robot-at robot2 tile_2) (robot-has robot1 black) (robot-has robot2 white) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": -8593866517964287602, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_19 is to the right of tile_18, tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_14 is to the right of tile_13, tile_4 is to the right of tile_3, tile_20 is to the right of tile_19, tile_7 is to the right of tile_6, tile_18 is to the right of tile_17, tile_2 is to the right of tile_1, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, tile_15 is to the right of tile_14, tile_10 is to the right of tile_9, tile_17 is to the right of tile_16, tile_13 is to the right of tile_12, and tile_9 is to the right of tile_8. Further, tile_11 is down from tile_16, tile_3 is down from tile_8, tile_10 is down from tile_15, tile_8 is down from tile_13, tile_2 is down from tile_7, tile_4 is down from tile_9, tile_12 is down from tile_17, tile_14 is down from tile_19, tile_13 is down from tile_18, tile_6 is down from tile_11, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_5 is down from tile_10, tile_1 is down from tile_6, and tile_9 is down from tile_14 Currently, robot robot2 is at tile_3 and holding color black and robot robot1 is at tile_9 and holding color black; tile_4, tile_1, tile_2, and tile_5 are clear; tile_11 is painted black, tile_15 is painted black, tile_16 is painted white, tile_12 is painted white, tile_13 is painted black, tile_19 is painted black, tile_10 is painted white, tile_7 is painted black, tile_14 is painted white, tile_20 is painted white, tile_8 is painted white, tile_18 is painted white, tile_6 is painted white, and tile_17 is painted black. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"move robot robot1 down from tile tile_9 to tile tile_4\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(robot-at robot1 tile_9)", "(clear tile_4)"], "pos": ["(clear tile_9)", "(robot-at robot1 tile_4)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-5-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (clear tile_4) (clear tile_5) (down tile_1 tile_6) (down tile_10 tile_15) (down tile_11 tile_16) (down tile_12 tile_17) (down tile_13 tile_18) (down tile_14 tile_19) (down tile_15 tile_20) (down tile_2 tile_7) (down tile_3 tile_8) (down tile_4 tile_9) (down tile_5 tile_10) (down tile_6 tile_11) (down tile_7 tile_12) (down tile_8 tile_13) (down tile_9 tile_14) (left tile_1 tile_2) (left tile_11 tile_12) (left tile_12 tile_13) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_18 tile_19) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_6 tile_7) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (right tile_10 tile_9) (right tile_12 tile_11) (right tile_13 tile_12) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_19 tile_18) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_7 tile_6) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_9) (robot-at robot2 tile_3) (robot-has robot1 black) (robot-has robot2 black) (up tile_10 tile_5) (up tile_11 tile_6) (up tile_12 tile_7) (up tile_13 tile_8) (up tile_14 tile_9) (up tile_15 tile_10) (up tile_16 tile_11) (up tile_17 tile_12) (up tile_18 tile_13) (up tile_19 tile_14) (up tile_20 tile_15) (up tile_6 tile_1) (up tile_7 tile_2) (up tile_8 tile_3) (up tile_9 tile_4))\n    (:goal (and (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 black) (painted tile_20 white)))\n)"}
{"id": 1644382363967074726, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_20 is to the right of tile_19, tile_6 is to the right of tile_5, tile_12 is to the right of tile_11, tile_24 is to the right of tile_23, tile_10 is to the right of tile_9, tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_16 is to the right of tile_15, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, tile_15 is to the right of tile_14, tile_14 is to the right of tile_13, tile_3 is to the right of tile_2, tile_4 is to the right of tile_3, tile_18 is to the right of tile_17, tile_22 is to the right of tile_21, tile_21 is to the right of tile_20, tile_17 is to the right of tile_16, and tile_23 is to the right of tile_22. Further, tile_10 is down from tile_16, tile_7 is down from tile_13, tile_1 is down from tile_7, tile_5 is down from tile_11, tile_8 is down from tile_14, tile_6 is down from tile_12, tile_18 is down from tile_24, tile_16 is down from tile_22, tile_11 is down from tile_17, tile_2 is down from tile_8, tile_15 is down from tile_21, tile_17 is down from tile_23, tile_4 is down from tile_10, tile_3 is down from tile_9, tile_14 is down from tile_20, tile_13 is down from tile_19, tile_12 is down from tile_18, and tile_9 is down from tile_15 Currently, robot robot1 is at tile_4 and holding color black and robot robot2 is at tile_15 and holding color white; tile_8, tile_7, tile_13, tile_16, tile_10, tile_1, tile_2, tile_3, tile_21, tile_5, tile_6, and tile_9 are clear; tile_11 is painted white, tile_23 is painted white, tile_24 is painted black, tile_20 is painted black, tile_14 is painted white, tile_19 is painted white, tile_12 is painted black, tile_18 is painted white, tile_22 is painted black, and tile_17 is painted black. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"move the robot robot2 from the tile tile_15 to the tile tile_9 going downwards\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear tile_9)", "(robot-at robot2 tile_15)"], "pos": ["(clear tile_15)", "(robot-at robot2 tile_9)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-6-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_13 tile_14 tile_15 tile_16 tile_17 tile_18 tile_19 tile_2 tile_20 tile_21 tile_22 tile_23 tile_24 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_10) (clear tile_13) (clear tile_16) (clear tile_2) (clear tile_21) (clear tile_3) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_8) (clear tile_9) (down tile_1 tile_7) (down tile_10 tile_16) (down tile_11 tile_17) (down tile_12 tile_18) (down tile_13 tile_19) (down tile_14 tile_20) (down tile_15 tile_21) (down tile_16 tile_22) (down tile_17 tile_23) (down tile_18 tile_24) (down tile_2 tile_8) (down tile_3 tile_9) (down tile_4 tile_10) (down tile_5 tile_11) (down tile_6 tile_12) (down tile_7 tile_13) (down tile_8 tile_14) (down tile_9 tile_15) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_13 tile_14) (left tile_14 tile_15) (left tile_15 tile_16) (left tile_16 tile_17) (left tile_17 tile_18) (left tile_19 tile_20) (left tile_2 tile_3) (left tile_20 tile_21) (left tile_21 tile_22) (left tile_22 tile_23) (left tile_23 tile_24) (left tile_3 tile_4) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (left tile_9 tile_10) (painted tile_11 white) (painted tile_12 black) (painted tile_14 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 white) (painted tile_20 black) (painted tile_22 black) (painted tile_23 white) (painted tile_24 black) (right tile_10 tile_9) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_14 tile_13) (right tile_15 tile_14) (right tile_16 tile_15) (right tile_17 tile_16) (right tile_18 tile_17) (right tile_2 tile_1) (right tile_20 tile_19) (right tile_21 tile_20) (right tile_22 tile_21) (right tile_23 tile_22) (right tile_24 tile_23) (right tile_3 tile_2) (right tile_4 tile_3) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_4) (robot-at robot2 tile_15) (robot-has robot1 black) (robot-has robot2 white) (up tile_10 tile_4) (up tile_11 tile_5) (up tile_12 tile_6) (up tile_13 tile_7) (up tile_14 tile_8) (up tile_15 tile_9) (up tile_16 tile_10) (up tile_17 tile_11) (up tile_18 tile_12) (up tile_19 tile_13) (up tile_20 tile_14) (up tile_21 tile_15) (up tile_22 tile_16) (up tile_23 tile_17) (up tile_24 tile_18) (up tile_7 tile_1) (up tile_8 tile_2) (up tile_9 tile_3))\n    (:goal (and (painted tile_7 white) (painted tile_8 black) (painted tile_9 white) (painted tile_10 black) (painted tile_11 white) (painted tile_12 black) (painted tile_13 black) (painted tile_14 white) (painted tile_15 black) (painted tile_16 white) (painted tile_17 black) (painted tile_18 white) (painted tile_19 white) (painted tile_20 black) (painted tile_21 white) (painted tile_22 black) (painted tile_23 white) (painted tile_24 black)))\n)"}
{"id": 6748119451395861994, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 9 tiles and 2 robots. The tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_6 is down from tile_9, and tile_5 is down from tile_8 Currently, robot robot2 is at tile_6 and holding color black and robot robot1 is at tile_1 and holding color white; tile_3, tile_2, tile_9, and tile_5 are clear; tile_7 is painted black, tile_4 is painted white, and tile_8 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"move the robot robot2 from tile tile_6 to tile tile_9 upwards\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(robot-at robot2 tile_6)", "(clear tile_9)"], "pos": ["(clear tile_6)", "(robot-at robot2 tile_9)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_2) (clear tile_3) (clear tile_5) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_4 white) (painted tile_7 black) (painted tile_8 white) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_1) (robot-at robot2 tile_6) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": -5560672262998685272, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 12 tiles and 2 robots. \nThe tiles locations are: tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_12 is to the right of tile_11, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_9 is down from tile_12, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_7 is down from tile_10, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_8 is down from tile_11 \nCurrently, robot robot2 is at tile_1 and holding color white and robot robot1 is at tile_3 and holding color white; tile_6 and tile_2 are clear; tile_7 is painted black, tile_11 is painted black, tile_4 is painted white, tile_9 is painted black, tile_8 is painted white, tile_5 is painted black, tile_12 is painted white, and tile_10 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"move robot robot1 from tile tile_3 to the left tile tile tile_2\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(robot-at robot1 tile_3)", "(clear tile_2)"], "pos": ["(robot-at robot1 tile_2)", "(clear tile_3)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_2) (clear tile_6) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_4 white) (painted tile_5 black) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_3) (robot-at robot2 tile_1) (robot-has robot1 white) (robot-has robot2 white) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 4184611347038712342, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 9 tiles and 2 robots. \nThe tiles locations are: tile_5 is to the right of tile_4, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_6 is down from tile_9, and tile_5 is down from tile_8 \nCurrently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_4 and holding color white; tile_6, tile_7, tile_3, tile_1, and tile_5 are clear; tile_9 is painted black and tile_8 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"move the robot robot2 from the tile tile_2 to the tile on its left tile_1\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(robot-at robot2 tile_2)", "(clear tile_1)"], "pos": ["(robot-at robot2 tile_1)", "(clear tile_2)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_5) (clear tile_6) (clear tile_7) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_4) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 6954158343664882229, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 12 tiles and 2 robots. \nThe tiles locations are: tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_12 is to the right of tile_11, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_9 is down from tile_12, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_7 is down from tile_10, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_8 is down from tile_11 \nCurrently, robot robot1 is at tile_6 and holding color black and robot robot2 is at tile_2 and holding color white; tile_8, tile_3, tile_1, tile_9, and tile_5 are clear; tile_7 is painted black, tile_11 is painted black, tile_4 is painted white, tile_12 is painted white, and tile_10 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"navigate robot robot2 from tile tile_2 to tile tile_1 to its left\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(robot-at robot2 tile_2)", "(clear tile_1)"], "pos": ["(robot-at robot2 tile_1)", "(clear tile_2)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_5) (clear tile_8) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_4 white) (painted tile_7 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_6) (robot-at robot2 tile_2) (robot-has robot1 black) (robot-has robot2 white) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 3592352739305718206, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 12 tiles and 2 robots. \nThe tiles locations are: tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_12 is to the right of tile_11, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_9 is down from tile_12, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_7 is down from tile_10, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_8 is down from tile_11 \nCurrently, robot robot2 is at tile_5 and holding color black and robot robot1 is at tile_7 and holding color white; tile_8, tile_4, tile_6, tile_3, tile_1, tile_2, tile_9, and tile_11 are clear; tile_12 is painted white and tile_10 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"move the robot robot2 up from tile tile_5 to tile tile_8\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(clear tile_8)", "(robot-at robot2 tile_5)"], "pos": ["(clear tile_5)", "(robot-at robot2 tile_8)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_11) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_6) (clear tile_8) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_12 white) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_7) (robot-at robot2 tile_5) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 8462041551824888234, "group": "progression_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 12 tiles and 2 robots. \nThe tiles locations are: tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_12 is to the right of tile_11, and tile_8 is to the right of tile_7. Further, tile_1 is down from tile_4, tile_9 is down from tile_12, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_3 is down from tile_6, tile_7 is down from tile_10, tile_5 is down from tile_8, tile_6 is down from tile_9, and tile_8 is down from tile_11 \nCurrently, robot robot2 is at tile_4 and holding color white and robot robot1 is at tile_8 and holding color white; tile_6, tile_7, tile_3, tile_1, tile_2, tile_9, and tile_5 are clear; tile_11 is painted black, tile_12 is painted white, and tile_10 is painted white. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Break down the outcomes of performing the action \"move the robot robot1 from tile tile_8 to the right tile tile_9\" into two lists, positive effects and negative effects. Positive effects are the propositions that are false in the current state but will become true after performing the action. Negative effects are the propositions that are true in the current state and will become false after performing the action.", "answer": {"neg": ["(robot-at robot1 tile_8)", "(clear tile_9)"], "pos": ["(robot-at robot1 tile_9)", "(clear tile_8)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_2) (clear tile_3) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_8) (robot-at robot2 tile_4) (robot-has robot1 white) (robot-has robot2 white) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
