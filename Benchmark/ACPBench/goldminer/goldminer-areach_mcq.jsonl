{"id": 6765164008681333749, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and is holding gold. The following locations have hard rock: f1-2f, f1-3f, f3-3f, and f3-1f. The following locations have soft rock: f2-2f, f2-1f, f2-3f, and f3-2f. The gold is at f0-3f location. The laser is at f0-0f location.", "question": "Which of the following actions can eventually be applied? A. move from location f1-0f to location f0-0f. B. pick up gold at location f2-1f. C. detonate bomb at loc f3-0f connected to loc f3-1f. D. detonate bomb at loc f2-1f connected to loc f3-1f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move from location f1-0f to location f0-0f", "pick up gold at location f2-1f", "detonate bomb at loc f3-0f connected to loc f3-1f", "detonate bomb at loc f2-1f connected to loc f3-1f"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -6318589795635451473, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-4f and is holding gold. The following locations have hard rock: f1-3f and f1-4f. The following locations have soft rock: f2-2f, f1-2f, f2-4f, f2-1f, and f2-3f. The gold is at f0-4f location. The laser is at f0-2f location.", "question": "Which of the following actions can eventually be applied? A. pick up gold at location f2-3f. B. move from location f0-1f to location f1-1f. C. pick up gold at location f2-2f. D. pick up gold at location f1-1f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up gold at location f2-3f", "move from location f0-1f to location f1-1f", "pick up gold at location f2-2f", "pick up gold at location f1-1f"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -8279183082541536469, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and is holding a laser. The following locations have hard rock: f1-3f, f1-1f, and f1-4f. The following locations have soft rock: f2-4f, f2-1f, f2-3f, and f2-2f. The gold is at f0-4f location.", "question": "Which of the following actions can eventually be applied? A. detonate the bomb at location f0-2f connected to location f1-1f. B. pick up gold at loc f0-1f. C. detonate the bomb at location f0-2f connected to location f1-3f. D. move to location f1-0f from location f1-1f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["detonate the bomb at location f0-2f connected to location f1-1f", "pick up gold at loc f0-1f", "detonate the bomb at location f0-2f connected to location f1-3f", "move to location f1-0f from location f1-1f"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -3220725258586485620, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-1f and is holding a laser. The following locations have hard rock: f0-2f. The following locations have soft rock: f1-2f, f2-1f, f2-3f, f2-2f, f0-3f, and f1-3f. The gold is at f0-3f location.", "question": "Which of the following actions can eventually be applied? A. pick up gold at location f0-0f. B. trigger the explosion of the bomb at location f1-1f, which is connected to location f2-0f. C. pick up the bomb at location f0-0f. D. trigger the explosion of the bomb at location f1-3f, which is connected to location f2-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up gold at location f0-0f", "trigger the explosion of the bomb at location f1-1f, which is connected to location f2-0f", "pick up the bomb at location f0-0f", "trigger the explosion of the bomb at location f1-3f, which is connected to location f2-0f"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -7505509187394772817, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f1-3f and f1-4f. The following locations have soft rock: f2-2f, f1-2f, f2-4f, f0-4f, f2-1f, and f2-3f. The gold is at f0-4f location. The laser is at f1-0f location.", "question": "Which of the following actions can eventually be applied? A. pick up the gold from location f1-2f. B. pick up the gold from location f2-4f. C. travel from location f1-0f to location f2-0f. D. detonate the bomb at location f1-2f connected to location f0-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up the gold from location f1-2f", "pick up the gold from location f2-4f", "travel from location f1-0f to location f2-0f", "detonate the bomb at location f1-2f connected to location f0-0f"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -280935932686001427, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and its arm is empty. The following locations have hard rock: f0-3f, f2-1f, and f2-2f. The following locations have soft rock: f2-3f, f0-2f, and f1-3f. The gold is at f1-3f location. The laser is at f0-0f location.", "question": "Which of the following actions can eventually be applied? A. retrieve gold from location f0-2f. B. retrieve gold from location f2-3f. C. detonate bomb at loc f1-1f connected to loc f2-0f. D. move from loc f0-1f to loc f0-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["retrieve gold from location f0-2f", "retrieve gold from location f2-3f", "detonate bomb at loc f1-1f connected to loc f2-0f", "move from loc f0-1f to loc f0-0f"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -106146135377521312, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-2f, f1-3f, f3-3f, f1-1f, and f3-1f. The following locations have soft rock: f2-2f, f0-3f, f2-3f, and f3-2f. The gold is at f0-3f location. The laser is at f0-0f location.", "question": "Which of the following actions can eventually be applied? A. pick up gold at loc f3-3f. B. pick up the laser at loc f0-0f. C. detonate bomb at loc f0-2f connected to loc f3-0f. D. detonate bomb at loc f0-0f connected to loc f1-3f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up gold at loc f3-3f", "pick up the laser at loc f0-0f", "detonate bomb at loc f0-2f connected to loc f3-0f", "detonate bomb at loc f0-0f connected to loc f1-3f"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 3694754428459184945, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and its arm is empty. The following locations have hard rock: f0-2f. The following locations have soft rock: f1-2f, f2-1f, f2-3f, f2-2f, f0-3f, and f1-3f. The gold is at f0-3f location. The laser is at f1-1f location.", "question": "Which of the following actions can eventually be applied? A. detonate bomb at loc f2-1f connected to loc f2-0f. B. pick up the gold from location f1-1f. C. pick up the gold from location f2-1f. D. move from location f0-0f to location f1-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["detonate bomb at loc f2-1f connected to loc f2-0f", "pick up the gold from location f1-1f", "pick up the gold from location f2-1f", "move from location f0-0f to location f1-0f"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 7926207302220157418, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and is holding gold. The following locations have hard rock: f0-3f, f2-1f, and f2-2f. The following locations have soft rock: f2-3f and f0-2f. The gold is at f1-3f location. The laser is at f0-0f location.", "question": "Which of the following actions can eventually be applied? A. pick up gold at location f0-3f. B. detonate bomb at loc f0-2f connected to loc f2-0f. C. move from location f1-2f to location f1-3f. D. detonate bomb at loc f1-3f connected to loc f1-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up gold at location f0-3f", "detonate bomb at loc f0-2f connected to loc f2-0f", "move from location f1-2f to location f1-3f", "detonate bomb at loc f1-3f connected to loc f1-0f"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -5506508396710483997, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and is holding a laser. The following locations have hard rock: f1-3f and f1-4f. The following locations have soft rock: f0-4f, f1-2f, f2-4f, f2-1f, f2-3f, and f2-2f. The gold is at f0-4f location.", "question": "Which of the following actions can eventually be applied? A. trigger the explosion of the bomb at location f1-4f, which is connected to location f0-0f. B. pick up the gold from location f1-3f. C. move from loc f1-1f to loc f0-1f. D. pick up the gold from location f0-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["trigger the explosion of the bomb at location f1-4f, which is connected to location f0-0f", "pick up the gold from location f1-3f", "move from loc f1-1f to loc f0-1f", "pick up the gold from location f0-0f"]}, "query": "Which action is reachable from this state?", "answer": "C"}
