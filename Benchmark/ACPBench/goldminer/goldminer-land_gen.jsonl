{"id": -6429902732632761668, "group": "landmarks_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-2f and is holding a bomb. The following locations have hard rock: f0-3f, f2-2f, and f2-1f. The following locations have soft rock: f1-1f, f2-3f, and f1-3f. The gold is at f1-3f location. The laser is at f0-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available propositions are: (robot-at ?x) - The robot is at position ?x, (laser-at ?x) - The laser is at ?x location, (soft-rock-at ?x) - Soft rock at ?x, (hard-rock-at ?x) - Hard rock at ?x, (gold-at ?x) - The gold is at ?x location, (arm-empty) - , (holds-bomb) - , (holds-laser) - , (holds-gold) - , and (clear ?x) - Location(s) ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(laser-at f0-3f)", "(robot-at f2-3f)", "(soft-rock-at f0-1f)", "(holds-laser)", "(robot-at f0-3f)", "(laser-at f1-1f)", "(laser-at f2-1f)", "(laser-at f2-3f)", "(robot-at f0-2f)", "(laser-at f1-0f)", "(clear f2-1f)", "(laser-at f0-2f)", "(clear f2-2f)", "(laser-at f1-2f)", "(robot-at f0-0f)", "(robot-at f1-1f)", "(soft-rock-at f0-2f)", "(laser-at f0-0f)", "(laser-at f2-0f)", "(robot-at f1-0f)", "(soft-rock-at f1-2f)", "(laser-at f2-2f)", "(clear f2-3f)", "(robot-at f2-2f)", "(robot-at f2-1f)", "(clear f0-3f)", "(clear f1-1f)", "(robot-at f2-0f)", "(laser-at f1-3f)", "(robot-at f0-1f)"], "yes": ["(arm-empty)", "(robot-at f1-3f)", "(clear f1-3f)"]}, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols4)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f1-0f f1-1f f1-2f f1-3f f2-0f f2-1f f2-2f f2-3f - loc)\n    (:init (bomb-at f0-0f) (clear f0-0f) (clear f0-1f) (clear f0-2f) (clear f1-0f) (clear f1-2f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f1-3f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f2-3f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (gold-at f1-3f) (hard-rock-at f0-3f) (hard-rock-at f2-1f) (hard-rock-at f2-2f) (holds-bomb) (laser-at f0-1f) (robot-at f1-2f) (soft-rock-at f1-1f) (soft-rock-at f1-3f) (soft-rock-at f2-3f))\n    (:goal (holds-gold))\n)"}
{"id": 2424292176153180923, "group": "landmarks_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and is holding a bomb. The following locations have hard rock: f0-2f and f1-1f. The following locations have soft rock: f2-1f, f2-2f, f2-3f, f0-3f, f1-2f, and f1-3f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available propositions are: (robot-at ?x) - The robot is at position ?x, (laser-at ?x) - The laser is at ?x location, (soft-rock-at ?x) - Soft rock at ?x, (hard-rock-at ?x) - Hard rock at ?x, (gold-at ?x) - The gold is at ?x location, (arm-empty) - , (holds-bomb) - , (holds-laser) - , (holds-gold) - , and (clear ?x) - Location(s) ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(laser-at f0-3f)", "(robot-at f1-2f)", "(robot-at f2-3f)", "(soft-rock-at f0-1f)", "(holds-laser)", "(laser-at f1-1f)", "(laser-at f2-1f)", "(laser-at f0-1f)", "(laser-at f2-3f)", "(robot-at f0-2f)", "(laser-at f1-0f)", "(clear f2-1f)", "(laser-at f0-2f)", "(clear f2-2f)", "(laser-at f1-2f)", "(clear f0-2f)", "(robot-at f1-1f)", "(laser-at f2-0f)", "(clear f1-2f)", "(laser-at f2-2f)", "(clear f2-3f)", "(robot-at f2-2f)", "(robot-at f2-1f)", "(clear f1-1f)", "(robot-at f1-3f)", "(laser-at f1-3f)", "(clear f1-3f)", "(robot-at f0-1f)"], "yes": ["(arm-empty)", "(robot-at f0-3f)", "(robot-at f2-0f)", "(clear f0-3f)", "(robot-at f0-0f)"]}, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols4)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f1-0f f1-1f f1-2f f1-3f f2-0f f2-1f f2-2f f2-3f - loc)\n    (:init (bomb-at f0-0f) (clear f0-0f) (clear f0-1f) (clear f1-0f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f1-3f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f2-3f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (gold-at f0-3f) (hard-rock-at f0-2f) (hard-rock-at f1-1f) (holds-bomb) (laser-at f0-0f) (robot-at f1-0f) (soft-rock-at f0-3f) (soft-rock-at f1-2f) (soft-rock-at f1-3f) (soft-rock-at f2-1f) (soft-rock-at f2-2f) (soft-rock-at f2-3f))\n    (:goal (holds-gold))\n)"}
{"id": -3499586746134681743, "group": "landmarks_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and is holding a laser. The following locations have hard rock: f1-4f, f1-1f, and f1-3f. The following locations have soft rock: f0-4f, f2-1f, f2-2f, f2-3f, and f2-4f. The gold is at f0-4f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available propositions are: (robot-at ?x) - The robot is at position ?x, (laser-at ?x) - The laser is at ?x location, (soft-rock-at ?x) - Soft rock at ?x, (hard-rock-at ?x) - Hard rock at ?x, (gold-at ?x) - The gold is at ?x location, (arm-empty) - , (holds-bomb) - , (holds-laser) - , (holds-gold) - , and (clear ?x) - Location(s) ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at f1-4f)", "(laser-at f0-3f)", "(robot-at f1-2f)", "(clear f2-4f)", "(robot-at f2-3f)", "(soft-rock-at f0-1f)", "(robot-at f0-3f)", "(laser-at f1-1f)", "(laser-at f2-1f)", "(laser-at f0-1f)", "(laser-at f2-3f)", "(laser-at f1-0f)", "(laser-at f0-2f)", "(clear f2-2f)", "(soft-rock-at f0-3f)", "(laser-at f1-2f)", "(laser-at f2-4f)", "(robot-at f1-1f)", "(soft-rock-at f0-2f)", "(laser-at f0-0f)", "(laser-at f1-4f)", "(clear f1-4f)", "(holds-bomb)", "(laser-at f2-0f)", "(robot-at f1-0f)", "(soft-rock-at f1-2f)", "(laser-at f2-2f)", "(laser-at f0-4f)", "(robot-at f0-1f)", "(clear f2-3f)", "(robot-at f2-2f)", "(robot-at f2-1f)", "(clear f1-1f)", "(robot-at f2-0f)", "(robot-at f1-3f)", "(laser-at f1-3f)", "(robot-at f2-4f)", "(clear f1-3f)", "(clear f2-1f)"], "yes": ["(arm-empty)", "(robot-at f0-4f)", "(clear f0-4f)", "(robot-at f0-0f)"]}, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols5)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f - loc)\n    (:init (bomb-at f0-0f) (clear f0-0f) (clear f0-1f) (clear f0-2f) (clear f0-3f) (clear f1-0f) (clear f1-2f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f0-4f) (connected f0-3f f1-3f) (connected f0-4f f0-3f) (connected f0-4f f1-4f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f1-4f) (connected f1-3f f2-3f) (connected f1-4f f0-4f) (connected f1-4f f1-3f) (connected f1-4f f2-4f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (connected f2-3f f2-4f) (connected f2-4f f1-4f) (connected f2-4f f2-3f) (gold-at f0-4f) (hard-rock-at f1-1f) (hard-rock-at f1-3f) (hard-rock-at f1-4f) (holds-laser) (robot-at f0-2f) (soft-rock-at f0-4f) (soft-rock-at f2-1f) (soft-rock-at f2-2f) (soft-rock-at f2-3f) (soft-rock-at f2-4f))\n    (:goal (holds-gold))\n)"}
{"id": 6811546892238242990, "group": "landmarks_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and is holding a bomb. The following locations have hard rock: f1-4f, f1-3f, and f1-1f. The following locations have soft rock: f2-4f, f2-3f, f0-4f, f2-1f, and f2-2f. The gold is at f0-4f location. The laser is at f0-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available propositions are: (robot-at ?x) - The robot is at position ?x, (laser-at ?x) - The laser is at ?x location, (soft-rock-at ?x) - Soft rock at ?x, (hard-rock-at ?x) - Hard rock at ?x, (gold-at ?x) - The gold is at ?x location, (arm-empty) - , (holds-bomb) - , (holds-laser) - , (holds-gold) - , and (clear ?x) - Location(s) ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at f1-4f)", "(laser-at f0-3f)", "(robot-at f1-2f)", "(clear f2-4f)", "(robot-at f2-3f)", "(soft-rock-at f0-1f)", "(holds-laser)", "(robot-at f0-3f)", "(laser-at f1-1f)", "(laser-at f2-1f)", "(laser-at f2-3f)", "(robot-at f0-2f)", "(laser-at f1-0f)", "(laser-at f0-2f)", "(clear f2-2f)", "(soft-rock-at f0-3f)", "(laser-at f1-2f)", "(robot-at f0-0f)", "(laser-at f2-4f)", "(robot-at f1-1f)", "(soft-rock-at f0-2f)", "(laser-at f0-0f)", "(laser-at f1-4f)", "(clear f1-4f)", "(laser-at f2-0f)", "(robot-at f1-0f)", "(soft-rock-at f1-2f)", "(laser-at f2-2f)", "(laser-at f0-4f)", "(clear f2-3f)", "(robot-at f2-2f)", "(robot-at f2-1f)", "(clear f1-1f)", "(robot-at f2-0f)", "(robot-at f1-3f)", "(laser-at f1-3f)", "(robot-at f2-4f)", "(clear f1-3f)", "(clear f2-1f)"], "yes": ["(arm-empty)", "(robot-at f0-4f)", "(clear f0-4f)"]}, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols5)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f - loc)\n    (:init (bomb-at f0-0f) (clear f0-0f) (clear f0-1f) (clear f0-2f) (clear f0-3f) (clear f1-0f) (clear f1-2f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f0-4f) (connected f0-3f f1-3f) (connected f0-4f f0-3f) (connected f0-4f f1-4f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f1-4f) (connected f1-3f f2-3f) (connected f1-4f f0-4f) (connected f1-4f f1-3f) (connected f1-4f f2-4f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (connected f2-3f f2-4f) (connected f2-4f f1-4f) (connected f2-4f f2-3f) (gold-at f0-4f) (hard-rock-at f1-1f) (hard-rock-at f1-3f) (hard-rock-at f1-4f) (holds-bomb) (laser-at f0-1f) (robot-at f0-1f) (soft-rock-at f0-4f) (soft-rock-at f2-1f) (soft-rock-at f2-2f) (soft-rock-at f2-3f) (soft-rock-at f2-4f))\n    (:goal (holds-gold))\n)"}
{"id": -631460933696028701, "group": "landmarks_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and is holding a bomb. The following locations have hard rock: f3-1f, f3-3f, f1-3f, f1-1f, and f1-2f. The following locations have soft rock: f2-3f, f0-3f, f3-2f, f0-2f, f2-1f, and f2-2f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available propositions are: (robot-at ?x) - The robot is at position ?x, (laser-at ?x) - The laser is at ?x location, (soft-rock-at ?x) - Soft rock at ?x, (hard-rock-at ?x) - Hard rock at ?x, (gold-at ?x) - The gold is at ?x location, (arm-empty) - , (holds-bomb) - , (holds-laser) - , (holds-gold) - , and (clear ?x) - Location(s) ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(laser-at f0-3f)", "(robot-at f1-2f)", "(laser-at f3-2f)", "(robot-at f2-3f)", "(soft-rock-at f0-1f)", "(holds-laser)", "(laser-at f1-1f)", "(laser-at f2-1f)", "(laser-at f0-1f)", "(clear f3-1f)", "(laser-at f2-3f)", "(robot-at f0-2f)", "(laser-at f1-0f)", "(laser-at f3-0f)", "(laser-at f0-2f)", "(clear f2-2f)", "(laser-at f1-2f)", "(clear f0-2f)", "(robot-at f1-1f)", "(clear f3-2f)", "(robot-at f3-3f)", "(robot-at f3-2f)", "(laser-at f2-0f)", "(clear f1-2f)", "(robot-at f1-0f)", "(clear f3-3f)", "(laser-at f2-2f)", "(robot-at f0-1f)", "(clear f2-3f)", "(laser-at f3-3f)", "(robot-at f2-2f)", "(robot-at f2-1f)", "(robot-at f3-1f)", "(clear f1-1f)", "(robot-at f3-0f)", "(robot-at f2-0f)", "(robot-at f1-3f)", "(laser-at f1-3f)", "(laser-at f3-1f)", "(clear f1-3f)", "(clear f2-1f)"], "yes": ["(arm-empty)", "(robot-at f0-3f)", "(clear f0-3f)"]}, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows4-cols4)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f1-0f f1-1f f1-2f f1-3f f2-0f f2-1f f2-2f f2-3f f3-0f f3-1f f3-2f f3-3f - loc)\n    (:init (bomb-at f0-0f) (clear f0-0f) (clear f0-1f) (clear f1-0f) (clear f2-0f) (clear f3-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f1-3f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f2-3f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-0f f3-0f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-1f f3-1f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-2f f3-2f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (connected f2-3f f3-3f) (connected f3-0f f2-0f) (connected f3-0f f3-1f) (connected f3-1f f2-1f) (connected f3-1f f3-0f) (connected f3-1f f3-2f) (connected f3-2f f2-2f) (connected f3-2f f3-1f) (connected f3-2f f3-3f) (connected f3-3f f2-3f) (connected f3-3f f3-2f) (gold-at f0-3f) (hard-rock-at f1-1f) (hard-rock-at f1-2f) (hard-rock-at f1-3f) (hard-rock-at f3-1f) (hard-rock-at f3-3f) (holds-bomb) (laser-at f0-0f) (robot-at f0-0f) (soft-rock-at f0-2f) (soft-rock-at f0-3f) (soft-rock-at f2-1f) (soft-rock-at f2-2f) (soft-rock-at f2-3f) (soft-rock-at f3-2f))\n    (:goal (holds-gold))\n)"}
{"id": -2463731086226704292, "group": "landmarks_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have soft rock: f2-1f, f2-2f, f2-3f, f1-2f, and f1-3f. The gold is at f0-3f location. The laser is at f0-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available propositions are: (robot-at ?x) - The robot is at position ?x, (laser-at ?x) - The laser is at ?x location, (soft-rock-at ?x) - Soft rock at ?x, (hard-rock-at ?x) - Hard rock at ?x, (gold-at ?x) - The gold is at ?x location, (arm-empty) - , (holds-bomb) - , (holds-laser) - , (holds-gold) - , and (clear ?x) - Location(s) ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(laser-at f0-3f)", "(robot-at f1-2f)", "(robot-at f2-3f)", "(soft-rock-at f0-1f)", "(holds-laser)", "(laser-at f1-1f)", "(laser-at f2-1f)", "(laser-at f2-3f)", "(robot-at f0-2f)", "(laser-at f1-0f)", "(laser-at f0-2f)", "(hard-rock-at f0-2f)", "(clear f2-2f)", "(soft-rock-at f0-3f)", "(laser-at f1-2f)", "(robot-at f1-1f)", "(laser-at f0-0f)", "(holds-bomb)", "(laser-at f2-0f)", "(robot-at f1-0f)", "(clear f1-2f)", "(laser-at f2-2f)", "(robot-at f0-1f)", "(clear f2-3f)", "(robot-at f2-2f)", "(robot-at f2-1f)", "(hard-rock-at f1-1f)", "(robot-at f2-0f)", "(robot-at f1-3f)", "(laser-at f1-3f)", "(clear f1-3f)", "(clear f2-1f)"], "yes": ["(robot-at f0-3f)"]}, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols4)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f1-0f f1-1f f1-2f f1-3f f2-0f f2-1f f2-2f f2-3f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f0-1f) (clear f0-2f) (clear f0-3f) (clear f1-0f) (clear f1-1f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f1-3f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f2-3f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (gold-at f0-3f) (laser-at f0-1f) (robot-at f0-0f) (soft-rock-at f1-2f) (soft-rock-at f1-3f) (soft-rock-at f2-1f) (soft-rock-at f2-2f) (soft-rock-at f2-3f))\n    (:goal (holds-gold))\n)"}
{"id": -9149840523179523190, "group": "landmarks_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and its arm is empty. The following locations have hard rock: f3-1f, f3-3f, f1-3f, f1-1f, and f1-2f. The following locations have soft rock: f0-1f, f2-3f, f0-3f, f3-2f, f0-2f, f2-1f, and f2-2f. The gold is at f0-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available propositions are: (robot-at ?x) - The robot is at position ?x, (laser-at ?x) - The laser is at ?x location, (soft-rock-at ?x) - Soft rock at ?x, (hard-rock-at ?x) - Hard rock at ?x, (gold-at ?x) - The gold is at ?x location, (arm-empty) - , (holds-bomb) - , (holds-laser) - , (holds-gold) - , and (clear ?x) - Location(s) ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(laser-at f0-3f)", "(robot-at f1-2f)", "(laser-at f3-2f)", "(robot-at f2-3f)", "(holds-laser)", "(laser-at f1-1f)", "(laser-at f2-1f)", "(laser-at f0-1f)", "(clear f3-1f)", "(laser-at f2-3f)", "(robot-at f0-2f)", "(laser-at f1-0f)", "(clear f0-1f)", "(laser-at f3-0f)", "(laser-at f0-2f)", "(clear f2-2f)", "(laser-at f1-2f)", "(clear f0-2f)", "(robot-at f1-1f)", "(clear f3-2f)", "(robot-at f3-3f)", "(robot-at f3-2f)", "(laser-at f2-0f)", "(clear f1-2f)", "(holds-bomb)", "(clear f3-3f)", "(laser-at f2-2f)", "(robot-at f0-1f)", "(clear f2-3f)", "(laser-at f3-3f)", "(robot-at f2-2f)", "(robot-at f2-1f)", "(robot-at f3-1f)", "(clear f1-1f)", "(robot-at f3-0f)", "(robot-at f2-0f)", "(robot-at f1-3f)", "(laser-at f1-3f)", "(laser-at f3-1f)", "(clear f1-3f)", "(clear f2-1f)"], "yes": ["(robot-at f0-3f)", "(clear f0-3f)", "(robot-at f0-0f)"]}, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows4-cols4)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f1-0f f1-1f f1-2f f1-3f f2-0f f2-1f f2-2f f2-3f f3-0f f3-1f f3-2f f3-3f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f1-0f) (clear f2-0f) (clear f3-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f1-3f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f2-3f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-0f f3-0f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-1f f3-1f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-2f f3-2f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (connected f2-3f f3-3f) (connected f3-0f f2-0f) (connected f3-0f f3-1f) (connected f3-1f f2-1f) (connected f3-1f f3-0f) (connected f3-1f f3-2f) (connected f3-2f f2-2f) (connected f3-2f f3-1f) (connected f3-2f f3-3f) (connected f3-3f f2-3f) (connected f3-3f f3-2f) (gold-at f0-3f) (hard-rock-at f1-1f) (hard-rock-at f1-2f) (hard-rock-at f1-3f) (hard-rock-at f3-1f) (hard-rock-at f3-3f) (laser-at f0-0f) (robot-at f1-0f) (soft-rock-at f0-1f) (soft-rock-at f0-2f) (soft-rock-at f0-3f) (soft-rock-at f2-1f) (soft-rock-at f2-2f) (soft-rock-at f2-3f) (soft-rock-at f3-2f))\n    (:goal (holds-gold))\n)"}
{"id": -8379391448523371705, "group": "landmarks_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and its arm is empty. The following locations have hard rock: f0-3f, f2-2f, and f2-1f. The following locations have soft rock: f1-1f, f2-3f, f0-1f, f1-2f, f1-3f, and f0-2f. The gold is at f1-3f location. The laser is at f0-0f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available propositions are: (robot-at ?x) - The robot is at position ?x, (laser-at ?x) - The laser is at ?x location, (soft-rock-at ?x) - Soft rock at ?x, (hard-rock-at ?x) - Hard rock at ?x, (gold-at ?x) - The gold is at ?x location, (arm-empty) - , (holds-bomb) - , (holds-laser) - , (holds-gold) - , and (clear ?x) - Location(s) ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(laser-at f0-3f)", "(robot-at f1-2f)", "(robot-at f2-3f)", "(holds-laser)", "(robot-at f0-3f)", "(laser-at f1-1f)", "(laser-at f2-1f)", "(laser-at f0-1f)", "(laser-at f2-3f)", "(robot-at f0-2f)", "(clear f0-1f)", "(laser-at f1-0f)", "(clear f2-1f)", "(laser-at f0-2f)", "(clear f2-2f)", "(laser-at f1-2f)", "(clear f0-2f)", "(robot-at f1-1f)", "(holds-bomb)", "(laser-at f2-0f)", "(clear f1-2f)", "(laser-at f2-2f)", "(clear f2-3f)", "(robot-at f2-2f)", "(robot-at f2-1f)", "(clear f0-3f)", "(clear f1-1f)", "(robot-at f2-0f)", "(laser-at f1-3f)", "(robot-at f0-1f)"], "yes": ["(robot-at f1-3f)", "(clear f1-3f)", "(robot-at f0-0f)"]}, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols4)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f1-0f f1-1f f1-2f f1-3f f2-0f f2-1f f2-2f f2-3f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f1-0f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f1-3f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f2-3f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (gold-at f1-3f) (hard-rock-at f0-3f) (hard-rock-at f2-1f) (hard-rock-at f2-2f) (laser-at f0-0f) (robot-at f1-0f) (soft-rock-at f0-1f) (soft-rock-at f0-2f) (soft-rock-at f1-1f) (soft-rock-at f1-2f) (soft-rock-at f1-3f) (soft-rock-at f2-3f))\n    (:goal (holds-gold))\n)"}
{"id": 8203029105717848481, "group": "landmarks_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f0-3f, f2-2f, and f2-1f. The following locations have soft rock: f2-3f, f0-1f, f1-3f, and f0-2f. The gold is at f1-3f location. The laser is at f1-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available propositions are: (robot-at ?x) - The robot is at position ?x, (laser-at ?x) - The laser is at ?x location, (soft-rock-at ?x) - Soft rock at ?x, (hard-rock-at ?x) - Hard rock at ?x, (gold-at ?x) - The gold is at ?x location, (arm-empty) - , (holds-bomb) - , (holds-laser) - , (holds-gold) - , and (clear ?x) - Location(s) ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(laser-at f0-3f)", "(robot-at f1-2f)", "(robot-at f2-3f)", "(holds-laser)", "(robot-at f0-3f)", "(laser-at f2-1f)", "(laser-at f0-1f)", "(laser-at f2-3f)", "(robot-at f0-2f)", "(clear f0-1f)", "(laser-at f1-0f)", "(clear f2-1f)", "(laser-at f0-2f)", "(clear f2-2f)", "(laser-at f1-2f)", "(clear f0-2f)", "(robot-at f1-1f)", "(laser-at f0-0f)", "(soft-rock-at f1-1f)", "(holds-bomb)", "(laser-at f2-0f)", "(robot-at f1-0f)", "(soft-rock-at f1-2f)", "(laser-at f2-2f)", "(clear f2-3f)", "(robot-at f2-2f)", "(robot-at f2-1f)", "(clear f0-3f)", "(robot-at f2-0f)", "(laser-at f1-3f)", "(robot-at f0-1f)"], "yes": ["(robot-at f1-3f)", "(clear f1-3f)"]}, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols4)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f1-0f f1-1f f1-2f f1-3f f2-0f f2-1f f2-2f f2-3f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f1-0f) (clear f1-1f) (clear f1-2f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f1-3f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f2-3f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (gold-at f1-3f) (hard-rock-at f0-3f) (hard-rock-at f2-1f) (hard-rock-at f2-2f) (laser-at f1-1f) (robot-at f0-0f) (soft-rock-at f0-1f) (soft-rock-at f0-2f) (soft-rock-at f1-3f) (soft-rock-at f2-3f))\n    (:goal (holds-gold))\n)"}
{"id": -8904918151794282239, "group": "landmarks_gen", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and its arm is empty. The following locations have hard rock: f0-3f, f2-2f, and f2-1f. The following locations have soft rock: f0-2f, f2-3f, and f1-3f. The gold is at f1-3f location. The laser is at f0-1f location. The goal is to reach a state where the following facts hold: The robot is holding gold. The available propositions are: (robot-at ?x) - The robot is at position ?x, (laser-at ?x) - The laser is at ?x location, (soft-rock-at ?x) - Soft rock at ?x, (hard-rock-at ?x) - Hard rock at ?x, (gold-at ?x) - The gold is at ?x location, (arm-empty) - , (holds-bomb) - , (holds-laser) - , (holds-gold) - , and (clear ?x) - Location(s) ?x is clear.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(laser-at f0-3f)", "(robot-at f1-2f)", "(robot-at f2-3f)", "(soft-rock-at f0-1f)", "(holds-laser)", "(robot-at f0-3f)", "(laser-at f1-1f)", "(laser-at f2-1f)", "(laser-at f2-3f)", "(robot-at f0-2f)", "(laser-at f1-0f)", "(laser-at f0-2f)", "(clear f2-2f)", "(laser-at f1-2f)", "(clear f0-2f)", "(robot-at f1-1f)", "(laser-at f0-0f)", "(soft-rock-at f1-1f)", "(holds-bomb)", "(laser-at f2-0f)", "(robot-at f1-0f)", "(soft-rock-at f1-2f)", "(laser-at f2-2f)", "(clear f2-3f)", "(robot-at f2-2f)", "(robot-at f2-1f)", "(clear f0-3f)", "(robot-at f2-0f)", "(laser-at f1-3f)", "(clear f2-1f)"], "yes": ["(robot-at f1-3f)", "(clear f1-3f)", "(robot-at f0-0f)"]}, "PDDL_domain": "(define (domain gold-miner-typed)\n    (:requirements :typing)\n    (:types loc)\n    (:predicates (arm-empty) (bomb-at ?x - loc)  (clear ?x - loc)  (connected ?x - loc ?y - loc)  (gold-at ?x - loc)  (hard-rock-at ?x - loc)  (holds-bomb) (holds-gold) (holds-laser) (laser-at ?x - loc)  (robot-at ?x - loc)  (soft-rock-at ?x - loc))\n    (:action detonate-bomb\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-bomb) (connected ?x ?y) (soft-rock-at ?y))\n        :effect (and (not (holds-bomb)) (arm-empty) (clear ?y) (not (soft-rock-at ?y)))\n    )\n     (:action fire-laser\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (holds-laser) (connected ?x ?y))\n        :effect (and (clear ?y) (not (soft-rock-at ?y)) (not (gold-at ?y)) (not (hard-rock-at ?y)))\n    )\n     (:action move\n        :parameters (?x - loc ?y - loc)\n        :precondition (and (robot-at ?x) (connected ?x ?y) (clear ?y))\n        :effect (and (robot-at ?y) (not (robot-at ?x)))\n    )\n     (:action pick-gold\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (arm-empty) (gold-at ?x))\n        :effect (and (not (arm-empty)) (holds-gold))\n    )\n     (:action pickup-bomb\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (bomb-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-bomb))\n    )\n     (:action pickup-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (laser-at ?x) (arm-empty))\n        :effect (and (not (arm-empty)) (holds-laser) (not (laser-at ?x)))\n    )\n     (:action putdown-laser\n        :parameters (?x - loc)\n        :precondition (and (robot-at ?x) (holds-laser))\n        :effect (and (arm-empty) (not (holds-laser)) (laser-at ?x))\n    )\n)", "PDDL_problem": "(define (problem typed-bomberman-rows3-cols4)\n    (:domain gold-miner-typed)\n    (:requirements :typing)\n    (:objects f0-0f f0-1f f0-2f f0-3f f1-0f f1-1f f1-2f f1-3f f2-0f f2-1f f2-2f f2-3f - loc)\n    (:init (arm-empty) (bomb-at f0-0f) (clear f0-0f) (clear f0-1f) (clear f1-0f) (clear f1-1f) (clear f1-2f) (clear f2-0f) (connected f0-0f f0-1f) (connected f0-0f f1-0f) (connected f0-1f f0-0f) (connected f0-1f f0-2f) (connected f0-1f f1-1f) (connected f0-2f f0-1f) (connected f0-2f f0-3f) (connected f0-2f f1-2f) (connected f0-3f f0-2f) (connected f0-3f f1-3f) (connected f1-0f f0-0f) (connected f1-0f f1-1f) (connected f1-0f f2-0f) (connected f1-1f f0-1f) (connected f1-1f f1-0f) (connected f1-1f f1-2f) (connected f1-1f f2-1f) (connected f1-2f f0-2f) (connected f1-2f f1-1f) (connected f1-2f f1-3f) (connected f1-2f f2-2f) (connected f1-3f f0-3f) (connected f1-3f f1-2f) (connected f1-3f f2-3f) (connected f2-0f f1-0f) (connected f2-0f f2-1f) (connected f2-1f f1-1f) (connected f2-1f f2-0f) (connected f2-1f f2-2f) (connected f2-2f f1-2f) (connected f2-2f f2-1f) (connected f2-2f f2-3f) (connected f2-3f f1-3f) (connected f2-3f f2-2f) (gold-at f1-3f) (hard-rock-at f0-3f) (hard-rock-at f2-1f) (hard-rock-at f2-2f) (laser-at f0-1f) (robot-at f0-1f) (soft-rock-at f0-2f) (soft-rock-at f1-3f) (soft-rock-at f2-3f))\n    (:goal (holds-gold))\n)"}
