{"id": -6561961169987719768, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-3f and is holding key0-0. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-2 is at position f0-4f. Key key0-1 is at position f2-2f.", "question": "Is it possible to transition to a state where the following holds: Robot is not holding anything and Robot is at f2-3f location?", "answer": "yes"}
{"id": 7199958739360794837, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-0 is of shape shape1, Key key1-1 is of shape shape1.  Currently, the robot is at position f0-1f and its arm is empty. All the positions are open except the following: f3-4f has shape1 shaped lock, f2-1f has shape1 shaped lock. Key key1-1 is at position f3-0f. Key key1-0 is at position f1-0f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f1-2f location?", "answer": "yes"}
{"id": 2055922311707026235, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-3f and is holding key0-0. All the positions are open except the following: f2-2f has shape0 shaped lock, f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-2 is at position f0-4f. Key key0-1 is at position f2-2f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f1-1f location?", "answer": "yes"}
{"id": 3589815530864347982, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-4f and its arm is empty. All the positions are open except the following: f2-2f has shape0 shaped lock, f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-2 is at position f0-4f. Key key0-0 is at position f1-3f. Key key0-1 is at position f2-2f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f3-3f location?", "answer": "yes"}
{"id": -8389810127872957892, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-0 is of shape shape1, Key key1-1 is of shape shape1.  Currently, the robot is at position f0-0f and its arm is empty. All the positions are open except the following: f3-4f has shape1 shaped lock, f2-1f has shape1 shaped lock. Key key1-1 is at position f3-0f. Key key1-0 is at position f0-0f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f1-0f location and Robot is holding key1-0?", "answer": "yes"}
{"id": -3280748980533762447, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-3 is of shape shape0.  Currently, the robot is at position f1-1f and its arm is empty. All the positions are open except the following: f0-2f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-0 is at position f1-1f. Key key0-1 is at position f4-2f. Key key0-2 is at position f3-0f. Key key0-3 is at position f0-1f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f2-2f location?", "answer": "yes"}
{"id": 2290808842321697151, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-3 is of shape shape0.  Currently, the robot is at position f2-2f and is holding key0-1. All the positions are open except the following: f0-3f has shape0 shaped lock. Key key0-0 is at position f0-1f. Key key0-2 is at position f4-0f. Key key0-3 is at position f0-1f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f2-0f location?", "answer": "yes"}
{"id": -7745541852822579417, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-3f and its arm is empty. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-0 is at position f1-3f. Key key0-2 is at position f2-3f. Key key0-1 is at position f2-2f.", "question": "Is it possible to transition to a state where the following holds: Robot is at key0-0 location?", "answer": "no"}
{"id": -8845809395763048442, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f4-1f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f0-1f has shape0 shaped lock. Key key0-0 is at position f3-2f. Key key0-1 is at position f4-1f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f4-3f location?", "answer": "yes"}
{"id": -3833354442525456454, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-3f and is holding key0-1. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-2 is at position f0-4f. Key key0-0 is at position f3-3f.", "question": "Is it possible to transition to a state where the following holds: Key key0-1 is at f1-3f location and Robot is holding key0-1?", "answer": "no"}
{"id": 1313050701834406721, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-4f and is holding key0-0. All the positions are open except the following: f0-0f has shape0 shaped lock. Key key0-1 is at position f3-0f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f2-4f location?", "answer": "yes"}
{"id": -8874972491600335569, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-2f and its arm is empty. All the positions are open except the following: f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f1-2f. Key key0-0 is at position f4-4f.", "question": "Is it possible to transition to a state where the following holds: Robot is holding key0-1 and Robot is at f0-2f location?", "answer": "yes"}
{"id": -4837821160497429846, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-2f and its arm is empty. All the positions are open except the following: f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f4-1f. Key key0-0 is at position f4-4f.", "question": "Is it possible to transition to a state where the following holds: Location key0-0 is locked?", "answer": "no"}
{"id": -4857683054103019041, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-1f and its arm is empty. All the positions are open except the following: f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f3-1f. Key key0-0 is at position f4-4f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f4-2f location?", "answer": "yes"}
{"id": -7637773953816219776, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-2f and its arm is empty. All the positions are open except the following: f4-0f has shape0 shaped lock. Key key0-1 is at position f3-2f. Key key0-0 is at position f4-4f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f4-3f location?", "answer": "yes"}
