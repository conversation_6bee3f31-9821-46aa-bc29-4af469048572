{"id": 5168208065347769991, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is free, and right gripper is carrying the ball ball11. Additionally, ball5, ball15, ball2, ball9, ball14, ball4, ball10, and ball6 are at room1, ball7 and ball8 are at room3, ball12, ball1, ball3, and ball13 are at room2.", "question": "Which of the following actions can eventually be applied? A. pick up the object room1 with robot robot1 using left1 gripper from room room2. B. pick up the object room2 with robot robot1 using right1 gripper from room room3. C. move robot robot1 from room room3 to room room2. D. use robot robot1 with right1 gripper to place the object room2 in room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up the object room1 with robot robot1 using left1 gripper from room room2", "pick up the object room2 with robot robot1 using right1 gripper from room room3", "move robot robot1 from room room3 to room room2", "use robot robot1 with right1 gripper to place the object room2 in room room3"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -552221678129268995, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball1 is at room4, ball3 is at room10, ball2 is at room2.", "question": "Which of the following actions can eventually be applied? A. transfer the robot robot1 from room room9 to room room4. B. place the object room9 in the room room7 from the left1 gripper of the robot robot1. C. use the robot robot1 equipped with left1 gripper to retrieve the object room3 from room room6. D. use the robot robot1 equipped with right1 gripper to retrieve the object room8 from room room1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transfer the robot robot1 from room room9 to room room4", "place the object room9 in the room room7 from the left1 gripper of the robot robot1", "use the robot robot1 equipped with left1 gripper to retrieve the object room3 from room room6", "use the robot robot1 equipped with right1 gripper to retrieve the object room8 from room room1"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -1386566616074454206, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball15. Additionally, ball5 and ball7 are at room3, ball2, ball9, ball14, ball8, ball4, ball11, ball10, and ball6 are at room1, ball12, ball1, ball3, and ball13 are at room2.", "question": "Which of the following actions can eventually be applied? A. use the right1 gripper of robot robot1 to drop the object left1 in room room1. B. use the right1 gripper of robot robot1 to drop the object room3 in room room1. C. use the robot robot1 equipped with left1 gripper to retrieve the object ball8 from room room1. D. use the right1 gripper of robot robot1 to drop the object room1 in room room2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use the right1 gripper of robot robot1 to drop the object left1 in room room1", "use the right1 gripper of robot robot1 to drop the object room3 in room room1", "use the robot robot1 equipped with left1 gripper to retrieve the object ball8 from room room1", "use the right1 gripper of robot robot1 to drop the object room1 in room room2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 6997523119699466595, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball7. Additionally, ball6 and ball5 are at room2, ball2, ball4, and ball3 are at room1, ball1 is at room3.", "question": "Which of the following actions can eventually be applied? A. use the left1 gripper of robot robot1 to pick up the object right1 from room room3. B. use the right1 gripper of robot robot1 to pick up the object room3 from room room2. C. use the left1 gripper of robot robot1 to pick up the object room1 from room room3. D. transfer the robot robot1 from room room1 to room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use the left1 gripper of robot robot1 to pick up the object right1 from room room3", "use the right1 gripper of robot robot1 to pick up the object room3 from room room2", "use the left1 gripper of robot robot1 to pick up the object room1 from room room3", "transfer the robot robot1 from room room1 to room room3"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 4403592822595862768, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball2 is at room1, ball3 is at room5, ball1 is at room2.", "question": "Which of the following actions can eventually be applied? A. use the right1 gripper of robot robot1 to pick up the object room5 from room room1. B. move the robot robot1 from room room5 to room room1. C. place the object room4 in the room room2 using the robot robot1 with right1 gripper. D. place the object left1 in the room room5 using the robot robot1 with right1 gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use the right1 gripper of robot robot1 to pick up the object room5 from room room1", "move the robot robot1 from room room5 to room room1", "place the object room4 in the room room2 using the robot robot1 with right1 gripper", "place the object left1 in the room room5 using the robot robot1 with right1 gripper"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -6351308511618536989, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball5, and right gripper is carrying the ball ball3. Additionally, ball2, ball4, and ball1 are at room1, ball7 is at room3, ball6 is at room2.", "question": "Which of the following actions can eventually be applied? A. pick up the object room3 with the robot robot1 using the left1 gripper from the room room1. B. place the object room3 in the room room2 using the robot robot1 with left1 gripper. C. place the object room2 in the room room1 using the robot robot1 with right1 gripper. D. transfer the robot robot1 from room room3 to room room2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up the object room3 with the robot robot1 using the left1 gripper from the room room1", "place the object room3 in the room room2 using the robot robot1 with left1 gripper", "place the object room2 in the room room1 using the robot robot1 with right1 gripper", "transfer the robot robot1 from room room3 to room room2"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 907446152243268719, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball3. Additionally, ball2 is at room1, ball1 and ball4 are at room2.", "question": "Which of the following actions can eventually be applied? A. grasp the object ball1 from room room2 with the left1 gripper of robot robot1. B. use robot robot1 with right1 gripper to place the object left1 in room room1. C. use robot robot1 with left1 gripper to place the object right1 in room room2. D. grasp the object right1 from room room1 with the left1 gripper of robot robot1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["grasp the object ball1 from room room2 with the left1 gripper of robot robot1", "use robot robot1 with right1 gripper to place the object left1 in room room1", "use robot robot1 with left1 gripper to place the object right1 in room room2", "grasp the object right1 from room room1 with the left1 gripper of robot robot1"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -8649513778453739, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is free, and right gripper is carrying the ball ball7. Additionally, ball6 and ball5 are at room2, ball2, ball4, and ball3 are at room1, ball1 is at room3.", "question": "Which of the following actions can eventually be applied? A. place the object room3 in the room room1 from the right1 gripper of the robot robot1. B. place the object right1 in the room room2 from the left1 gripper of the robot robot1. C. move robot robot1 from room room3 to room room1. D. grasp the object left1 from room room2 with the right1 gripper of robot robot1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object room3 in the room room1 from the right1 gripper of the robot robot1", "place the object right1 in the room room2 from the left1 gripper of the robot robot1", "move robot robot1 from room room3 to room room1", "grasp the object left1 from room room2 with the right1 gripper of robot robot1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 3322071119175369768, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball10, and right gripper is carrying the ball ball12. Additionally, ball5, ball2, ball9, ball14, ball4, ball11, and ball6 are at room1, ball7 and ball8 are at room3, ball13, ball1, ball3, and ball15 are at room2.", "question": "Which of the following actions can eventually be applied? A. use the robot robot1 equipped with left1 gripper to retrieve the object room3 from room room1. B. use the robot robot1 equipped with left1 gripper to retrieve the object right1 from room room2. C. drop the object ball10 in the left1 gripper of the robot robot1 at the room room1. D. use the robot robot1 equipped with right1 gripper to retrieve the object room1 from room room2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use the robot robot1 equipped with left1 gripper to retrieve the object room3 from room room1", "use the robot robot1 equipped with left1 gripper to retrieve the object right1 from room room2", "drop the object ball10 in the left1 gripper of the robot robot1 at the room room1", "use the robot robot1 equipped with right1 gripper to retrieve the object room1 from room room2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -4617601423969873452, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3, right gripper is free, and left gripper is carrying the ball ball7. Additionally, ball6 and ball5 are at room2, ball2, ball4, and ball3 are at room1, ball1 is at room3.", "question": "Which of the following actions can eventually be applied? A. move the robot robot1 from room room2 to room room3. B. use the robot robot1 equipped with left1 gripper to retrieve the object room2 from room room3. C. drop the object room3 in the right1 gripper of the robot robot1 at the room room1. D. use the robot robot1 equipped with right1 gripper to retrieve the object room1 from room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move the robot robot1 from room room2 to room room3", "use the robot robot1 equipped with left1 gripper to retrieve the object room2 from room room3", "drop the object room3 in the right1 gripper of the robot robot1 at the room room1", "use the robot robot1 equipped with right1 gripper to retrieve the object room1 from room room3"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 2172192209001455935, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5, left gripper is carrying the ball ball1, and right gripper is carrying the ball ball2. Additionally, ball4 is at room4, ball3 is at room3.", "question": "Which of the following actions can eventually be applied? A. use the right1 gripper of robot robot1 to drop the object room5 in room room1. B. use the right1 gripper of robot robot1 to drop the object room4 in room room5. C. use the right1 gripper of robot robot1 to drop the object ball2 in room room4. D. use the right1 gripper of robot robot1 to drop the object room1 in room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use the right1 gripper of robot robot1 to drop the object room5 in room room1", "use the right1 gripper of robot robot1 to drop the object room4 in room room5", "use the right1 gripper of robot robot1 to drop the object ball2 in room room4", "use the right1 gripper of robot robot1 to drop the object room1 in room room3"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 7017819477300952567, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball1, and right gripper is carrying the ball ball4. Additionally, ball2 is at room2, ball3 is at room1.", "question": "Which of the following actions can eventually be applied? A. use the robot robot1 equipped with right1 gripper to retrieve the object room2 from room room1. B. use the robot robot1 equipped with left1 gripper to retrieve the object ball4 from room room1. C. use the robot robot1 equipped with right1 gripper to retrieve the object left1 from room room1. D. place the object right1 in the room room2 using the robot robot1 with left1 gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use the robot robot1 equipped with right1 gripper to retrieve the object room2 from room room1", "use the robot robot1 equipped with left1 gripper to retrieve the object ball4 from room room1", "use the robot robot1 equipped with right1 gripper to retrieve the object left1 from room room1", "place the object right1 in the room room2 using the robot robot1 with left1 gripper"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 3700010459572399014, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball4. Additionally, ball2 and ball1 are at room1.", "question": "Which of the following actions can eventually be applied? A. place the object room2 in the room room3 from the right1 gripper of the robot robot1. B. use the left1 gripper of robot robot1 to pick up the object room3 from room room1. C. place the object left1 in the room room3 from the right1 gripper of the robot robot1. D. transfer the robot robot1 from the room room2 to the room room1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object room2 in the room room3 from the right1 gripper of the robot robot1", "use the left1 gripper of robot robot1 to pick up the object room3 from room room1", "place the object left1 in the room room3 from the right1 gripper of the robot robot1", "transfer the robot robot1 from the room room2 to the room room1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 4414687145792024580, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball1. Additionally, ball2 is at room2, ball4 is at room1.", "question": "Which of the following actions can eventually be applied? A. use the right1 gripper of robot robot1 to pick up the object room2 from room room5. B. use the right1 gripper of robot robot1 to pick up the object room4 from room room5. C. drop the object ball1 in the right1 gripper of the robot robot1 at the room room5. D. use the left1 gripper of robot robot1 to pick up the object room2 from room room5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use the right1 gripper of robot robot1 to pick up the object room2 from room room5", "use the right1 gripper of robot robot1 to pick up the object room4 from room room5", "drop the object ball1 in the right1 gripper of the robot robot1 at the room room5", "use the left1 gripper of robot robot1 to pick up the object room2 from room room5"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -5999934745431998417, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball2. Additionally, ball3 is at room3, ball4 is at room1, ball1 is at room2.", "question": "Which of the following actions can eventually be applied? A. transfer the robot robot1 from room room1 to room room3. B. pick up the object room1 with the robot robot1 using the right1 gripper from the room room2. C. drop the object left1 in the right1 gripper of the robot robot1 at the room room1. D. pick up the object room1 with the robot robot1 using the right1 gripper from the room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transfer the robot robot1 from room room1 to room room3", "pick up the object room1 with the robot robot1 using the right1 gripper from the room room2", "drop the object left1 in the right1 gripper of the robot robot1 at the room room1", "pick up the object room1 with the robot robot1 using the right1 gripper from the room room3"]}, "query": "Which action is reachable from this state?", "answer": "A"}
