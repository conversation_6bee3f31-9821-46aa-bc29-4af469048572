{"id": 7810040286699608104, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l2-7, l2-1, l2-9, l2-3, l2-5, l2-0, l2-6, l2-8, l2-2, and l2-4 are in c2; l4-0, l4-2, l4-4, l4-6, l4-1, l4-8, l4-9, l4-5, l4-3, and l4-7 are in c4; l0-1, l0-6, l0-4, l0-0, l0-7, l0-3, l0-2, l0-5, l0-8, and l0-9 are in c0; l1-8, l1-6, l1-7, l1-4, l1-3, l1-5, l1-0, l1-1, l1-2, and l1-9 are in c1; l3-7, l3-2, l3-1, l3-6, l3-3, l3-4, l3-5, l3-8, l3-9, and l3-0 are in c3. Currently, t2 and p1 are at l2-7, a0 and t1 are at l1-0, p2 is at l4-8, t0 is at l0-2, t3 is at l3-0, t4 is at l4-7, p0 and p3 are in t1. The goal is to reach a state where the following facts hold: p1 is at l2-7, p3 is at l1-1, p0 is at l1-5, and p2 is at l4-8. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p0 t3)", "(at p3 l4-8)", "(at p1 l0-0)", "(at p2 l4-4)", "(in p2 t0)", "(at p3 l2-3)", "(at p0 l1-9)", "(at p0 l2-7)", "(at p2 l3-0)", "(at p2 l0-3)", "(at t3 l3-6)", "(at t0 l0-7)", "(at p2 l2-0)", "(at p0 l2-9)", "(at p0 l4-9)", "(at p3 l1-9)", "(in p2 t1)", "(at p0 l3-9)", "(in p3 a0)", "(at p1 l4-3)", "(at p2 l3-1)", "(at p1 l2-3)", "(at p2 l0-1)", "(at p3 l1-4)", "(at p1 l0-4)", "(at p3 l2-5)", "(at p0 l2-0)", "(at t4 l4-8)", "(at p0 l0-5)", "(at p0 l0-2)", "(at t4 l4-0)", "(at p3 l2-8)", "(at p3 l2-1)", "(at p1 l3-1)", "(at t2 l2-5)", "(at p3 l4-1)", "(in p0 t4)", "(at p3 l1-8)", "(at p0 l4-5)", "(at p2 l0-5)", "(at t3 l3-3)", "(at t2 l2-3)", "(at p3 l2-0)", "(at p2 l4-0)", "(at p3 l4-4)", "(at p2 l1-8)", "(at p3 l3-7)", "(at t2 l2-4)", "(at p1 l3-2)", "(at t0 l0-3)", "(at p2 l2-9)", "(at p3 l4-9)", "(at p3 l0-1)", "(at a0 l2-0)", "(at p0 l3-5)", "(at a0 l4-0)", "(at p0 l2-5)", "(at p0 l3-7)", "(at p0 l0-4)", "(at t3 l3-8)", "(in p0 t0)", "(at t4 l4-4)", "(at t1 l1-4)", "(at p2 l4-7)", "(at t0 l0-8)", "(at p3 l2-6)", "(in p1 t0)", "(at t1 l1-2)", "(at p3 l3-6)", "(at p0 l2-1)", "(at p0 l1-7)", "(at t3 l3-5)", "(at p1 l3-8)", "(at p1 l4-8)", "(at t2 l2-9)", "(at p1 l1-5)", "(at p0 l0-0)", "(at p0 l1-4)", "(at t4 l4-6)", "(at p0 l4-7)", "(at p1 l3-5)", "(at p3 l0-9)", "(at p3 l0-8)", "(at p3 l1-2)", "(at p1 l0-3)", "(at p3 l4-2)", "(at p2 l1-5)", "(at p3 l3-5)", "(at p0 l3-4)", "(at p2 l2-4)", "(at p1 l3-9)", "(at p2 l4-3)", "(at p3 l3-4)", "(at p1 l1-1)", "(at p2 l3-3)", "(at p1 l0-9)", "(at t4 l4-2)", "(at p2 l0-0)", "(at p2 l4-1)", "(at p0 l0-6)", "(at p2 l1-6)", "(at p0 l2-2)", "(at p1 l4-6)", "(at p2 l3-2)", "(at p0 l2-8)", "(at p0 l3-6)", "(at t4 l4-1)", "(at p1 l0-6)", "(at t0 l0-5)", "(at p1 l1-9)", "(at t0 l0-9)", "(at p3 l1-3)", "(at p1 l1-7)", "(at p1 l1-0)", "(at p3 l2-4)", "(at p1 l4-0)", "(in p3 t4)", "(at p3 l4-0)", "(at p1 l1-6)", "(at p2 l1-1)", "(at p3 l3-8)", "(at p0 l3-1)", "(in p3 t0)", "(at p2 l1-0)", "(at t1 l1-9)", "(at p1 l4-4)", "(at p1 l4-9)", "(at p0 l4-8)", "(at p3 l0-7)", "(at t4 l4-5)", "(at p2 l1-7)", "(at p1 l1-4)", "(at t1 l1-7)", "(at p1 l2-8)", "(at t4 l4-9)", "(at p0 l4-2)", "(at p2 l2-3)", "(at p1 l4-7)", "(at p1 l3-6)", "(at p1 l2-6)", "(in p3 t2)", "(at p3 l3-9)", "(at p3 l4-3)", "(at t3 l3-9)", "(at p3 l1-7)", "(at p1 l0-1)", "(at p3 l2-9)", "(at p0 l1-2)", "(at p2 l0-9)", "(at p3 l0-6)", "(at p2 l0-2)", "(at p3 l0-3)", "(at p1 l0-8)", "(at p1 l1-3)", "(in p1 t2)", "(at p2 l1-4)", "(at p0 l3-8)", "(at p3 l0-2)", "(at p2 l1-2)", "(at p0 l0-9)", "(at t2 l2-8)", "(at p1 l2-9)", "(at p1 l1-2)", "(at t1 l1-3)", "(at p3 l0-5)", "(at p3 l3-1)", "(in p1 a0)", "(at p1 l2-1)", "(at t0 l0-0)", "(at t3 l3-1)", "(at p3 l0-0)", "(at p2 l0-7)", "(at p1 l3-3)", "(at p2 l4-9)", "(at t2 l2-2)", "(in p2 t3)", "(at p3 l3-0)", "(at p0 l1-6)", "(at p1 l0-2)", "(at a0 l3-0)", "(at a0 l0-0)", "(at p1 l2-4)", "(in p0 t2)", "(in p3 t3)", "(at p3 l1-0)", "(at p3 l4-5)", "(at p2 l4-5)", "(at p0 l2-6)", "(at t0 l0-6)", "(at p3 l1-6)", "(at p2 l2-6)", "(at p3 l4-6)", "(at p2 l3-7)", "(at p1 l4-2)", "(in p2 a0)", "(at p1 l4-1)", "(in p1 t1)", "(at p0 l3-3)", "(at p2 l3-9)", "(at p2 l4-2)", "(in p2 t4)", "(at p0 l4-1)", "(at t2 l2-6)", "(at p0 l1-3)", "(at p0 l3-0)", "(at t2 l2-1)", "(at p2 l3-5)", "(at p2 l4-6)", "(at p1 l2-2)", "(at p0 l0-1)", "(at t1 l1-8)", "(at p2 l2-2)", "(at p2 l0-4)", "(at p2 l0-6)", "(at t4 l4-3)", "(at t3 l3-2)", "(at p0 l4-3)", "(at p2 l1-3)", "(at p0 l1-8)", "(at p2 l2-7)", "(at p2 l3-8)", "(at p3 l2-7)", "(at p1 l1-8)", "(at p0 l3-2)", "(at p1 l0-7)", "(at p1 l3-4)", "(at p2 l2-1)", "(at p2 l2-8)", "(in p0 a0)", "(at p0 l2-3)", "(at p3 l2-2)", "(at p0 l4-4)", "(at t0 l0-1)", "(at p0 l0-3)", "(in p2 t2)", "(at p3 l4-7)", "(at p2 l2-5)", "(at p3 l0-4)", "(at t0 l0-4)", "(at p0 l1-1)", "(at p2 l1-9)", "(in p1 t3)", "(at t1 l1-6)", "(at p0 l1-0)", "(at p1 l0-5)", "(at p3 l3-2)", "(at p1 l2-0)", "(at t3 l3-4)", "(at p0 l0-8)", "(at p2 l3-4)", "(at p3 l1-5)", "(at p3 l3-3)", "(in p1 t4)", "(at t3 l3-7)", "(at p0 l2-4)", "(at p1 l3-0)", "(at p1 l3-7)", "(at p0 l0-7)", "(at p1 l4-5)", "(at p0 l4-0)", "(at p2 l0-8)", "(at t2 l2-0)", "(at p2 l3-6)", "(at p0 l4-6)", "(at p1 l2-5)"], "yes": ["(at t1 l1-1)", "(at t1 l1-5)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s10-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l0-3 l0-4 l0-5 l0-6 l0-7 l0-8 l0-9 l1-1 l1-2 l1-3 l1-4 l1-5 l1-6 l1-7 l1-8 l1-9 l2-1 l2-2 l2-3 l2-4 l2-5 l2-6 l2-7 l2-8 l2-9 l3-1 l3-2 l3-3 l3-4 l3-5 l3-6 l3-7 l3-8 l3-9 l4-1 l4-2 l4-3 l4-4 l4-5 l4-6 l4-7 l4-8 l4-9 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l1-0) (at p1 l2-7) (at p2 l4-8) (at t0 l0-2) (at t1 l1-0) (at t2 l2-7) (at t3 l3-0) (at t4 l4-7) (in p0 t1) (in p3 t1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l0-3 c0) (in-city l0-4 c0) (in-city l0-5 c0) (in-city l0-6 c0) (in-city l0-7 c0) (in-city l0-8 c0) (in-city l0-9 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l1-3 c1) (in-city l1-4 c1) (in-city l1-5 c1) (in-city l1-6 c1) (in-city l1-7 c1) (in-city l1-8 c1) (in-city l1-9 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l2-3 c2) (in-city l2-4 c2) (in-city l2-5 c2) (in-city l2-6 c2) (in-city l2-7 c2) (in-city l2-8 c2) (in-city l2-9 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l3-3 c3) (in-city l3-4 c3) (in-city l3-5 c3) (in-city l3-6 c3) (in-city l3-7 c3) (in-city l3-8 c3) (in-city l3-9 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4) (in-city l4-3 c4) (in-city l4-4 c4) (in-city l4-5 c4) (in-city l4-6 c4) (in-city l4-7 c4) (in-city l4-8 c4) (in-city l4-9 c4))\n    (:goal (and (at p0 l1-5) (at p1 l2-7) (at p2 l4-8) (at p3 l1-1)))\n)"}
{"id": -8500730819952977935, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l4-1, l4-0, and l4-2 are in c4; l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1; l2-1, l2-0, and l2-2 are in c2; l3-1, l3-2, and l3-0 are in c3. Currently, p0, a0, and t2 are at l2-0, p3 is at l4-1, t0 is at l0-2, t1 is at l1-2, t3 is at l3-0, t4 is at l4-0, p1 is in t3, p2 is in t2. The goal is to reach a state where the following facts hold: p2 is at l2-2, p1 is at l3-2, p0 is at l3-0, and p3 is at l3-2. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p1 t1)", "(in p0 t3)", "(at p1 l0-0)", "(in p1 t0)", "(at p2 l4-2)", "(in p2 t4)", "(in p2 t0)", "(at t1 l1-0)", "(at p0 l2-1)", "(at p2 l3-0)", "(at p0 l4-1)", "(at t2 l2-1)", "(at p2 l2-0)", "(at p0 l4-2)", "(at p1 l2-2)", "(at p0 l0-1)", "(in p2 t1)", "(at p0 l0-0)", "(at p3 l1-2)", "(in p3 t2)", "(at p2 l3-1)", "(at p3 l4-2)", "(at p2 l0-1)", "(at p1 l0-1)", "(at p1 l1-1)", "(at p0 l1-2)", "(at t4 l4-2)", "(at p0 l3-2)", "(at p2 l0-2)", "(in p1 t2)", "(at p2 l2-1)", "(at p0 l0-2)", "(at p2 l0-0)", "(at p2 l4-1)", "(at p3 l2-2)", "(at p2 l3-2)", "(at p0 l2-2)", "(at p3 l2-1)", "(at p1 l3-1)", "(at t0 l0-1)", "(at p3 l0-2)", "(at p2 l1-2)", "(at p1 l1-2)", "(in p0 t4)", "(at p3 l3-1)", "(in p1 a0)", "(at p3 l2-0)", "(at p0 l1-1)", "(at p2 l4-0)", "(at t1 l1-1)", "(at p1 l2-1)", "(at p0 l1-0)", "(at t0 l0-0)", "(at t3 l3-1)", "(at p3 l0-0)", "(in p0 t1)", "(at p1 l2-0)", "(in p3 t1)", "(at p1 l1-0)", "(in p2 t3)", "(at p3 l0-1)", "(at p1 l0-2)", "(at a0 l0-0)", "(at p1 l4-0)", "(in p1 t4)", "(in p0 t2)", "(at p3 l1-0)", "(at p2 l1-1)", "(at p1 l3-0)", "(at a0 l1-0)", "(at p0 l3-1)", "(in p3 t0)", "(in p0 t0)", "(at p2 l1-0)", "(at p0 l4-0)", "(at p3 l1-1)", "(at p1 l4-2)", "(in p2 a0)", "(at p1 l4-1)"], "yes": ["(in p3 t3)", "(at t3 l3-2)", "(at p3 l3-0)", "(at p3 l4-0)", "(in p3 a0)", "(in p3 t4)", "(at t2 l2-2)", "(in p0 a0)", "(at a0 l3-0)", "(at a0 l4-0)", "(at t4 l4-1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 l3-1 l3-2 l4-1 l4-2 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l2-0) (at p0 l2-0) (at p3 l4-1) (at t0 l0-2) (at t1 l1-2) (at t2 l2-0) (at t3 l3-0) (at t4 l4-0) (in p1 t3) (in p2 t2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4))\n    (:goal (and (at p0 l3-0) (at p1 l3-2) (at p2 l2-2) (at p3 l3-2)))\n)"}
{"id": -7742846664494019863, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, a0, t0, and p3 are at l0-0, t1 is at l1-1, p2 and p0 are at l0-1, p1 is at l1-0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p3 is at l1-1, p2 is at l1-0, and p0 is at l1-1. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p3 t0)", "(at p1 l0-0)", "(in p1 t0)", "(at p1 l0-1)", "(in p1 a0)", "(in p2 t1)", "(at p2 l1-1)", "(at p3 l0-1)"], "yes": ["(in p3 t1)", "(at p3 l1-0)", "(in p3 a0)", "(in p2 a0)", "(at a0 l1-0)", "(at p2 l0-0)", "(in p2 t0)", "(in p1 t1)", "(in p0 t1)", "(at p0 l0-0)", "(at p0 l1-0)", "(in p0 a0)", "(in p0 t0)", "(at t1 l1-0)", "(at t0 l0-1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l0-1) (at p1 l1-0) (at p2 l0-1) (at p3 l0-0) (at t0 l0-0) (at t1 l1-1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-1)))\n)"}
{"id": -8509356278483209597, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l4-1, l4-0, and l4-2 are in c4; l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1; l2-1, l2-0, and l2-2 are in c2; l3-1, l3-2, and l3-0 are in c3. Currently, p1 is at l3-1, a0 and t4 are at l4-0, p3 is at l4-1, t0 is at l0-2, t1 is at l1-2, t3 is at l3-0, t2 is at l2-0, p2 is at l2-2, p0 is in a0. The goal is to reach a state where the following facts hold: p2 is at l2-2, p1 is at l3-2, p0 is at l3-0, and p3 is at l3-2. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p1 t1)", "(in p0 t3)", "(at p1 l0-0)", "(in p1 t0)", "(at p2 l4-2)", "(in p2 t4)", "(in p2 t0)", "(at t1 l1-0)", "(at p0 l2-1)", "(at p2 l3-0)", "(at p0 l4-1)", "(at t2 l2-1)", "(at p2 l2-0)", "(at p0 l4-2)", "(at p1 l2-2)", "(at p0 l0-1)", "(in p2 t1)", "(at p0 l0-0)", "(at p3 l1-2)", "(in p3 t2)", "(at p2 l3-1)", "(at p3 l4-2)", "(at p2 l0-1)", "(at p1 l0-1)", "(at p1 l1-1)", "(at p0 l1-2)", "(at t4 l4-2)", "(at p0 l3-2)", "(at p2 l0-2)", "(at p0 l2-0)", "(in p1 t2)", "(at p2 l2-1)", "(at p0 l0-2)", "(at p2 l0-0)", "(at p2 l4-1)", "(at p3 l2-2)", "(at p2 l3-2)", "(at p0 l2-2)", "(at p3 l2-1)", "(at t0 l0-1)", "(at p3 l0-2)", "(at p2 l1-2)", "(in p2 t2)", "(at p1 l1-2)", "(in p0 t4)", "(at p3 l3-1)", "(in p1 a0)", "(at p3 l2-0)", "(at p0 l1-1)", "(at p2 l4-0)", "(at t1 l1-1)", "(at p1 l2-1)", "(at p0 l1-0)", "(at t0 l0-0)", "(at p3 l0-0)", "(in p0 t1)", "(at p1 l2-0)", "(at t2 l2-2)", "(in p3 t1)", "(at p1 l1-0)", "(in p2 t3)", "(at p3 l0-1)", "(at p1 l0-2)", "(at a0 l0-0)", "(at a0 l2-0)", "(at p1 l4-0)", "(in p1 t4)", "(in p0 t2)", "(at p3 l1-0)", "(at p2 l1-1)", "(at p1 l3-0)", "(at a0 l1-0)", "(at p0 l3-1)", "(in p3 t0)", "(in p0 t0)", "(at p2 l1-0)", "(at p0 l4-0)", "(at p3 l1-1)", "(at p1 l4-2)", "(in p2 a0)", "(at p1 l4-1)"], "yes": ["(in p3 t3)", "(at t3 l3-2)", "(at p3 l3-0)", "(at p3 l4-0)", "(in p3 a0)", "(in p3 t4)", "(in p1 t3)", "(at a0 l3-0)", "(at t4 l4-1)", "(at t3 l3-1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 l3-1 l3-2 l4-1 l4-2 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l4-0) (at p1 l3-1) (at p2 l2-2) (at p3 l4-1) (at t0 l0-2) (at t1 l1-2) (at t2 l2-0) (at t3 l3-0) (at t4 l4-0) (in p0 a0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4))\n    (:goal (and (at p0 l3-0) (at p1 l3-2) (at p2 l2-2) (at p3 l3-2)))\n)"}
{"id": 5081963180761945619, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l2-7, l2-1, l2-9, l2-3, l2-5, l2-0, l2-6, l2-8, l2-2, and l2-4 are in c2; l4-0, l4-2, l4-4, l4-6, l4-1, l4-8, l4-9, l4-5, l4-3, and l4-7 are in c4; l0-1, l0-6, l0-4, l0-0, l0-7, l0-3, l0-2, l0-5, l0-8, and l0-9 are in c0; l1-8, l1-6, l1-7, l1-4, l1-3, l1-5, l1-0, l1-1, l1-2, and l1-9 are in c1; l3-7, l3-2, l3-1, l3-6, l3-3, l3-4, l3-5, l3-8, l3-9, and l3-0 are in c3. Currently, t2 and p1 are at l2-7, a0, t1, and p3 are at l1-0, t4 and p2 are at l4-8, t0 is at l0-2, t3 is at l3-0, p0 is in t1. The goal is to reach a state where the following facts hold: p1 is at l2-7, p3 is at l1-1, p0 is at l1-5, and p2 is at l4-8. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p0 t3)", "(at p3 l4-8)", "(at p1 l0-0)", "(at p2 l4-4)", "(in p2 t0)", "(at p3 l2-3)", "(at p0 l1-9)", "(at p0 l2-7)", "(at p2 l3-0)", "(at p2 l0-3)", "(at t3 l3-6)", "(at t0 l0-7)", "(at p2 l2-0)", "(at p0 l2-9)", "(at p0 l4-9)", "(at p3 l1-9)", "(in p2 t1)", "(at p0 l3-9)", "(in p3 a0)", "(at p1 l4-3)", "(at p2 l3-1)", "(at p1 l2-3)", "(at p2 l0-1)", "(at p3 l1-4)", "(at p1 l0-4)", "(at p3 l2-5)", "(at p0 l2-0)", "(at p0 l0-5)", "(at p0 l0-2)", "(at t4 l4-0)", "(at p3 l2-8)", "(at p3 l2-1)", "(at p1 l3-1)", "(at t2 l2-5)", "(at p3 l4-1)", "(in p0 t4)", "(at p3 l1-8)", "(at p0 l4-5)", "(at p2 l0-5)", "(at t3 l3-3)", "(at t2 l2-3)", "(at p3 l2-0)", "(at p2 l4-0)", "(at p3 l4-4)", "(at p2 l1-8)", "(at p3 l3-7)", "(at t2 l2-4)", "(at p1 l3-2)", "(at t0 l0-3)", "(at p2 l2-9)", "(at p3 l4-9)", "(at p3 l0-1)", "(at a0 l2-0)", "(at p0 l3-5)", "(at a0 l4-0)", "(at p0 l2-5)", "(at p0 l3-7)", "(at p0 l0-4)", "(at t3 l3-8)", "(in p0 t0)", "(at t4 l4-7)", "(at t4 l4-4)", "(at t1 l1-4)", "(at p2 l4-7)", "(at t0 l0-8)", "(at p3 l2-6)", "(in p1 t0)", "(at t1 l1-2)", "(at p3 l3-6)", "(at p0 l2-1)", "(at p0 l1-7)", "(at t3 l3-5)", "(at p1 l3-8)", "(at p1 l4-8)", "(at t2 l2-9)", "(at p1 l1-5)", "(at p0 l0-0)", "(at p0 l1-4)", "(at t4 l4-6)", "(at p0 l4-7)", "(at p1 l3-5)", "(at p3 l0-9)", "(at p3 l0-8)", "(at p3 l1-2)", "(at p1 l0-3)", "(at p3 l4-2)", "(at p2 l1-5)", "(at p3 l3-5)", "(at p0 l3-4)", "(at p2 l2-4)", "(at p1 l3-9)", "(at p2 l4-3)", "(at p3 l3-4)", "(at p1 l1-1)", "(at p2 l3-3)", "(at p1 l0-9)", "(at t4 l4-2)", "(at p2 l0-0)", "(at p2 l4-1)", "(at p0 l0-6)", "(at p2 l1-6)", "(at p0 l2-2)", "(at p1 l4-6)", "(at p2 l3-2)", "(at p0 l2-8)", "(at p0 l3-6)", "(at t4 l4-1)", "(at p1 l0-6)", "(at t0 l0-5)", "(at p1 l1-9)", "(at t0 l0-9)", "(at p3 l1-3)", "(at p1 l1-7)", "(at p1 l1-0)", "(at p3 l2-4)", "(at p1 l4-0)", "(in p3 t4)", "(at p3 l4-0)", "(at p1 l1-6)", "(at p2 l1-1)", "(at p3 l3-8)", "(at p0 l3-1)", "(in p3 t0)", "(at p2 l1-0)", "(at t1 l1-9)", "(at p1 l4-4)", "(at p1 l4-9)", "(at p0 l4-8)", "(at p3 l0-7)", "(at t4 l4-5)", "(at p2 l1-7)", "(at p1 l1-4)", "(at t1 l1-7)", "(at p1 l2-8)", "(at t4 l4-9)", "(at p0 l4-2)", "(at p2 l2-3)", "(at p1 l4-7)", "(at p1 l3-6)", "(at p1 l2-6)", "(in p3 t2)", "(at p3 l3-9)", "(at p3 l4-3)", "(at t3 l3-9)", "(at p3 l1-7)", "(at p1 l0-1)", "(at p3 l2-9)", "(at p0 l1-2)", "(at p2 l0-9)", "(at p3 l0-6)", "(at p2 l0-2)", "(at p3 l0-3)", "(at p1 l0-8)", "(at p1 l1-3)", "(in p1 t2)", "(at p2 l1-4)", "(at p0 l3-8)", "(at p3 l0-2)", "(at p2 l1-2)", "(at p0 l0-9)", "(at t2 l2-8)", "(at p1 l2-9)", "(at p1 l1-2)", "(at t1 l1-3)", "(at p3 l0-5)", "(at p3 l3-1)", "(in p1 a0)", "(at p1 l2-1)", "(at t0 l0-0)", "(at t3 l3-1)", "(at p3 l0-0)", "(at p2 l0-7)", "(at p1 l3-3)", "(at p2 l4-9)", "(at t2 l2-2)", "(in p2 t3)", "(at p3 l3-0)", "(at p0 l1-6)", "(at p1 l0-2)", "(at a0 l3-0)", "(at a0 l0-0)", "(at p1 l2-4)", "(in p0 t2)", "(in p3 t3)", "(at p3 l4-5)", "(at p2 l4-5)", "(at p0 l2-6)", "(at t0 l0-6)", "(at p3 l1-6)", "(at p2 l2-6)", "(at p3 l4-6)", "(at p2 l3-7)", "(at p1 l4-2)", "(in p2 a0)", "(at p1 l4-1)", "(in p1 t1)", "(at p0 l3-3)", "(at p2 l3-9)", "(at p2 l4-2)", "(in p2 t4)", "(at p0 l4-1)", "(at t2 l2-6)", "(at p0 l1-3)", "(at p0 l3-0)", "(at t2 l2-1)", "(at p2 l3-5)", "(at p2 l4-6)", "(at p1 l2-2)", "(at p0 l0-1)", "(at t1 l1-8)", "(at p2 l2-2)", "(at p2 l0-4)", "(at p2 l0-6)", "(at t4 l4-3)", "(at t3 l3-2)", "(at p0 l4-3)", "(at p2 l1-3)", "(at p0 l1-8)", "(at p2 l2-7)", "(at p2 l3-8)", "(at p3 l2-7)", "(at p1 l1-8)", "(at p0 l3-2)", "(at p1 l0-7)", "(at p1 l3-4)", "(at p2 l2-1)", "(at p2 l2-8)", "(in p0 a0)", "(at p0 l2-3)", "(at p3 l2-2)", "(at p0 l4-4)", "(at t0 l0-1)", "(at p0 l0-3)", "(in p2 t2)", "(at p3 l4-7)", "(at p2 l2-5)", "(at p3 l0-4)", "(at t0 l0-4)", "(at p0 l1-1)", "(at p2 l1-9)", "(in p1 t3)", "(at t1 l1-6)", "(at p0 l1-0)", "(at p1 l0-5)", "(at p3 l3-2)", "(at p1 l2-0)", "(at t3 l3-4)", "(at p0 l0-8)", "(at p2 l3-4)", "(at p3 l1-5)", "(at p3 l3-3)", "(in p1 t4)", "(at t3 l3-7)", "(at p0 l2-4)", "(at p1 l3-0)", "(at p1 l3-7)", "(at p0 l0-7)", "(at p1 l4-5)", "(at p0 l4-0)", "(at p2 l0-8)", "(at t2 l2-0)", "(at p2 l3-6)", "(at p0 l4-6)", "(at p1 l2-5)"], "yes": ["(in p3 t1)", "(at t1 l1-1)", "(at t1 l1-5)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s10-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l0-3 l0-4 l0-5 l0-6 l0-7 l0-8 l0-9 l1-1 l1-2 l1-3 l1-4 l1-5 l1-6 l1-7 l1-8 l1-9 l2-1 l2-2 l2-3 l2-4 l2-5 l2-6 l2-7 l2-8 l2-9 l3-1 l3-2 l3-3 l3-4 l3-5 l3-6 l3-7 l3-8 l3-9 l4-1 l4-2 l4-3 l4-4 l4-5 l4-6 l4-7 l4-8 l4-9 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l1-0) (at p1 l2-7) (at p2 l4-8) (at p3 l1-0) (at t0 l0-2) (at t1 l1-0) (at t2 l2-7) (at t3 l3-0) (at t4 l4-8) (in p0 t1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l0-3 c0) (in-city l0-4 c0) (in-city l0-5 c0) (in-city l0-6 c0) (in-city l0-7 c0) (in-city l0-8 c0) (in-city l0-9 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l1-3 c1) (in-city l1-4 c1) (in-city l1-5 c1) (in-city l1-6 c1) (in-city l1-7 c1) (in-city l1-8 c1) (in-city l1-9 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l2-3 c2) (in-city l2-4 c2) (in-city l2-5 c2) (in-city l2-6 c2) (in-city l2-7 c2) (in-city l2-8 c2) (in-city l2-9 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l3-3 c3) (in-city l3-4 c3) (in-city l3-5 c3) (in-city l3-6 c3) (in-city l3-7 c3) (in-city l3-8 c3) (in-city l3-9 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4) (in-city l4-3 c4) (in-city l4-4 c4) (in-city l4-5 c4) (in-city l4-6 c4) (in-city l4-7 c4) (in-city l4-8 c4) (in-city l4-9 c4))\n    (:goal (and (at p0 l1-5) (at p1 l2-7) (at p2 l4-8) (at p3 l1-1)))\n)"}
{"id": -6739596301530628611, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1. Currently, a0 and t0 are at l0-0, p3 and p1 are at l0-2, t1 is at l1-2, p0 is in t0, p2 is in a0. The goal is to reach a state where the following facts hold: p0 is at l0-1, p1 is at l0-1, p2 is at l0-1, and p3 is at l0-1. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p1 t1)", "(at p1 l0-0)", "(at p2 l1-2)", "(at p1 l1-2)", "(at t1 l1-0)", "(in p1 a0)", "(at p0 l1-1)", "(at t1 l1-1)", "(at p0 l1-0)", "(at p3 l0-0)", "(in p0 t1)", "(at p1 l1-0)", "(in p3 t1)", "(at p0 l0-0)", "(in p2 t1)", "(at p3 l1-2)", "(in p3 a0)", "(at p1 l1-1)", "(at p0 l1-2)", "(at p2 l1-1)", "(at p3 l1-0)", "(at a0 l1-0)", "(at p2 l0-2)", "(at p2 l1-0)", "(at p0 l0-2)", "(at p3 l1-1)", "(in p0 a0)"], "yes": ["(in p3 t0)", "(at t0 l0-1)", "(in p2 t0)", "(at p2 l0-0)", "(in p1 t0)", "(at t0 l0-2)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p1 l0-2) (at p3 l0-2) (at t0 l0-0) (at t1 l1-2) (in p0 t0) (in p2 a0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-1) (at p1 l0-1) (at p2 l0-1) (at p3 l0-1)))\n)"}
{"id": 6664303548037751194, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1; l2-1, l2-0, and l2-2 are in c2. Currently, a0, p4, and t1 are at l1-0, t2 is at l2-1, t0 is at l0-2, p3 and p1 are in a0, p0 is in t1, p2 is in t0. The goal is to reach a state where the following facts hold: p3 is at l1-2, p2 is at l1-2, p4 is at l0-0, p1 is at l1-0, and p0 is at l1-2. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p1 t1)", "(at p0 l2-2)", "(at p3 l2-1)", "(at p1 l0-0)", "(at p4 l2-0)", "(at p3 l0-2)", "(in p1 t0)", "(at t0 l0-1)", "(in p2 t2)", "(at p4 l1-1)", "(at p1 l1-2)", "(at p0 l2-1)", "(at p3 l2-0)", "(in p4 t0)", "(at p0 l1-1)", "(at t1 l1-1)", "(at p1 l2-1)", "(at p0 l1-0)", "(at p3 l0-0)", "(at p2 l2-0)", "(at p4 l0-2)", "(at p1 l2-0)", "(at t2 l2-2)", "(at p1 l2-2)", "(at p0 l0-1)", "(at p4 l2-1)", "(at p2 l2-2)", "(at p0 l0-0)", "(at p3 l0-1)", "(at p1 l0-2)", "(in p3 t2)", "(at p4 l0-1)", "(at a0 l2-0)", "(at p2 l0-1)", "(at p4 l1-2)", "(at p1 l0-1)", "(at p4 l2-2)", "(in p0 t2)", "(at p1 l1-1)", "(at p2 l1-1)", "(at p2 l0-2)", "(in p3 t0)", "(at p0 l2-0)", "(in p0 t0)", "(in p1 t2)", "(at p2 l2-1)", "(in p4 t1)", "(at p0 l0-2)", "(at p3 l1-1)", "(in p4 t2)", "(at t2 l2-0)", "(in p0 a0)", "(at p3 l2-2)"], "yes": ["(in p4 a0)", "(at a0 l0-0)", "(in p3 t1)", "(at t1 l1-2)", "(at p3 l1-0)", "(in p2 t1)", "(at p2 l0-0)", "(at p2 l1-0)", "(in p2 a0)", "(at t0 l0-0)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c3-s3-p5-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 - airport c0 c1 c2 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 - location p0 p1 p2 p3 p4 - package t0 t1 t2 - truck)\n    (:init (at a0 l1-0) (at p4 l1-0) (at t0 l0-2) (at t1 l1-0) (at t2 l2-1) (in p0 t1) (in p1 a0) (in p2 t0) (in p3 a0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2))\n    (:goal (and (at p0 l1-2) (at p1 l1-0) (at p2 l1-2) (at p3 l1-2) (at p4 l0-0)))\n)"}
{"id": 4515430472964367099, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l2-7, l2-1, l2-9, l2-3, l2-5, l2-0, l2-6, l2-8, l2-2, and l2-4 are in c2; l4-0, l4-2, l4-4, l4-6, l4-1, l4-8, l4-9, l4-5, l4-3, and l4-7 are in c4; l0-1, l0-6, l0-4, l0-0, l0-7, l0-3, l0-2, l0-5, l0-8, and l0-9 are in c0; l1-8, l1-6, l1-7, l1-4, l1-3, l1-5, l1-0, l1-1, l1-2, and l1-9 are in c1; l3-7, l3-2, l3-1, l3-6, l3-3, l3-4, l3-5, l3-8, l3-9, and l3-0 are in c3. Currently, p0, a0, t2, and p3 are at l2-0, t4 and p2 are at l4-8, t0 is at l0-2, t1 and p1 are at l1-0, t3 is at l3-0. The goal is to reach a state where the following facts hold: p1 is at l2-7, p3 is at l1-1, p0 is at l1-5, and p2 is at l4-8. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p0 t3)", "(at p3 l4-8)", "(at p1 l0-0)", "(at p2 l4-4)", "(in p2 t0)", "(at p3 l2-3)", "(at p0 l1-9)", "(at p0 l2-7)", "(at p2 l3-0)", "(at p2 l0-3)", "(at t3 l3-6)", "(at t0 l0-7)", "(at p2 l2-0)", "(at p0 l2-9)", "(at p0 l4-9)", "(at p3 l1-9)", "(in p2 t1)", "(at p0 l3-9)", "(at p1 l4-3)", "(at p2 l3-1)", "(at p1 l2-3)", "(at p2 l0-1)", "(at p3 l1-4)", "(at p1 l0-4)", "(at p3 l2-5)", "(at p0 l0-5)", "(at p0 l0-2)", "(at t4 l4-0)", "(at p3 l2-8)", "(at p3 l2-1)", "(at p1 l3-1)", "(at t2 l2-5)", "(at p3 l4-1)", "(in p0 t4)", "(at p3 l1-8)", "(at p0 l4-5)", "(at p2 l0-5)", "(at t3 l3-3)", "(at t2 l2-3)", "(at p2 l4-0)", "(at p3 l4-4)", "(at p2 l1-8)", "(at p3 l3-7)", "(at t2 l2-4)", "(at p1 l3-2)", "(at t0 l0-3)", "(at p2 l2-9)", "(at p3 l4-9)", "(at p3 l0-1)", "(at p0 l3-5)", "(at a0 l4-0)", "(at p0 l2-5)", "(at p0 l3-7)", "(at p0 l0-4)", "(at t3 l3-8)", "(in p0 t0)", "(at t4 l4-7)", "(at t4 l4-4)", "(at t1 l1-4)", "(at p2 l4-7)", "(at t0 l0-8)", "(at p3 l2-6)", "(in p1 t0)", "(at t1 l1-2)", "(at p3 l3-6)", "(at p0 l2-1)", "(at p0 l1-7)", "(at t3 l3-5)", "(at p1 l3-8)", "(at p1 l4-8)", "(at t2 l2-9)", "(at p1 l1-5)", "(at p0 l0-0)", "(at p0 l1-4)", "(at t4 l4-6)", "(at p0 l4-7)", "(at p1 l3-5)", "(at p3 l0-9)", "(at p3 l0-8)", "(at p3 l1-2)", "(at p1 l0-3)", "(at p3 l4-2)", "(at p2 l1-5)", "(at p3 l3-5)", "(at p0 l3-4)", "(at p2 l2-4)", "(at p1 l3-9)", "(at p2 l4-3)", "(at p3 l3-4)", "(at p1 l1-1)", "(at p2 l3-3)", "(at p1 l0-9)", "(at t4 l4-2)", "(at p2 l0-0)", "(at p2 l4-1)", "(at p0 l0-6)", "(at p2 l1-6)", "(at p0 l2-2)", "(at p1 l4-6)", "(at p2 l3-2)", "(at p0 l2-8)", "(at p0 l3-6)", "(at t4 l4-1)", "(at p1 l0-6)", "(at t0 l0-5)", "(at p1 l1-9)", "(at t0 l0-9)", "(at p3 l1-3)", "(at p1 l1-7)", "(at p3 l2-4)", "(at p1 l4-0)", "(in p3 t4)", "(at p3 l4-0)", "(at p1 l1-6)", "(at p2 l1-1)", "(at p3 l3-8)", "(at p0 l3-1)", "(in p3 t0)", "(at p2 l1-0)", "(at t1 l1-9)", "(at p1 l4-4)", "(at p1 l4-9)", "(at p0 l4-8)", "(at p3 l0-7)", "(at t4 l4-5)", "(at p2 l1-7)", "(at p1 l1-4)", "(at t1 l1-7)", "(at p1 l2-8)", "(at t4 l4-9)", "(at p0 l4-2)", "(at p2 l2-3)", "(at p1 l4-7)", "(at p1 l3-6)", "(at p1 l2-6)", "(in p3 t2)", "(at p3 l3-9)", "(at p3 l4-3)", "(at t3 l3-9)", "(at p3 l1-7)", "(at p1 l0-1)", "(at p3 l2-9)", "(at p0 l1-2)", "(at p2 l0-9)", "(at p3 l0-6)", "(at p2 l0-2)", "(at p3 l0-3)", "(at p1 l0-8)", "(at p1 l1-3)", "(at p2 l1-4)", "(at p0 l3-8)", "(at p3 l0-2)", "(at p2 l1-2)", "(at p0 l0-9)", "(at t2 l2-8)", "(at p1 l2-9)", "(at p1 l1-2)", "(at t1 l1-3)", "(at p3 l0-5)", "(at p3 l3-1)", "(at p1 l2-1)", "(at t0 l0-0)", "(at t3 l3-1)", "(at p3 l0-0)", "(at p2 l0-7)", "(at p1 l3-3)", "(at p2 l4-9)", "(at t2 l2-2)", "(in p2 t3)", "(at p3 l3-0)", "(at p0 l1-6)", "(at p1 l0-2)", "(at a0 l3-0)", "(at a0 l0-0)", "(at p1 l2-4)", "(in p0 t2)", "(in p3 t3)", "(at p3 l4-5)", "(at p2 l4-5)", "(at p0 l2-6)", "(at t0 l0-6)", "(at p3 l1-6)", "(at p2 l2-6)", "(at p3 l4-6)", "(at p2 l3-7)", "(at p1 l4-2)", "(in p2 a0)", "(at p1 l4-1)", "(in p1 t1)", "(at p0 l3-3)", "(at p2 l3-9)", "(at p2 l4-2)", "(in p2 t4)", "(at p0 l4-1)", "(at t2 l2-6)", "(at p0 l1-3)", "(at p0 l3-0)", "(at t2 l2-1)", "(at p2 l3-5)", "(at p2 l4-6)", "(at p1 l2-2)", "(at p0 l0-1)", "(at t1 l1-8)", "(at p2 l2-2)", "(at p2 l0-4)", "(at p2 l0-6)", "(at t4 l4-3)", "(at t3 l3-2)", "(at p0 l4-3)", "(at p2 l1-3)", "(at p0 l1-8)", "(at p2 l2-7)", "(at p2 l3-8)", "(at p3 l2-7)", "(at p1 l1-8)", "(at p0 l3-2)", "(at p1 l0-7)", "(at p1 l3-4)", "(at p2 l2-1)", "(at p2 l2-8)", "(at p0 l2-3)", "(at p3 l2-2)", "(at p0 l4-4)", "(at t0 l0-1)", "(at p0 l0-3)", "(in p2 t2)", "(at p3 l4-7)", "(at p2 l2-5)", "(at p3 l0-4)", "(at t0 l0-4)", "(at p0 l1-1)", "(at p2 l1-9)", "(in p1 t3)", "(at t1 l1-6)", "(at p1 l0-5)", "(at p3 l3-2)", "(at t3 l3-4)", "(at p0 l0-8)", "(at p2 l3-4)", "(at p3 l1-5)", "(at p3 l3-3)", "(in p1 t4)", "(at t3 l3-7)", "(at p0 l2-4)", "(at p1 l3-0)", "(at p1 l3-7)", "(at p0 l0-7)", "(at p1 l4-5)", "(at p0 l4-0)", "(at p2 l0-8)", "(at p2 l3-6)", "(at p0 l4-6)", "(at p1 l2-5)"], "yes": ["(in p3 t1)", "(at t1 l1-1)", "(at p3 l1-0)", "(in p3 a0)", "(in p1 t2)", "(at t2 l2-7)", "(at p1 l2-0)", "(in p1 a0)", "(in p0 t1)", "(at t1 l1-5)", "(at p0 l1-0)", "(in p0 a0)", "(at a0 l1-0)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s10-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l0-3 l0-4 l0-5 l0-6 l0-7 l0-8 l0-9 l1-1 l1-2 l1-3 l1-4 l1-5 l1-6 l1-7 l1-8 l1-9 l2-1 l2-2 l2-3 l2-4 l2-5 l2-6 l2-7 l2-8 l2-9 l3-1 l3-2 l3-3 l3-4 l3-5 l3-6 l3-7 l3-8 l3-9 l4-1 l4-2 l4-3 l4-4 l4-5 l4-6 l4-7 l4-8 l4-9 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l2-0) (at p0 l2-0) (at p1 l1-0) (at p2 l4-8) (at p3 l2-0) (at t0 l0-2) (at t1 l1-0) (at t2 l2-0) (at t3 l3-0) (at t4 l4-8) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l0-3 c0) (in-city l0-4 c0) (in-city l0-5 c0) (in-city l0-6 c0) (in-city l0-7 c0) (in-city l0-8 c0) (in-city l0-9 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l1-3 c1) (in-city l1-4 c1) (in-city l1-5 c1) (in-city l1-6 c1) (in-city l1-7 c1) (in-city l1-8 c1) (in-city l1-9 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l2-3 c2) (in-city l2-4 c2) (in-city l2-5 c2) (in-city l2-6 c2) (in-city l2-7 c2) (in-city l2-8 c2) (in-city l2-9 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l3-3 c3) (in-city l3-4 c3) (in-city l3-5 c3) (in-city l3-6 c3) (in-city l3-7 c3) (in-city l3-8 c3) (in-city l3-9 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4) (in-city l4-3 c4) (in-city l4-4 c4) (in-city l4-5 c4) (in-city l4-6 c4) (in-city l4-7 c4) (in-city l4-8 c4) (in-city l4-9 c4))\n    (:goal (and (at p0 l1-5) (at p1 l2-7) (at p2 l4-8) (at p3 l1-1)))\n)"}
{"id": -5491820835845176450, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, t1 and p0 are at l1-1, a0 is at l1-0, t0 is at l0-0, p1 and p3 are in t1, p2 is in t0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p3 is at l1-1, p2 is at l1-0, and p0 is at l1-1. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at p1 l0-0)", "(in p1 t0)", "(at t0 l0-1)", "(at t1 l1-0)", "(in p1 a0)", "(at p0 l1-0)", "(at p3 l0-0)", "(in p0 t1)", "(at p0 l0-1)", "(at p1 l1-0)", "(at p0 l0-0)", "(in p2 t1)", "(at p3 l0-1)", "(in p3 a0)", "(at p2 l0-1)", "(at p1 l0-1)", "(at p3 l1-0)", "(at p2 l1-1)", "(in p3 t0)", "(in p0 t0)", "(in p0 a0)"], "yes": ["(in p2 a0)", "(at p2 l0-0)", "(at a0 l0-0)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s2-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l1-1 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p0 l1-1) (at t0 l0-0) (at t1 l1-1) (in p1 t1) (in p2 t0) (in p3 t1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l1-0 c1) (in-city l1-1 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-1)))\n)"}
{"id": -8473160474630250651, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l4-1, l4-0, and l4-2 are in c4; l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1; l2-1, l2-0, and l2-2 are in c2; l3-1, l3-2, and l3-0 are in c3. Currently, a0, p0, and t3 are at l3-0, p1 is at l3-1, t0 is at l0-2, t1 is at l1-2, t2 is at l2-0, t4 is at l4-0, p2 is in t2, p3 is in t3. The goal is to reach a state where the following facts hold: p2 is at l2-2, p1 is at l3-2, p0 is at l3-0, and p3 is at l3-2. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p1 t1)", "(in p0 t3)", "(at p1 l0-0)", "(in p1 t0)", "(at p2 l4-2)", "(in p2 t4)", "(in p2 t0)", "(at t1 l1-0)", "(at p0 l2-1)", "(at p2 l3-0)", "(at p0 l4-1)", "(at t2 l2-1)", "(at p2 l2-0)", "(at p0 l4-2)", "(at p1 l2-2)", "(at p0 l0-1)", "(in p2 t1)", "(at p0 l0-0)", "(at p3 l1-2)", "(in p3 t2)", "(in p3 a0)", "(at p2 l3-1)", "(at p3 l4-2)", "(at p2 l0-1)", "(at p1 l0-1)", "(at p1 l1-1)", "(at p0 l1-2)", "(at t4 l4-2)", "(at p0 l3-2)", "(at p2 l0-2)", "(at p0 l2-0)", "(in p1 t2)", "(at p2 l2-1)", "(at p0 l0-2)", "(in p0 a0)", "(at p2 l0-0)", "(at p2 l4-1)", "(at p3 l2-2)", "(at p2 l3-2)", "(at p0 l2-2)", "(at p3 l2-1)", "(at t0 l0-1)", "(at p3 l0-2)", "(at p2 l1-2)", "(at p1 l1-2)", "(at p3 l4-1)", "(in p0 t4)", "(at t4 l4-1)", "(at p3 l3-1)", "(in p1 a0)", "(at p3 l2-0)", "(at p0 l1-1)", "(at p2 l4-0)", "(at t1 l1-1)", "(at p1 l2-1)", "(at p0 l1-0)", "(at t0 l0-0)", "(at p3 l0-0)", "(in p0 t1)", "(at p1 l2-0)", "(in p3 t1)", "(at p1 l1-0)", "(in p2 t3)", "(at p3 l3-0)", "(at p3 l0-1)", "(at p1 l0-2)", "(at a0 l0-0)", "(at a0 l2-0)", "(at p1 l4-0)", "(in p3 t4)", "(at a0 l4-0)", "(at p3 l4-0)", "(in p1 t4)", "(in p0 t2)", "(at p3 l1-0)", "(at p2 l1-1)", "(at p1 l3-0)", "(at a0 l1-0)", "(at p0 l3-1)", "(in p3 t0)", "(in p0 t0)", "(at p2 l1-0)", "(at p0 l4-0)", "(at p3 l1-1)", "(at p1 l4-2)", "(in p2 a0)", "(at p1 l4-1)"], "yes": ["(at t3 l3-2)", "(at t2 l2-2)", "(in p1 t3)", "(at t3 l3-1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c5-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 l3-0 l4-0 - airport c0 c1 c2 c3 c4 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 l3-1 l3-2 l4-1 l4-2 - location p0 p1 p2 p3 - package t0 t1 t2 t3 t4 - truck)\n    (:init (at a0 l3-0) (at p0 l3-0) (at p1 l3-1) (at t0 l0-2) (at t1 l1-2) (at t2 l2-0) (at t3 l3-0) (at t4 l4-0) (in p2 t2) (in p3 t3) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2) (in-city l3-0 c3) (in-city l3-1 c3) (in-city l3-2 c3) (in-city l4-0 c4) (in-city l4-1 c4) (in-city l4-2 c4))\n    (:goal (and (at p0 l3-0) (at p1 l3-2) (at p2 l2-2) (at p3 l3-2)))\n)"}
{"id": -6896839021435593768, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1. \nCurrently, a0 is at l1-0, t0 is at l0-0, p2 is at l0-1, t1 is at l1-2, p1 is at l1-1, p3 is in t0, p0 is in a0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p3 is at l1-2, p2 is at l1-0, and p0 is at l1-1. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p1 t1)", "(at p1 l0-0)", "(in p1 t0)", "(at p2 l1-2)", "(at p3 l0-2)", "(at p1 l1-2)", "(in p1 a0)", "(at p0 l0-1)", "(at p1 l1-0)", "(at p0 l0-0)", "(in p2 t1)", "(at p1 l0-2)", "(at p3 l0-1)", "(at p1 l0-1)", "(at p2 l1-1)", "(at p0 l1-2)", "(at p2 l0-2)", "(in p0 t0)", "(at t0 l0-2)", "(at p0 l0-2)", "(at p3 l1-1)"], "yes": ["(in p3 t1)", "(at p3 l0-0)", "(at p3 l1-0)", "(in p3 a0)", "(in p2 a0)", "(at p2 l0-0)", "(in p2 t0)", "(in p0 t1)", "(at t1 l1-1)", "(at p0 l1-0)", "(at t1 l1-0)", "(at a0 l0-0)", "(at t0 l0-1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p1 l1-1) (at p2 l0-1) (at t0 l0-0) (at t1 l1-2) (in p0 a0) (in p3 t0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2)))\n)"}
{"id": 1550100744096115788, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1. \nCurrently, a0 and t0 are at l0-0, t1 is at l1-0, p1 is at l1-1, p3 and p0 are in a0, p2 is in t0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p3 is at l1-2, p2 is at l1-0, and p0 is at l1-1. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p1 t1)", "(at p1 l0-0)", "(in p1 t0)", "(at p2 l1-2)", "(at p3 l0-2)", "(at t0 l0-1)", "(at p1 l1-2)", "(in p1 a0)", "(at p3 l0-0)", "(at p0 l0-1)", "(at p1 l1-0)", "(at p0 l0-0)", "(in p2 t1)", "(at p1 l0-2)", "(at p3 l0-1)", "(at p2 l0-1)", "(at p1 l0-1)", "(at p2 l1-1)", "(at p0 l1-2)", "(at p2 l0-2)", "(in p3 t0)", "(in p0 t0)", "(at t0 l0-2)", "(at p0 l0-2)", "(at p3 l1-1)"], "yes": ["(in p3 t1)", "(at t1 l1-2)", "(at p3 l1-0)", "(in p2 a0)", "(at a0 l1-0)", "(at p2 l0-0)", "(in p0 t1)", "(at t1 l1-1)", "(at p0 l1-0)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p1 l1-1) (at t0 l0-0) (at t1 l1-0) (in p0 a0) (in p2 t0) (in p3 a0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2)))\n)"}
{"id": 7447525980482188540, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 3 trucks and 1 airplane, as well as 4 packages. There are 9 locations across 3 cities. \nThe locations are in cities as follows: l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1; l2-1, l2-0, and l2-2 are in c2. \nCurrently, a0, p1, and t2 are at l2-0, t0 is at l0-0, t1 and p3 are at l1-0, p0 is in t2, p2 is in a0. The goal is to reach a state where the following facts hold: p2 is at l2-2, p1 is at l2-0, p0 is at l2-1, and p3 is at l1-2. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p1 t1)", "(at p0 l2-2)", "(at p3 l2-1)", "(at p1 l0-0)", "(in p1 t0)", "(at p2 l1-2)", "(at p3 l0-2)", "(at t0 l0-1)", "(at p1 l1-2)", "(in p2 t0)", "(in p1 a0)", "(at p3 l2-0)", "(at p0 l1-1)", "(at t1 l1-1)", "(at p1 l2-1)", "(at p0 l1-0)", "(at p3 l0-0)", "(in p0 t1)", "(at p0 l0-1)", "(at p1 l2-2)", "(at p1 l1-0)", "(in p2 t1)", "(at p0 l0-0)", "(at p3 l0-1)", "(at p1 l0-2)", "(in p3 t2)", "(in p3 a0)", "(at a0 l0-0)", "(at p2 l0-1)", "(at p1 l0-1)", "(at p1 l1-1)", "(at p0 l1-2)", "(at p2 l1-1)", "(at a0 l1-0)", "(at p2 l0-2)", "(at p0 l2-0)", "(at p2 l1-0)", "(in p0 t0)", "(in p3 t0)", "(in p1 t2)", "(at p2 l2-1)", "(at t0 l0-2)", "(at p0 l0-2)", "(at p3 l1-1)", "(in p0 a0)", "(at p2 l0-0)", "(at p3 l2-2)"], "yes": ["(in p3 t1)", "(at t1 l1-2)", "(in p2 t2)", "(at t2 l2-2)", "(at p2 l2-0)", "(at t2 l2-1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c3-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 - airport c0 c1 c2 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 - location p0 p1 p2 p3 - package t0 t1 t2 - truck)\n    (:init (at a0 l2-0) (at p1 l2-0) (at p3 l1-0) (at t0 l0-0) (at t1 l1-0) (at t2 l2-0) (in p0 t2) (in p2 a0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2))\n    (:goal (and (at p0 l2-1) (at p1 l2-0) (at p2 l2-2) (at p3 l1-2)))\n)"}
{"id": -3000776064842292921, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1. \nCurrently, a0 is at l1-0, p3 and t0 are at l0-2, p2 is at l0-1, t1 is at l1-2, p1 is at l1-1, p0 is at l0-0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p3 is at l1-2, p2 is at l1-0, and p0 is at l1-1. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p1 t1)", "(at p2 l0-2)", "(at p1 l1-0)", "(in p0 t0)", "(at p1 l0-0)", "(in p1 t0)", "(at p2 l1-2)", "(at p2 l1-1)", "(at p1 l1-2)", "(at p1 l0-2)", "(at p0 l0-2)", "(at p0 l0-1)", "(at p1 l0-1)", "(at p3 l1-1)", "(in p1 a0)", "(in p2 t1)", "(at p0 l1-2)", "(at p3 l0-1)"], "yes": ["(in p3 t1)", "(at p3 l0-0)", "(at p3 l1-0)", "(in p3 a0)", "(in p3 t0)", "(in p2 a0)", "(at p2 l0-0)", "(in p2 t0)", "(in p0 t1)", "(at t1 l1-1)", "(at p0 l1-0)", "(in p0 a0)", "(at t1 l1-0)", "(at t0 l0-0)", "(at a0 l0-0)", "(at t0 l0-1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p0 l0-0) (at p1 l1-1) (at p2 l0-1) (at p3 l0-2) (at t0 l0-2) (at t1 l1-2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2)))\n)"}
{"id": 4264614740131260175, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1. \nCurrently, a0 is at l0-0, t0 and p3 are at l0-1, p0 is at l0-2, t1 is at l1-0, p1 and p2 are in t1. The goal is to reach a state where the following facts hold: p1 is at l1-2, p0 is at l0-2, p2 is at l1-2, and p3 is at l0-1. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at p1 l0-0)", "(in p1 t0)", "(at p3 l0-2)", "(in p2 t0)", "(in p1 a0)", "(at p0 l1-1)", "(at t1 l1-1)", "(at p0 l1-0)", "(at t0 l0-0)", "(at p3 l0-0)", "(in p0 t1)", "(at p0 l0-1)", "(in p3 t1)", "(at p1 l1-0)", "(at p0 l0-0)", "(at p1 l0-2)", "(at p3 l1-2)", "(in p3 a0)", "(at p2 l0-1)", "(at p1 l0-1)", "(at p1 l1-1)", "(at p0 l1-2)", "(at p2 l1-1)", "(at p3 l1-0)", "(at a0 l1-0)", "(at p2 l0-2)", "(at p2 l1-0)", "(in p0 t0)", "(in p3 t0)", "(at t0 l0-2)", "(at p3 l1-1)", "(in p0 a0)", "(in p2 a0)", "(at p2 l0-0)"], "yes": ["(at t1 l1-2)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l0-2) (at p3 l0-1) (at t0 l0-1) (at t1 l1-0) (in p1 t1) (in p2 t1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-2) (at p1 l1-2) (at p2 l1-2) (at p3 l0-1)))\n)"}
