{"id": 4821718203476065346, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 7 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 and camera1 on board. Rover rover1 has camera2 on board. Camera camera1 can be calibrated on objective1. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective4. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective6 is visible from waypoint1 and waypoint0. Objective objective4 is visible from waypoint1. Objective objective5 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint1. Objective objective3 is visible from waypoint2. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0 and waypoint2. Rovers rover1 and rover0 are available. Rock data was communicated from waypoint waypoint1; Soil data were communicated from the following waypoints: waypoint2, waypoint1. Image objective0 was communicated in mode low_res. Image objective3 was communicated in mode high_res. Rover rover0 has soil analyzed in waypoint waypoint2. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode colour. Rover rover0 has image objective0 in mode low_res. Rover rover1 has image objective3 in mode high_res. Store(s) store0 and store1 are full. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint0;, Image objective6 was communicated in mode low_res, Soil data was communicated from waypoint waypoint2;, Image objective3 was communicated in mode high_res, Image objective4 was communicated in mode low_res, Image objective0 was communicated in mode low_res, Soil data was communicated from waypoint waypoint1;, Image objective0 was communicated in mode colour, Rock data was communicated from waypoint waypoint2;, and Rock data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Image objective5 was communicated in mode high_res. B. Rover rover1 has image objective1 in mode colour. C. Rover rover1 has rock analyzed in waypoint waypoint2. D. Image objective3 was communicated in mode colour.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Image objective5 was communicated in mode high_res", "Rover rover1 has image objective1 in mode colour", "Rover rover1 has rock analyzed in waypoint waypoint2", "Image objective3 was communicated in mode colour"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -4419243224709509874, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 on board. Rover rover1 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint4, waypoint0 to waypoint1, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint3, waypoint2, waypoint1, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint1 and waypoint2. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint4. Rocks can be sampled at the following location(s): waypoint0 and waypoint1. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode low_res. Rover rover1 has its camera camera1 calibrated. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode low_res, Image objective0 was communicated in mode colour, and Rock data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover0 has image objective0 in mode low_res. B. Rover rover1 is at waypoint0. C. Rover rover0 has image objective0 in mode colour. D. Rover rover0 has its camera camera0 calibrated.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 has image objective0 in mode low_res", "Rover rover1 is at waypoint0", "Rover rover0 has image objective0 in mode colour", "Rover rover0 has its camera camera0 calibrated"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 3500558693611047165, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 on board. Rover rover1 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint4, waypoint0 to waypoint1, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint3, waypoint2, waypoint1, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint1 and waypoint2. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint4. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Image objective0 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Rover rover1 has image objective0 in mode colour. Store(s) store1 and store0 are full. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode low_res, Image objective0 was communicated in mode colour, and Rock data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover0 is at waypoint1. B. Rocks can be sampled at the following location(s): waypoint0. C. Store(s) store1 is empty. D. Rocks can be sampled at the following location(s): waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 is at waypoint1", "Rocks can be sampled at the following location(s): waypoint0", "Store(s) store1 is empty", "Rocks can be sampled at the following location(s): waypoint1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 6142717132055529809, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Soil data was communicated from waypoint waypoint1; Image objective1 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Store(s) store1 is empty. Store(s) store0 is full. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint1;, Image objective0 was communicated in mode low_res, and Soil data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover1 has image objective1 in mode low_res. B. Rover rover1 is at waypoint1. C. Image objective0 was communicated in mode low_res. D. Rover rover0 has image objective0 in mode high_res.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 has image objective1 in mode low_res", "Rover rover1 is at waypoint1", "Image objective0 was communicated in mode low_res", "Rover rover0 has image objective0 in mode high_res"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 7367733930913896581, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 and camera1 on board. Rover rover1 has camera2 on board. Camera camera2 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective2 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint2. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode colour. Store(s) store0 is empty. Store(s) store1 is full. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, and Image objective1 was communicated in mode colour.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover1 has image objective2 in mode high_res. B. Rover rover1 has image objective1 in mode high_res. C. Image objective1 was communicated in mode colour. D. Soil can be sampled at the following location(s): waypoint0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 has image objective2 in mode high_res", "Rover rover1 has image objective1 in mode high_res", "Image objective1 was communicated in mode colour", "Soil can be sampled at the following location(s): waypoint0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -3610328923271677066, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint1. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover1 has its camera camera0 calibrated. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint1;, Image objective0 was communicated in mode low_res, and Soil data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Image objective1 was communicated in mode high_res. B. Rover rover1 is at waypoint0. C. Rock data was communicated from waypoint waypoint1; . D. Rover rover0 has image objective0 in mode high_res.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Image objective1 was communicated in mode high_res", "Rover rover1 is at waypoint0", "Rock data was communicated from waypoint waypoint1; ", "Rover rover0 has image objective0 in mode high_res"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 6470264818412767253, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 on board. Rover rover1 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint4, waypoint0 to waypoint1, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint3, waypoint2, waypoint1, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint1 and waypoint2. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint4. Rocks can be sampled at the following location(s): waypoint0 and waypoint1. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode low_res. Rover rover1 has its camera camera1 calibrated. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode low_res, Image objective0 was communicated in mode colour, and Rock data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover0 has image objective0 in mode colour. B. Rover rover0 has its camera camera0 calibrated. C. Rover rover1 is at waypoint2. D. Rover rover1 has image objective0 in mode low_res.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 has image objective0 in mode colour", "Rover rover0 has its camera camera0 calibrated", "Rover rover1 is at waypoint2", "Rover rover1 has image objective0 in mode low_res"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -7946745159529043314, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 7 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 and camera1 on board. Rover rover1 has camera2 on board. Camera camera1 can be calibrated on objective1. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective4. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective6 is visible from waypoint1 and waypoint0. Objective objective4 is visible from waypoint1. Objective objective5 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint1. Objective objective3 is visible from waypoint2. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint2 and waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover1 and rover0 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint2; Image objective3 was communicated in mode high_res. Image objective0 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint2. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective3 in mode high_res. Rover rover1 has image objective0 in mode colour. Rover rover0 has its camera camera1 calibrated. Store(s) store0 and store1 are full. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint0;, Image objective6 was communicated in mode low_res, Soil data was communicated from waypoint waypoint2;, Image objective3 was communicated in mode high_res, Image objective4 was communicated in mode low_res, Image objective0 was communicated in mode low_res, Soil data was communicated from waypoint waypoint1;, Image objective0 was communicated in mode colour, Rock data was communicated from waypoint waypoint2;, and Rock data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Image objective6 was communicated in mode high_res. B. Image objective5 was communicated in mode high_res. C. Rover rover0 has image objective0 in mode low_res. D. Rover rover0 has image objective3 in mode low_res.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Image objective6 was communicated in mode high_res", "Image objective5 was communicated in mode high_res", "Rover rover0 has image objective0 in mode low_res", "Rover rover0 has image objective3 in mode low_res"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": 8599727145682852999, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 on board. Rover rover1 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint4, waypoint0 to waypoint1, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2, waypoint4, and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint3: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint3, waypoint2, waypoint1, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint1 and waypoint2. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint1. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Image objective0 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Rover rover1 has its camera camera1 calibrated. Store(s) store1 and store0 are full. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode low_res, Image objective0 was communicated in mode low_res, Image objective0 was communicated in mode colour, and Rock data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover0 has its camera camera0 calibrated. B. Rover rover1 is at waypoint0. C. Image objective1 was communicated in mode colour. D. Rover rover0 is at waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 has its camera camera0 calibrated", "Rover rover1 is at waypoint0", "Image objective1 was communicated in mode colour", "Rover rover0 is at waypoint1"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 1670320605776143986, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports high_res and low_res and colour. Camera camera2 supports colour and low_res. Camera camera1 supports colour. Rover rover0 can traverse from waypoint2 to waypoint5, waypoint3 to waypoint0, waypoint6 to waypoint5, waypoint5 to waypoint0, waypoint0 to waypoint5, waypoint5 to waypoint2, waypoint1 to waypoint5, waypoint5 to waypoint1, waypoint5 to waypoint4, waypoint0 to waypoint3, waypoint4 to waypoint5, waypoint5 to waypoint6. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint3 to waypoint0, waypoint5 to waypoint0, waypoint2 to waypoint0, waypoint1 to waypoint6, waypoint4 to waypoint2, waypoint0 to waypoint6, waypoint6 to waypoint0, waypoint0 to waypoint5, waypoint0 to waypoint3, waypoint2 to waypoint4, waypoint6 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint4, waypoint3, waypoint5, waypoint2, and waypoint6. Waypoint(s) are visible from waypoint2: waypoint3, waypoint5, waypoint1, waypoint0, and waypoint4. Waypoint(s) are visible from waypoint6: waypoint1, waypoint0, waypoint4, waypoint3, and waypoint5. Waypoint(s) are visible from waypoint0: waypoint6, waypoint3, waypoint5, and waypoint2. Waypoint(s) are visible from waypoint3: waypoint6, waypoint1, waypoint0, waypoint4, waypoint5, and waypoint2. Waypoint(s) are visible from waypoint5: waypoint1, waypoint0, waypoint4, waypoint3, waypoint2, and waypoint6. Waypoint(s) are visible from waypoint4: waypoint3, waypoint5, waypoint2, waypoint6, and waypoint1. Objective objective1 is visible from waypoint4, waypoint2, waypoint5, and waypoint0. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint5. Rocks can be sampled at the following location(s): waypoint3, waypoint6, waypoint2, and waypoint4. Soil can be sampled at the following location(s): waypoint3, waypoint6, waypoint1, and waypoint4. Rovers rover1 and rover0 are available. Rover rover0 has rock analyzed in waypoint waypoint5. Rover rover1 has image objective0 in mode high_res. Store(s) store1 is empty. Store(s) store0 is full. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Image objective1 was communicated in mode high_res, Rock data was communicated from waypoint waypoint3;, Image objective0 was communicated in mode high_res, Rock data was communicated from waypoint waypoint4;, Image objective1 was communicated in mode colour, and Rock data was communicated from waypoint waypoint5;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover1 has its camera camera1 calibrated. B. Rover rover0 has rock analyzed in waypoint waypoint2. C. Rover rover0 is at waypoint0. D. Rover rover1 has its camera camera0 calibrated.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 has its camera camera1 calibrated", "Rover rover0 has rock analyzed in waypoint waypoint2", "Rover rover0 is at waypoint0", "Rover rover1 has its camera camera0 calibrated"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 1672726117546815303, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera0 and camera1 on board. Rover rover1 has camera2 on board. Camera camera2 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Image objective0 was communicated in mode low_res. Rover rover0 has image objective0 in mode low_res. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint1;, Image objective0 was communicated in mode low_res, Image objective1 was communicated in mode colour, and Soil data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover0 is at waypoint1. B. Rover rover0 has image objective1 in mode colour. C. Rover rover1 has rock analyzed in waypoint waypoint1. D. Rover rover0 is at waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 is at waypoint1", "Rover rover0 has image objective1 in mode colour", "Rover rover1 has rock analyzed in waypoint waypoint1", "Rover rover0 is at waypoint2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "C"}
{"id": -4890825063765272546, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera1 can be calibrated on objective1. Camera camera0 can be calibrated on objective2. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective2 is visible from waypoint1. Objective objective1 is visible from waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint0 and waypoint2. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Image objective1 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint2. Rover rover0 has image objective1 in mode high_res. Rover rover0 has image objective1 in mode low_res. Rover rover0 has its camera camera1 calibrated. Store(s) store1 is empty. Store(s) store0 is full. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint2;, Image objective1 was communicated in mode low_res, Image objective1 was communicated in mode high_res, Rock data was communicated from waypoint waypoint2;, and Image objective0 was communicated in mode low_res.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover1 has its camera camera0 calibrated. B. Rover rover0 has soil analyzed in waypoint waypoint0. C. Rover rover0 is at waypoint2. D. Rover rover0 has image objective0 in mode high_res.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 has its camera camera0 calibrated", "Rover rover0 has soil analyzed in waypoint waypoint0", "Rover rover0 is at waypoint2", "Rover rover0 has image objective0 in mode high_res"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -483543385770336342, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective2. Camera camera0 can be calibrated on objective0. Camera camera1 supports high_res. Camera camera2 supports high_res. Camera camera0 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0, waypoint0 to waypoint1. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint0. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint0; Image objective1 was communicated in mode low_res. Rover rover1 has soil analyzed in waypoint waypoint0. Rover rover1 has image objective1 in mode low_res. Rover rover1 has image objective1 in mode high_res. Store(s) store0 is empty. Store(s) store1 is full. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode high_res, and Image objective1 was communicated in mode low_res.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover0 has soil analyzed in waypoint waypoint0. B. Image objective2 was communicated in mode low_res. C. Rover rover0 has image objective0 in mode high_res. D. Rover rover0 has rock analyzed in waypoint waypoint0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 has soil analyzed in waypoint waypoint0", "Image objective2 was communicated in mode low_res", "Rover rover0 has image objective0 in mode high_res", "Rover rover0 has rock analyzed in waypoint waypoint0"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 7994956755284539049, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 5 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective4. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera1 supports high_res. Camera camera2 supports high_res. Camera camera0 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0, waypoint0 to waypoint1. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective4 is visible from waypoint1. Objective objective2 is visible from waypoint0. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective3 is visible from waypoint0. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint2. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Rock data were communicated from the following waypoints: waypoint2, waypoint1. Soil data was communicated from waypoint waypoint2; Image objective0 was communicated in mode high_res. Rover rover0 has soil analyzed in waypoint waypoint2. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint2. Rover rover1 has image objective2 in mode high_res. Rover rover1 has image objective0 in mode high_res. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint2;, Image objective2 was communicated in mode high_res, Image objective0 was communicated in mode high_res, Rock data was communicated from waypoint waypoint2;, and Rock data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover0 is at waypoint0. B. Image objective3 was communicated in mode colour. C. Image objective4 was communicated in mode high_res. D. Image objective2 was communicated in mode high_res.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 is at waypoint0", "Image objective3 was communicated in mode colour", "Image objective4 was communicated in mode high_res", "Image objective2 was communicated in mode high_res"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 4773690407359984596, "group": "landmarks_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 5 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective4. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera1 supports high_res. Camera camera2 supports high_res. Camera camera0 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0, waypoint0 to waypoint1. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint1, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective4 is visible from waypoint1. Objective objective2 is visible from waypoint0. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective3 is visible from waypoint0. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint2. Soil can be sampled at the following location(s): waypoint2 and waypoint0. Rovers rover0 and rover1 are available. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover1 has its camera camera1 calibrated. Rover rover1 has its camera camera0 calibrated. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint2;, Image objective2 was communicated in mode high_res, Image objective0 was communicated in mode high_res, Rock data was communicated from waypoint waypoint2;, and Rock data was communicated from waypoint waypoint1;.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. Rover rover1 has image objective3 in mode low_res. B. Image objective0 was communicated in mode low_res. C. Image objective3 was communicated in mode high_res. D. Rover rover0 has rock analyzed in waypoint waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 has image objective3 in mode low_res", "Image objective0 was communicated in mode low_res", "Image objective3 was communicated in mode high_res", "Rover rover0 has rock analyzed in waypoint waypoint2"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
