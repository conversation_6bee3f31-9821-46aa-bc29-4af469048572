{"id": 4497726815764405601, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Camera camera1 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective1 in mode colour. Rover rover0 has its camera camera0 calibrated. Store(s) store1 and store0 are full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover0 is at waypoint1 and Rover rover0 is at waypoint2. B. Store(s) store0 is empty. C. Rocks can be sampled at the following location(s): waypoint0. D. Rock data was communicated from waypoint waypoint1;.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 is at waypoint1 and Rover rover0 is at waypoint2", "Store(s) store0 is empty", "Rocks can be sampled at the following location(s): waypoint0", "Rock data was communicated from waypoint waypoint1;"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 7861171636961752333, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint1 to waypoint0, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint0, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint1: waypoint0, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2, waypoint1, waypoint4, and waypoint3. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint4. Rovers rover0 and rover1 are available. Rock data were communicated from the following waypoints: waypoint1, waypoint0. Soil data was communicated from waypoint waypoint0; Image objective0 was communicated in mode low_res. Image objective0 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective0 in mode low_res. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode colour. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Soil can be sampled at the following location(s): waypoint1. B. Rover rover0 is at waypoint1. C. Rover rover0 has soil analyzed in waypoint waypoint0 and Rover rover1 has soil analyzed in waypoint waypoint0. D. Rocks can be sampled at the following location(s): waypoint3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Soil can be sampled at the following location(s): waypoint1", "Rover rover0 is at waypoint1", "Rover rover0 has soil analyzed in waypoint waypoint0 and Rover rover1 has soil analyzed in waypoint waypoint0", "Rocks can be sampled at the following location(s): waypoint3"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 2324362675760777823, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective1. Camera camera2 supports colour and low_res. Camera camera1 supports colour. Camera camera0 supports high_res and colour and low_res. Rover rover0 can traverse from waypoint5 to waypoint6, waypoint6 to waypoint5, waypoint1 to waypoint5, waypoint4 to waypoint5, waypoint5 to waypoint2, waypoint5 to waypoint0, waypoint0 to waypoint5, waypoint5 to waypoint4, waypoint0 to waypoint3, waypoint3 to waypoint0, waypoint5 to waypoint1, waypoint2 to waypoint5. Rover rover1 can traverse from waypoint1 to waypoint6, waypoint2 to waypoint0, waypoint6 to waypoint1, waypoint5 to waypoint0, waypoint2 to waypoint4, waypoint0 to waypoint5, waypoint0 to waypoint6, waypoint4 to waypoint2, waypoint0 to waypoint3, waypoint6 to waypoint0, waypoint3 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint4: waypoint5, waypoint2, waypoint1, waypoint6, and waypoint3. Waypoint(s) are visible from waypoint0: waypoint6, waypoint3, waypoint5, and waypoint2. Waypoint(s) are visible from waypoint2: waypoint3, waypoint0, waypoint5, waypoint1, and waypoint4. Waypoint(s) are visible from waypoint1: waypoint5, waypoint2, waypoint4, waypoint6, and waypoint3. Waypoint(s) are visible from waypoint5: waypoint1, waypoint2, waypoint4, waypoint6, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint6: waypoint5, waypoint1, waypoint4, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint3: waypoint5, waypoint2, waypoint1, waypoint4, waypoint6, and waypoint0. Objective objective1 is visible from waypoint5, waypoint4, waypoint0, and waypoint2. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint4. Rocks can be sampled at the following location(s): waypoint2 and waypoint6. Soil can be sampled at the following location(s): waypoint6, waypoint3, waypoint1, and waypoint4. Rovers rover0 and rover1 are available. Rock data were communicated from the following waypoints: waypoint3, waypoint5, waypoint4. Image objective0 was communicated in mode high_res. Image objective1 was communicated in mode high_res. Image objective1 was communicated in mode colour. Rover rover0 has rock analyzed in waypoint waypoint4. Rover rover0 has rock analyzed in waypoint waypoint5. Rover rover1 has rock analyzed in waypoint waypoint3. Rover rover1 has image objective1 in mode high_res. Rover rover1 has image objective1 in mode colour. Rover rover1 has image objective0 in mode high_res. Rover rover0 has its camera camera2 calibrated. Store(s) store0 and store1 are full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover1 is at waypoint2. B. Rover rover0 has image objective1 in mode high_res and Image objective1 was communicated in mode high_res. C. Rocks can be sampled at the following location(s): waypoint3. D. Rover rover1 is at waypoint5 and Rover rover1 is at waypoint3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 is at waypoint2", "Rover rover0 has image objective1 in mode high_res and Image objective1 was communicated in mode high_res", "Rocks can be sampled at the following location(s): waypoint3", "Rover rover1 is at waypoint5 and Rover rover1 is at waypoint3"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 7210913259460911577, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 7 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera1 can be calibrated on objective1. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective4. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Camera camera1 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective6 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint1. Objective objective4 is visible from waypoint1. Objective objective3 is visible from waypoint2. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective5 is visible from waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint0. Rocks can be sampled at the following location(s): waypoint1, waypoint2, and waypoint0. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint2; Image objective3 was communicated in mode high_res. Rover rover1 has soil analyzed in waypoint waypoint2. Rover rover1 has image objective0 in mode colour. Rover rover1 has image objective3 in mode high_res. Rover rover0 has its camera camera1 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Soil can be sampled at the following location(s): waypoint2 and Rocks can be sampled at the following location(s): waypoint1. B. Rover rover1 is at waypoint2 and Rover rover0 has image objective1 in mode low_res. C. Rover rover1 has rock analyzed in waypoint waypoint2 and Rocks can be sampled at the following location(s): waypoint2. D. Rover rover0 has soil analyzed in waypoint waypoint0 and Rover rover1 is available.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Soil can be sampled at the following location(s): waypoint2 and Rocks can be sampled at the following location(s): waypoint1", "Rover rover1 is at waypoint2 and Rover rover0 has image objective1 in mode low_res", "Rover rover1 has rock analyzed in waypoint waypoint2 and Rocks can be sampled at the following location(s): waypoint2", "Rover rover0 has soil analyzed in waypoint waypoint0 and Rover rover1 is available"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 2872903799677729435, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Store(s) store0 and store1 are empty. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover0 is at waypoint0 and Rover rover1 is at waypoint2. B. Rover rover1 has image objective1 in mode high_res. C. Rover rover0 is at waypoint2 and Rover rover0 is at waypoint0. D. Soil can be sampled at the following location(s): waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 is at waypoint0 and Rover rover1 is at waypoint2", "Rover rover1 has image objective1 in mode high_res", "Rover rover0 is at waypoint2 and Rover rover0 is at waypoint0", "Soil can be sampled at the following location(s): waypoint2"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -1235732764542744935, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint1 to waypoint0, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint0, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint1: waypoint0, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2, waypoint1, waypoint4, and waypoint3. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint4. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Image objective0 was communicated in mode colour. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective0 in mode low_res. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode colour. Rover rover1 has its camera camera1 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover1 is at waypoint1. B. Rover rover1 has its camera camera0 calibrated. C. Rover rover0 has soil analyzed in waypoint waypoint0 and Soil can be sampled at the following location(s): waypoint0. D. Rocks can be sampled at the following location(s): waypoint4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 is at waypoint1", "Rover rover1 has its camera camera0 calibrated", "Rover rover0 has soil analyzed in waypoint waypoint0 and Soil can be sampled at the following location(s): waypoint0", "Rocks can be sampled at the following location(s): waypoint4"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -628658658738634411, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Camera camera1 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint1. Rover rover1 is at waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has its camera camera2 calibrated. Store(s) store0 and store1 are empty. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover1 has rock analyzed in waypoint waypoint0 and Rocks can be sampled at the following location(s): waypoint0. B. Rover rover1 has image objective0 in mode colour. C. Rocks can be sampled at the following location(s): waypoint1 and Rover rover1 is available. D. Soil can be sampled at the following location(s): waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 has rock analyzed in waypoint waypoint0 and Rocks can be sampled at the following location(s): waypoint0", "Rover rover1 has image objective0 in mode colour", "Rocks can be sampled at the following location(s): waypoint1 and Rover rover1 is available", "Soil can be sampled at the following location(s): waypoint2"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -755003146917583975, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint1 to waypoint0, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint0, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint1: waypoint0, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2, waypoint1, waypoint4, and waypoint3. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint4. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Image objective0 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode colour. Rover rover1 has its camera camera1 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover1 has soil analyzed in waypoint waypoint0 and Soil can be sampled at the following location(s): waypoint0. B. Rover rover0 is at waypoint1 and Rover rover1 is at waypoint2. C. Rover rover1 has soil analyzed in waypoint waypoint2. D. Rocks can be sampled at the following location(s): waypoint0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 has soil analyzed in waypoint waypoint0 and Soil can be sampled at the following location(s): waypoint0", "Rover rover0 is at waypoint1 and Rover rover1 is at waypoint2", "Rover rover1 has soil analyzed in waypoint waypoint2", "Rocks can be sampled at the following location(s): waypoint0"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 3887422845016589811, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective1. Camera camera2 supports colour and low_res. Camera camera1 supports colour. Camera camera0 supports high_res and colour and low_res. Rover rover0 can traverse from waypoint5 to waypoint6, waypoint6 to waypoint5, waypoint1 to waypoint5, waypoint4 to waypoint5, waypoint5 to waypoint2, waypoint5 to waypoint0, waypoint0 to waypoint5, waypoint5 to waypoint4, waypoint0 to waypoint3, waypoint3 to waypoint0, waypoint5 to waypoint1, waypoint2 to waypoint5. Rover rover1 can traverse from waypoint1 to waypoint6, waypoint2 to waypoint0, waypoint6 to waypoint1, waypoint5 to waypoint0, waypoint2 to waypoint4, waypoint0 to waypoint5, waypoint0 to waypoint6, waypoint4 to waypoint2, waypoint0 to waypoint3, waypoint6 to waypoint0, waypoint3 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint4: waypoint5, waypoint2, waypoint1, waypoint6, and waypoint3. Waypoint(s) are visible from waypoint0: waypoint6, waypoint3, waypoint5, and waypoint2. Waypoint(s) are visible from waypoint2: waypoint3, waypoint0, waypoint5, waypoint1, and waypoint4. Waypoint(s) are visible from waypoint1: waypoint5, waypoint2, waypoint4, waypoint6, and waypoint3. Waypoint(s) are visible from waypoint5: waypoint1, waypoint2, waypoint4, waypoint6, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint6: waypoint5, waypoint1, waypoint4, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint3: waypoint5, waypoint2, waypoint1, waypoint4, waypoint6, and waypoint0. Objective objective1 is visible from waypoint5, waypoint4, waypoint0, and waypoint2. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint4. Rocks can be sampled at the following location(s): waypoint2, waypoint6, and waypoint4. Soil can be sampled at the following location(s): waypoint6, waypoint3, waypoint1, and waypoint4. Rovers rover0 and rover1 are available. Rock data were communicated from the following waypoints: waypoint3, waypoint5. Image objective0 was communicated in mode high_res. Image objective1 was communicated in mode high_res. Image objective1 was communicated in mode colour. Rover rover0 has rock analyzed in waypoint waypoint5. Rover rover1 has rock analyzed in waypoint waypoint3. Rover rover1 has image objective1 in mode high_res. Rover rover1 has image objective0 in mode high_res. Rover rover0 has image objective1 in mode colour. Store(s) store0 and store1 are full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover1 has its camera camera1 calibrated and Store(s) store0 is empty. B. Rock data was communicated from waypoint waypoint1;. C. Rover rover1 has rock analyzed in waypoint waypoint4 and Rover rover0 has rock analyzed in waypoint waypoint4. D. Rocks can be sampled at the following location(s): waypoint5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 has its camera camera1 calibrated and Store(s) store0 is empty", "Rock data was communicated from waypoint waypoint1;", "Rover rover1 has rock analyzed in waypoint waypoint4 and Rover rover0 has rock analyzed in waypoint waypoint4", "Rocks can be sampled at the following location(s): waypoint5"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -5538864297682710631, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint1 to waypoint0, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint0, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint1: waypoint0, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2, waypoint1, waypoint4, and waypoint3. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint4. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Image objective0 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective0 in mode low_res. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode colour. Store(s) store1 and store0 are full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Soil can be sampled at the following location(s): waypoint1 and Rover rover0 has image objective1 in mode low_res. B. Rover rover0 has soil analyzed in waypoint waypoint0 and Soil can be sampled at the following location(s): waypoint0. C. Rover rover1 is at waypoint2 and Image objective0 was communicated in mode low_res. D. Soil can be sampled at the following location(s): waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Soil can be sampled at the following location(s): waypoint1 and Rover rover0 has image objective1 in mode low_res", "Rover rover0 has soil analyzed in waypoint waypoint0 and Soil can be sampled at the following location(s): waypoint0", "Rover rover1 is at waypoint2 and Image objective0 was communicated in mode low_res", "Soil can be sampled at the following location(s): waypoint1"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -5244094381470899807, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera0 can be calibrated on objective2. Camera camera1 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint2 and waypoint0. Soil can be sampled at the following location(s): waypoint2 and waypoint0. Rovers rover0 and rover1 are available. Rover rover0 has image objective1 in mode low_res. Rover rover0 has image objective1 in mode high_res. Rover rover0 has its camera camera1 calibrated. Store(s) store0 and store1 are empty. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover0 has soil analyzed in waypoint waypoint2 and Soil can be sampled at the following location(s): waypoint2. B. Rover rover1 has rock analyzed in waypoint waypoint1. C. Rocks can be sampled at the following location(s): waypoint1. D. Rover rover0 is at waypoint1 and Rover rover0 has soil analyzed in waypoint waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 has soil analyzed in waypoint waypoint2 and Soil can be sampled at the following location(s): waypoint2", "Rover rover1 has rock analyzed in waypoint waypoint1", "Rocks can be sampled at the following location(s): waypoint1", "Rover rover0 is at waypoint1 and Rover rover0 has soil analyzed in waypoint waypoint2"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 4796978865687087238, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera0 can be calibrated on objective2. Camera camera1 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint2 and waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Image objective0 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint2. Rover rover0 has image objective1 in mode low_res. Rover rover0 has image objective1 in mode high_res. Rover rover0 has image objective0 in mode low_res. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover1 is at waypoint1 and Rover rover1 has its camera camera0 calibrated. B. Rover rover0 is at waypoint1 and Rover rover0 is at waypoint0. C. Rocks can be sampled at the following location(s): waypoint1. D. Rover rover1 has its camera camera1 calibrated.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 is at waypoint1 and Rover rover1 has its camera camera0 calibrated", "Rover rover0 is at waypoint1 and Rover rover0 is at waypoint0", "Rocks can be sampled at the following location(s): waypoint1", "Rover rover1 has its camera camera1 calibrated"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 6347990569667260018, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Camera camera1 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Soil data was communicated from waypoint waypoint1; Image objective1 was communicated in mode colour. Image objective0 was communicated in mode low_res. Rover rover1 has soil analyzed in waypoint waypoint1. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode low_res. Rover rover1 has image objective0 in mode colour. Rover rover1 has image objective1 in mode colour. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover0 is at waypoint1 and Rover rover0 is at waypoint0. B. Rover rover0 is at waypoint0 and Rover rover1 is at waypoint0. C. Rocks can be sampled at the following location(s): waypoint2 and Rover rover0 is at waypoint2. D. Rock data was communicated from waypoint waypoint1; and Rover rover1 has its camera camera1 calibrated.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 is at waypoint1 and Rover rover0 is at waypoint0", "Rover rover0 is at waypoint0 and Rover rover1 is at waypoint0", "Rocks can be sampled at the following location(s): waypoint2 and Rover rover0 is at waypoint2", "Rock data was communicated from waypoint waypoint1; and Rover rover1 has its camera camera1 calibrated"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 4309717698291833104, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera0 can be calibrated on objective2. Camera camera1 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint2 and waypoint0. Rovers rover0 and rover1 are available. Rover rover1 has rock analyzed in waypoint waypoint2. Rover rover0 has image objective1 in mode low_res. Rover rover0 has image objective1 in mode high_res. Rover rover0 has its camera camera1 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover0 has its camera camera1 calibrated and Rocks can be sampled at the following location(s): waypoint1. B. Rock data was communicated from waypoint waypoint2; and Rover rover0 has image objective0 in mode low_res. C. Rocks can be sampled at the following location(s): waypoint2. D. Rover rover1 is at waypoint2 and Rover rover1 is at waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 has its camera camera1 calibrated and Rocks can be sampled at the following location(s): waypoint1", "Rock data was communicated from waypoint waypoint2; and Rover rover0 has image objective0 in mode low_res", "Rocks can be sampled at the following location(s): waypoint2", "Rover rover1 is at waypoint2 and Rover rover1 is at waypoint1"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 1833918989864714378, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 5 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective4. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports high_res. Camera camera2 supports high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective4 is visible from waypoint1. Objective objective3 is visible from waypoint0. Objective objective0 is visible from waypoint2. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint2. Soil can be sampled at the following location(s): waypoint2 and waypoint0. Rovers rover0 and rover1 are available. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective2 in mode high_res. Rover rover0 has its camera camera2 calibrated. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover0 has soil analyzed in waypoint waypoint0 and Rover rover1 has soil analyzed in waypoint waypoint0. B. Soil can be sampled at the following location(s): waypoint1. C. Rover rover0 has image objective2 in mode high_res and Soil data was communicated from waypoint waypoint1;. D. Rover rover1 is at waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 has soil analyzed in waypoint waypoint0 and Rover rover1 has soil analyzed in waypoint waypoint0", "Soil can be sampled at the following location(s): waypoint1", "Rover rover0 has image objective2 in mode high_res and Soil data was communicated from waypoint waypoint1;", "Rover rover1 is at waypoint2"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
