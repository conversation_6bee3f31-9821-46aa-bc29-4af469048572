{"id": -4048612321761238602, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Given the plan: \"power on instrument instrument0 on the satellite satellite0, point the satellite satellite0 to direction star4 instead of groundstation1, calibrate instrument instrument0 on the satellite satellite0 for direction star4, point the satellite satellite0 to direction groundstation3 instead of star4, point the satellite satellite0 to direction planet6 instead of groundstation3, capture an image in direction planet6 in mode infrared2 using the instrument instrument0 on the satellite satellite0, point the satellite satellite0 to direction planet5 instead of planet6, capture an image in direction planet5 in mode thermograph0 using the instrument instrument0 on the satellite satellite0, turn off the instrument instrument0 on board the satellite satellite0\"; can the following action be removed from this plan and still have a valid plan: turn off the instrument instrument0 on board the satellite satellite0?", "answer": "yes"}
{"id": 20508163328247991, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Given the plan: \"turn satellite satellite0 to point from groundstation1 direction to planet5, turn satellite satellite0 to point from planet5 direction to groundstation1, switch on instrument instrument0 on the satellite satellite0, turn satellite satellite0 to point from groundstation1 direction to planet6, turn satellite satellite0 to point from planet6 direction to star2, turn satellite satellite0 to point from star2 direction to star4, calibrate instrument instrument0 on the satellite satellite0 for direction star4, turn satellite satellite0 to point from star4 direction to planet6, capture an image of direction planet6 in mode infrared2 using the instrument instrument0 on satellite satellite0, turn satellite satellite0 to point from planet6 direction to planet5, capture an image of direction planet5 in mode thermograph0 using the instrument instrument0 on satellite satellite0\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: turn satellite satellite0 to point from groundstation1 direction to planet5 and turn satellite satellite0 to point from planet5 direction to groundstation1?", "answer": "yes"}
{"id": -6290043019660818644, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite6 has following instruments onboard: instrument10, instrument11, instrument9. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite4 has following instruments onboard: instrument6, instrument7. Satellite satellite1 has following instruments onboard: instrument2, instrument1. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite3 has following instruments onboard: instrument4, instrument5. Satellite satellite7 has following instruments onboard: instrument12. Instrument instrument5 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument7 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument12 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument10 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument8 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument4 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument6 supports image of mode infrared2 and its calibration target is groundstation0. Instrument instrument2 supports image of mode infrared2 and its calibration target is star2. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3.  Currently, Satellite satellite6 is pointing to groundstation1. Satellite satellite5 is pointing to groundstation3. Satellite satellite8 is pointing to star2. Satellite satellite3 is pointing to groundstation1. Satellite satellite0 is pointing to groundstation1. Satellite satellite2 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite4 is pointing to planet6. Satellite satellite7 is pointing to star2. Satellite satellite9 is pointing to star4. Power is available on the following satellite(s): satellite8, satellite2, satellite0, satellite6, satellite9, satellite5, satellite7, satellite1, satellite3, satellite4. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available, A thermograph0 mode image of target planet5 is available, Satellite satellite8 is pointing to planet6, and Satellite satellite6 is pointing to star4.", "question": "Given the plan: \"direct the satellite satellite8 to point in the direction planet6 instead of star2, direct the satellite satellite6 to point in the direction star4 instead of groundstation1, activate the instrument instrument5 which is on the satellite satellite3, adjust the instrument instrument5 on the satellite satellite3 for direction groundstation1, direct the satellite satellite3 to point in the direction planet6 instead of groundstation1, take an image of direction planet6 in mode infrared2 using instrument instrument5 on board satellite satellite3, direct the satellite satellite3 to point in the direction planet5 instead of planet6, take an image of direction planet5 in mode thermograph0 using instrument instrument5 on board satellite satellite3, activate the instrument instrument13 which is on the satellite satellite8\"; can the following action be removed from this plan and still have a valid plan: activate the instrument instrument13 which is on the satellite satellite8?", "answer": "yes"}
{"id": 7046667637904357957, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Given the plan: \"turn on the instrument instrument0 on board the satellite satellite0, change the direction of the satellite satellite0 from groundstation1 to planet5, change the direction of the satellite satellite0 from planet5 to star4, adjust the direction for instrument instrument0 on the satellite satellite0 to star4, change the direction of the satellite satellite0 from star4 to planet5, capture an image in direction planet5 in mode thermograph0 using the instrument instrument0 on the satellite satellite0, change the direction of the satellite satellite0 from planet5 to planet6, capture an image in direction planet6 in mode infrared2 using the instrument instrument0 on the satellite satellite0, change the direction of the satellite satellite0 from planet6 to groundstation1\"; can the following action be removed from this plan and still have a valid plan: capture an image in direction planet6 in mode infrared2 using the instrument instrument0 on the satellite satellite0?", "answer": "no"}
{"id": 193306943933099839, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, groundstation4, star0, planet5, phenomenon6, groundstation3. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument4. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite1 has following instruments onboard: instrument3. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to groundstation4. Satellite satellite2 is pointing to star2. Satellite satellite1 is pointing to groundstation3. Power is available on the following satellite(s): satellite2, satellite0, satellite1. The goal is to reach a state where the following facts hold: A spectrograph0 mode image of target phenomenon6 is available, Satellite satellite1 is pointing to phenomenon6, and A thermograph2 mode image of target planet5 is available.", "question": "Given the plan: \"turn satellite satellite1 from direction groundstation3 to direction phenomenon6, turn on the instrument instrument4 on board the satellite satellite2, point the instrument instrument4 on the satellite satellite2 to direction star2, turn satellite satellite2 from direction star2 to direction phenomenon6, capture a image of direction phenomenon6 in mode spectrograph0 using the instrument instrument4 on the satellite satellite2, turn satellite satellite2 from direction phenomenon6 to direction planet5, capture a image of direction planet5 in mode thermograph2 using the instrument instrument4 on the satellite satellite2, turn satellite satellite0 from direction groundstation4 to direction planet5\"; can the following action be removed from this plan and still have a valid plan: capture a image of direction planet5 in mode thermograph2 using the instrument instrument4 on the satellite satellite2?", "answer": "no"}
{"id": 2596369097557016442, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Given the plan: \"turn the satellite satellite0 from direction groundstation1 to direction planet5, turn the satellite satellite0 from direction planet5 to direction groundstation1, turn on the instrument instrument0 on board the satellite satellite0, turn the satellite satellite0 from direction groundstation1 to direction star4, point the instrument instrument0 on the satellite satellite0 to direction star4, turn the satellite satellite0 from direction star4 to direction planet5, capture an image of direction planet5 in mode thermograph0 using the instrument instrument0 on satellite satellite0, turn the satellite satellite0 from direction planet5 to direction star4, turn the satellite satellite0 from direction star4 to direction planet6, capture an image of direction planet6 in mode infrared2 using the instrument instrument0 on satellite satellite0\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: turn the satellite satellite0 from direction groundstation1 to direction planet5 and turn the satellite satellite0 from direction planet5 to direction groundstation1?", "answer": "yes"}
{"id": 428595516947545534, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): phenomenon5, star6, star1, groundstation4, groundstation2, star3, groundstation0. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite3 has following instruments onboard: instrument8, instrument7. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite1 has following instruments onboard: instrument4, instrument5, instrument3. Satellite satellite4 has following instruments onboard: instrument9, instrument10, instrument11. Satellite satellite6 has following instruments onboard: instrument17, instrument16, instrument15. Satellite satellite5 has following instruments onboard: instrument13, instrument12, instrument14. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument14 supports image of mode image1 and its calibration target is groundstation4. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument6 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument8 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument2 supports image of mode image1 and its calibration target is star1. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument5 supports image of mode image1 and its calibration target is star3. Instrument instrument7 supports image of mode image1 and its calibration target is star3. Instrument instrument13 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0.  Currently, Satellite satellite1 is pointing to phenomenon5. Satellite satellite6 is pointing to groundstation0. Satellite satellite2 is pointing to groundstation2. Satellite satellite3 is pointing to star6. Satellite satellite5 is pointing to phenomenon5. Satellite satellite0 is pointing to groundstation2. Satellite satellite4 is pointing to star1. Power is available on the following satellite(s): satellite3, satellite2, satellite0, satellite6, satellite4, satellite1, satellite5. The goal is to reach a state where the following facts hold: A image1 mode image of target star6 is available, A image0 mode image of target phenomenon5 is available, Satellite satellite1 is pointing to phenomenon5, Satellite satellite5 is pointing to groundstation0, and Satellite satellite4 is pointing to star1.", "question": "Given the plan: \"change the direction of the satellite satellite5 from phenomenon5 to groundstation0, switch on instrument instrument6 on board satellite satellite2, adjust the instrument instrument6 on the satellite satellite2 for direction groundstation2, change the direction of the satellite satellite2 from groundstation2 to phenomenon5, take an image of direction phenomenon5 in mode image0 using instrument instrument6 on board satellite satellite2, change the direction of the satellite satellite2 from phenomenon5 to star6, take an image of direction star6 in mode image1 using instrument instrument6 on board satellite satellite2, change the direction of the satellite satellite0 from groundstation2 to groundstation4\"; can the following action be removed from this plan and still have a valid plan: change the direction of the satellite satellite5 from phenomenon5 to groundstation0?", "answer": "no"}
{"id": -8346296507874456290, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Given the plan: \"turn on the instrument instrument0 on board the satellite satellite0, point the satellite satellite0 from groundstation1 direction to new direction star4, calibrate instrument instrument0 on the satellite satellite0 for direction star4, calibrate instrument instrument0 on the satellite satellite0 for direction star4, point the satellite satellite0 from star4 direction to new direction planet5, capture an image in direction planet5 in mode thermograph0 using the instrument instrument0 on the satellite satellite0, point the satellite satellite0 from planet5 direction to new direction planet6, capture an image in direction planet6 in mode infrared2 using the instrument instrument0 on the satellite satellite0, point the satellite satellite0 from planet6 direction to new direction planet5\"; can the following action be removed from this plan and still have a valid plan: point the satellite satellite0 from planet6 direction to new direction planet5?", "answer": "yes"}
{"id": -4993312540406227093, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Given the plan: \"turn on the instrument instrument0 on the satellite satellite0, turn satellite satellite0 to point from groundstation1 direction to star4, adjust the direction for instrument instrument0 on the satellite satellite0 to star4, turn satellite satellite0 to point from star4 direction to planet6, capture an image of direction planet6 in mode infrared2 using the instrument instrument0 on satellite satellite0, turn satellite satellite0 to point from planet6 direction to planet5, capture an image of direction planet5 in mode thermograph0 using the instrument instrument0 on satellite satellite0, turn satellite satellite0 to point from planet5 direction to star2, turn satellite satellite0 to point from star2 direction to groundstation0\"; can the following action be removed from this plan and still have a valid plan: turn satellite satellite0 to point from planet5 direction to star2?", "answer": "no"}
{"id": -905183918628274492, "group": "action_justification_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star2, groundstation1, planet6, star4, planet5, groundstation3, groundstation0. There are 3 image mode(s): thermograph0, image1, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation1. Power is available on the following satellite(s): satellite0. The goal is to reach a state where the following facts hold: A infrared2 mode image of target planet6 is available and A thermograph0 mode image of target planet5 is available.", "question": "Given the plan: \"activate the instrument instrument0 which is on the satellite satellite0, turn satellite satellite0 to point from groundstation1 direction to star4, calibrate instrument instrument0 on the satellite satellite0 for direction star4, turn satellite satellite0 to point from star4 direction to planet6, capture a image of direction planet6 in mode infrared2 using the instrument instrument0 on the satellite satellite0, turn satellite satellite0 to point from planet6 direction to planet5, capture a image of direction planet5 in mode thermograph0 using the instrument instrument0 on the satellite satellite0, turn satellite satellite0 to point from planet5 direction to groundstation1, turn satellite satellite0 to point from groundstation1 direction to groundstation3\"; can the following action be removed from this plan and still have a valid plan: turn satellite satellite0 to point from star4 direction to planet6?", "answer": "no"}
