{"id": 2300972001521773338, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: xena, heidi, michelle, dave, vic, zoe, alice, and carol. There are 8 items/roles: iceskates, frisbee, quadcopter, necklace, zebra, slinky, whale, and guitar. Currently, zoe is assigned whale, heidi is assigned iceskates, michelle is assigned zebra, alice is assigned necklace, carol is assigned frisbee, dave is assigned guitar, vic is assigned quadcopter, and xena is assigned slinky.", "question": "Which of the following actions can eventually be applied? A. exchange whale of vic with zebra of dave. B. exchange zebra of xena with necklace of xena. C. exchange necklace of alice with frisbee of alice. D. exchange whale of dave with zebra of dave.", "choices": {"label": ["A", "B", "C", "D"], "text": ["exchange whale of vic with zebra of dave", "exchange zebra of xena with necklace of xena", "exchange necklace of alice with frisbee of alice", "exchange whale of dave with zebra of dave"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 5363384699282263561, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: xena, ted, heidi, bob, dave, kevin, and alice. There are 7 items/roles: parsnip, quince, yam, ulluco, leek, mushroom, and valerian. Currently, heidi is assigned yam, dave is assigned valerian, xena is assigned quince, ted is assigned mushroom, alice is assigned ulluco, bob is assigned parsnip, and kevin is assigned leek.", "question": "Which of the following actions can eventually be applied? A. exchange ulluco of xena with leek of xena. B. exchange ulluco of kevin with valerian of kevin. C. exchange parsnip of bob with ulluco of ted. D. exchange mushroom of dave with parsnip of dave.", "choices": {"label": ["A", "B", "C", "D"], "text": ["exchange ulluco of xena with leek of xena", "exchange ulluco of kevin with valerian of kevin", "exchange parsnip of bob with ulluco of ted", "exchange mushroom of dave with parsnip of dave"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -2712966604549046117, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: xena, ted, heidi, bob, dave, kevin, and alice. There are 7 items/roles: parsnip, quince, yam, ulluco, leek, mushroom, and valerian. Currently, xena is assigned valerian, heidi is assigned leek, dave is assigned yam, kevin is assigned mushroom, ted is assigned quince, alice is assigned ulluco, and bob is assigned parsnip.", "question": "Which of the following actions can eventually be applied? A. trade ulluco of bob for leek of bob. B. trade parsnip of dave for valerian of ted. C. trade quince of heidi for ulluco of heidi. D. trade mushroom of bob for yam of bob.", "choices": {"label": ["A", "B", "C", "D"], "text": ["trade ulluco of bob for leek of bob", "trade parsnip of dave for valerian of ted", "trade quince of heidi for ulluco of heidi", "trade mushroom of bob for yam of bob"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 1789100020401752326, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: xena, quentin, bob, frank, vic, and liam. There are 6 items/roles: knead, sander, wrench, ratchet, pliers, and nibbler. Currently, frank is assigned sander, vic is assigned knead, liam is assigned pliers, bob is assigned ratchet, xena is assigned wrench, and quentin is assigned nibbler.", "question": "Which of the following actions can eventually be applied? A. swap vic:nibbler with vic:sander. B. swap frank:knead with xena:wrench. C. swap liam:pliers with liam:wrench. D. swap frank:wrench with frank:sander.", "choices": {"label": ["A", "B", "C", "D"], "text": ["swap vic:nibbler with vic:sander", "swap frank:knead with xena:wrench", "swap liam:pliers with liam:wrench", "swap frank:wrench with frank:sander"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 7647918843487028999, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: xena, ted, heidi, bob, dave, kevin, and alice. There are 7 items/roles: parsnip, quince, yam, ulluco, leek, mushroom, and valerian. Currently, bob is assigned valerian, xena is assigned parsnip, ted is assigned mushroom, alice is assigned yam, dave is assigned quince, kevin is assigned leek, and heidi is assigned ulluco.", "question": "Which of the following actions can eventually be applied? A. swap ted with alice, mushroom for ulluco. B. swap kevin with kevin, yam for quince. C. swap ted with ted, valerian for quince. D. swap heidi with heidi, yam for mushroom.", "choices": {"label": ["A", "B", "C", "D"], "text": ["swap ted with alice, mushroom for ulluco", "swap kevin with kevin, yam for quince", "swap ted with ted, valerian for quince", "swap heidi with heidi, yam for mushroom"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -2987946268267597438, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: xena, heidi, michelle, dave, vic, zoe, alice, and carol. There are 8 items/roles: iceskates, frisbee, quadcopter, necklace, zebra, slinky, whale, and guitar. Currently, zoe is assigned whale, vic is assigned necklace, carol is assigned guitar, michelle is assigned zebra, heidi is assigned quadcopter, alice is assigned iceskates, dave is assigned frisbee, and xena is assigned slinky.", "question": "Which of the following actions can eventually be applied? A. exchange slinky of vic with zebra of vic. B. exchange guitar of heidi with frisbee of heidi. C. exchange quadcopter of heidi with frisbee of carol. D. exchange zebra of dave with slinky of dave.", "choices": {"label": ["A", "B", "C", "D"], "text": ["exchange slinky of vic with zebra of vic", "exchange guitar of heidi with frisbee of heidi", "exchange quadcopter of heidi with frisbee of carol", "exchange zebra of dave with slinky of dave"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -4862122697158698648, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: xena, ted, heidi, bob, dave, kevin, and alice. There are 7 items/roles: parsnip, quince, yam, ulluco, leek, mushroom, and valerian. Currently, bob is assigned quince, alice is assigned parsnip, dave is assigned valerian, kevin is assigned yam, ted is assigned ulluco, xena is assigned leek, and heidi is assigned mushroom.", "question": "Which of the following actions can eventually be applied? A. swap ted:ulluco with ted:parsnip. B. swap xena:leek with ted:parsnip. C. swap heidi:ulluco with heidi:valerian. D. swap xena:quince with xena:mushroom.", "choices": {"label": ["A", "B", "C", "D"], "text": ["swap ted:ulluco with ted:parsnip", "swap xena:leek with ted:parsnip", "swap heidi:ulluco with heidi:valerian", "swap xena:quince with xena:mushroom"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -1355629327753333971, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: xena, heidi, michelle, dave, vic, zoe, alice, and carol. There are 8 items/roles: iceskates, frisbee, quadcopter, necklace, zebra, slinky, whale, and guitar. Currently, dave is assigned slinky, xena is assigned frisbee, heidi is assigned zebra, michelle is assigned necklace, vic is assigned guitar, carol is assigned whale, zoe is assigned iceskates, and alice is assigned quadcopter.", "question": "Which of the following actions can eventually be applied? A. exchange slinky of xena with necklace of xena. B. exchange iceskates of vic with quadcopter of vic. C. exchange frisbee of michelle with iceskates of michelle. D. exchange whale of carol with slinky of xena.", "choices": {"label": ["A", "B", "C", "D"], "text": ["exchange slinky of xena with necklace of xena", "exchange iceskates of vic with quadcopter of vic", "exchange frisbee of michelle with iceskates of michelle", "exchange whale of carol with slinky of xena"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 9039276937033102247, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: xena, heidi, michelle, dave, vic, zoe, alice, and carol. There are 8 items/roles: iceskates, frisbee, quadcopter, necklace, zebra, slinky, whale, and guitar. Currently, dave is assigned slinky, zoe is assigned whale, carol is assigned guitar, michelle is assigned zebra, alice is assigned necklace, heidi is assigned frisbee, xena is assigned iceskates, and vic is assigned quadcopter.", "question": "Which of the following actions can eventually be applied? A. exchange necklace of zoe with whale of zoe. B. exchange guitar of xena with slinky of alice. C. exchange guitar of dave with zebra of dave. D. exchange necklace of xena with zebra of xena.", "choices": {"label": ["A", "B", "C", "D"], "text": ["exchange necklace of zoe with whale of zoe", "exchange guitar of xena with slinky of alice", "exchange guitar of dave with zebra of dave", "exchange necklace of xena with zebra of xena"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 8457124189181021587, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: xena, quentin, bob, frank, vic, and liam. There are 6 items/roles: knead, sander, wrench, ratchet, pliers, and nibbler. Currently, quentin is assigned sander, frank is assigned nibbler, vic is assigned knead, liam is assigned pliers, bob is assigned ratchet, and xena is assigned wrench.", "question": "Which of the following actions can eventually be applied? A. trade sander of quentin for knead of quentin. B. trade knead of vic for wrench of vic. C. trade sander of bob for ratchet of vic. D. trade sander of frank for wrench of frank.", "choices": {"label": ["A", "B", "C", "D"], "text": ["trade sander of quentin for knead of quentin", "trade knead of vic for wrench of vic", "trade sander of bob for ratchet of vic", "trade sander of frank for wrench of frank"]}, "query": "Which action is reachable from this state?", "answer": "C"}
