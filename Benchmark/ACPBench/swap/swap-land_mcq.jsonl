{"id": 481197292643812895, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: heidi, kevin, bob, xena, alice, ted, and dave. There are 7 items/roles: valerian, quince, leek, mushroom, ulluco, parsnip, and yam. Currently, heidi is assigned valerian, ted is assigned mushroom, alice is assigned yam, bob is assigned leek, xena is assigned parsnip, kevin is assigned quince, and dave is assigned ulluco. The goal is to reach a state where the following facts hold: heidi is assigned mushroom, kevin is assigned yam, ted is assigned leek, bob is assigned parsnip, alice is assigned valerian, xena is assigned quince, and dave is assigned ulluco.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. ted is assigned quince. <PERSON><PERSON> dave is assigned quince. C. heidi is assigned parsnip. D. ted is assigned leek.", "choices": {"label": ["A", "B", "C", "D"], "text": ["ted is assigned quince", "dave is assigned quince", "heidi is assigned parsnip", "ted is assigned leek"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": 3340046248284691489, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: frank, vic, bob, xena, quentin, and liam. There are 6 items/roles: sander, nibbler, wrench, ratchet, knead, and pliers. Currently, xena is assigned pliers, bob is assigned ratchet, liam is assigned wrench, quentin is assigned sander, vic is assigned knead, and frank is assigned nibbler. The goal is to reach a state where the following facts hold: frank is assigned pliers, liam is assigned sander, quentin is assigned knead, bob is assigned nibbler, xena is assigned wrench, and vic is assigned ratchet.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. bob is assigned nibbler. B. frank is assigned knead. C. xena is assigned ratchet. D. bob is assigned wrench.", "choices": {"label": ["A", "B", "C", "D"], "text": ["bob is assigned nibbler", "frank is assigned knead", "xena is assigned ratchet", "bob is assigned wrench"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 105499911598814093, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, zoe, carol, vic, xena, alice, michelle, and dave. There are 8 items/roles: zebra, iceskates, necklace, guitar, frisbee, slinky, quadcopter, and whale. Currently, xena is assigned whale, heidi is assigned necklace, zoe is assigned quadcopter, carol is assigned guitar, michelle is assigned slinky, alice is assigned iceskates, dave is assigned zebra, and vic is assigned frisbee. The goal is to reach a state where the following facts hold: dave is assigned iceskates, alice is assigned zebra, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, zoe is assigned whale, vic is assigned necklace, and xena is assigned slinky.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. vic is assigned necklace. B. dave is assigned necklace. C. vic is assigned zebra. D. michelle is assigned whale.", "choices": {"label": ["A", "B", "C", "D"], "text": ["vic is assigned necklace", "dave is assigned necklace", "vic is assigned zebra", "michelle is assigned whale"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -1293079638875164679, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: frank, vic, bob, xena, quentin, and liam. There are 6 items/roles: sander, nibbler, wrench, ratchet, knead, and pliers. Currently, bob is assigned sander, liam is assigned ratchet, quentin is assigned knead, vic is assigned pliers, xena is assigned wrench, and frank is assigned nibbler. The goal is to reach a state where the following facts hold: frank is assigned pliers, liam is assigned sander, quentin is assigned knead, bob is assigned nibbler, xena is assigned wrench, and vic is assigned ratchet.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. bob is assigned wrench. B. quentin is assigned pliers. C. vic is assigned wrench. D. vic is assigned ratchet.", "choices": {"label": ["A", "B", "C", "D"], "text": ["bob is assigned wrench", "quentin is assigned pliers", "vic is assigned wrench", "vic is assigned ratchet"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
{"id": -5863871147475670648, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, zoe, carol, vic, xena, alice, michelle, and dave. There are 8 items/roles: zebra, iceskates, necklace, guitar, frisbee, slinky, quadcopter, and whale. Currently, dave is assigned iceskates, alice is assigned necklace, zoe is assigned frisbee, xena is assigned slinky, vic is assigned whale, heidi is assigned guitar, michelle is assigned zebra, and carol is assigned quadcopter. The goal is to reach a state where the following facts hold: dave is assigned iceskates, alice is assigned zebra, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, zoe is assigned whale, vic is assigned necklace, and xena is assigned slinky.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. alice is assigned frisbee. B. alice is assigned zebra. C. vic is assigned guitar. D. michelle is assigned guitar.", "choices": {"label": ["A", "B", "C", "D"], "text": ["alice is assigned frisbee", "alice is assigned zebra", "vic is assigned guitar", "michelle is assigned guitar"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 4940705949281648741, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: frank, vic, bob, xena, quentin, and liam. There are 6 items/roles: sander, nibbler, wrench, ratchet, knead, and pliers. Currently, quentin is assigned pliers, frank is assigned wrench, bob is assigned sander, liam is assigned nibbler, xena is assigned ratchet, and vic is assigned knead. The goal is to reach a state where the following facts hold: frank is assigned pliers, liam is assigned sander, quentin is assigned knead, bob is assigned nibbler, xena is assigned wrench, and vic is assigned ratchet.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. quentin is assigned knead. B. frank is assigned knead. C. vic is assigned sander. D. vic is assigned nibbler.", "choices": {"label": ["A", "B", "C", "D"], "text": ["quentin is assigned knead", "frank is assigned knead", "vic is assigned sander", "vic is assigned nibbler"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": 4253607171926211489, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: heidi, kevin, bob, xena, alice, ted, and dave. There are 7 items/roles: valerian, quince, leek, mushroom, ulluco, parsnip, and yam. Currently, dave is assigned parsnip, heidi is assigned quince, alice is assigned yam, bob is assigned mushroom, kevin is assigned leek, xena is assigned valerian, and ted is assigned ulluco. The goal is to reach a state where the following facts hold: heidi is assigned mushroom, kevin is assigned yam, ted is assigned leek, bob is assigned parsnip, alice is assigned valerian, xena is assigned quince, and dave is assigned ulluco.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. xena is assigned yam. B. kevin is assigned yam. C. kevin is assigned ulluco. D. dave is assigned leek.", "choices": {"label": ["A", "B", "C", "D"], "text": ["xena is assigned yam", "kevin is assigned yam", "kevin is assigned ulluco", "dave is assigned leek"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": 3006376119510676894, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, zoe, carol, vic, xena, alice, michelle, and dave. There are 8 items/roles: zebra, iceskates, necklace, guitar, frisbee, slinky, quadcopter, and whale. Currently, dave is assigned iceskates, carol is assigned frisbee, alice is assigned necklace, zoe is assigned whale, heidi is assigned guitar, michelle is assigned zebra, xena is assigned slinky, and vic is assigned quadcopter. The goal is to reach a state where the following facts hold: dave is assigned iceskates, alice is assigned zebra, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, zoe is assigned whale, vic is assigned necklace, and xena is assigned slinky.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. vic is assigned slinky. B. alice is assigned zebra. C. heidi is assigned necklace. D. dave is assigned frisbee.", "choices": {"label": ["A", "B", "C", "D"], "text": ["vic is assigned slinky", "alice is assigned zebra", "heidi is assigned necklace", "dave is assigned frisbee"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "B"}
{"id": -3140714423911560623, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, zoe, carol, vic, xena, alice, michelle, and dave. There are 8 items/roles: zebra, iceskates, necklace, guitar, frisbee, slinky, quadcopter, and whale. Currently, xena is assigned whale, heidi is assigned quadcopter, carol is assigned necklace, zoe is assigned guitar, alice is assigned iceskates, michelle is assigned zebra, vic is assigned frisbee, and dave is assigned slinky. The goal is to reach a state where the following facts hold: dave is assigned iceskates, alice is assigned zebra, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, zoe is assigned whale, vic is assigned necklace, and xena is assigned slinky.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. xena is assigned slinky. B. dave is assigned necklace. C. xena is assigned zebra. D. vic is assigned slinky.", "choices": {"label": ["A", "B", "C", "D"], "text": ["xena is assigned slinky", "dave is assigned necklace", "xena is assigned zebra", "vic is assigned slinky"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "A"}
{"id": -440863436631593540, "group": "landmarks_mcq", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, zoe, carol, vic, xena, alice, michelle, and dave. There are 8 items/roles: zebra, iceskates, necklace, guitar, frisbee, slinky, quadcopter, and whale. Currently, xena is assigned whale, alice is assigned quadcopter, zoe is assigned frisbee, carol is assigned slinky, heidi is assigned iceskates, michelle is assigned necklace, vic is assigned guitar, and dave is assigned zebra. The goal is to reach a state where the following facts hold: dave is assigned iceskates, alice is assigned zebra, carol is assigned frisbee, michelle is assigned quadcopter, heidi is assigned guitar, zoe is assigned whale, vic is assigned necklace, and xena is assigned slinky.", "question": "Which of the following facts is a landmark (must hold at some point along any plan) for the current state? A. vic is assigned slinky. B. carol is assigned guitar. C. heidi is assigned slinky. D. carol is assigned frisbee.", "choices": {"label": ["A", "B", "C", "D"], "text": ["vic is assigned slinky", "carol is assigned guitar", "heidi is assigned slinky", "carol is assigned frisbee"]}, "query": "Which fact must hold at some point on any way to the goal from the current state?", "answer": "D"}
