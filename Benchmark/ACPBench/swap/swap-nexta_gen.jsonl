{"id": 901605640805481485, "group": "goal_closer_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: kevin, ted, alice, heidi, bob, xena, and dave. There are 7 items/roles: yam, leek, quince, valerian, ulluco, mushroom, and parsnip. Currently, dave is assigned mushroom, kevin is assigned valerian, alice is assigned parsnip, heidi is assigned leek, xena is assigned yam, ted is assigned quince, and bob is assigned ulluco. The goal is to reach a state where the following facts hold: ted is assigned leek, kevin is assigned yam, xena is assigned quince, heidi is assigned mushroom, bob is assigned parsnip, dave is assigned ulluco, and alice is assigned valerian. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(swap alice bob parsnip ulluco)", "(swap heidi kevin leek valerian)", "(swap xena bob yam ulluco)", "(swap bob bob ulluco ulluco)", "(swap ted kevin quince valerian)", "(swap xena dave yam mushroom)", "(swap heidi heidi leek leek)", "(swap heidi xena leek yam)", "(swap dave dave mushroom mushroom)", "(swap kevin ted valerian quince)", "(swap xena alice yam parsnip)", "(swap kevin xena valerian yam)", "(swap dave kevin mushroom valerian)", "(swap heidi ted leek quince)", "(swap bob heidi ulluco leek)", "(swap ted heidi quince leek)", "(swap dave bob mushroom ulluco)", "(swap xena heidi yam leek)", "(swap ted ted quince quince)", "(swap heidi alice leek parsnip)", "(swap dave ted mushroom quince)", "(swap ted dave quince mushroom)", "(swap bob ted ulluco quince)", "(swap dave heidi mushroom leek)"], "no": ["(swap alice alice parsnip parsnip)", "(swap kevin kevin valerian valerian)", "(swap xena xena yam yam)"], "opt": "6", "yes": ["(swap alice dave parsnip mushroom)", "(swap alice xena parsnip yam)", "(swap bob alice ulluco parsnip)", "(swap bob kevin ulluco valerian)", "(swap alice kevin parsnip valerian)", "(swap alice heidi parsnip leek)", "(swap ted bob quince ulluco)", "(swap heidi dave leek mushroom)", "(swap xena kevin yam valerian)", "(swap ted xena quince yam)", "(swap xena ted yam quince)", "(swap kevin heidi valerian leek)", "(swap bob xena ulluco yam)", "(swap bob dave ulluco mushroom)", "(swap ted alice quince parsnip)", "(swap kevin alice valerian parsnip)", "(swap dave alice mushroom parsnip)", "(swap dave xena mushroom yam)", "(swap kevin dave valerian mushroom)", "(swap alice ted parsnip quince)", "(swap heidi bob leek ulluco)", "(swap kevin bob valerian ulluco)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Bob Dave Heidi Kevin Ted Xena - agent Leek Mushroom Parsnip Quince Ulluco Valerian Yam - role)\n    (:init (assigned Alice Parsnip) (assigned Bob Ulluco) (assigned Dave Mushroom) (assigned Heidi Leek) (assigned Kevin Valerian) (assigned Ted Quince) (assigned Xena Yam))\n    (:goal (and (assigned Xena Quince) (assigned Heidi Mushroom) (assigned Kevin Yam) (assigned Alice Valerian) (assigned Dave Ulluco) (assigned Ted Leek) (assigned Bob Parsnip)))\n)"}
{"id": -5214082836982774090, "group": "goal_closer_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: carol, alice, zoe, heidi, xena, vic, michelle, and dave. There are 8 items/roles: slinky, iceskates, necklace, guitar, quadcopter, frisbee, zebra, and whale. Currently, xena is assigned frisbee, dave is assigned iceskates, vic is assigned slinky, heidi is assigned guitar, michelle is assigned quadcopter, carol is assigned whale, zoe is assigned necklace, and alice is assigned zebra. The goal is to reach a state where the following facts hold: carol is assigned frisbee, dave is assigned iceskates, vic is assigned necklace, xena is assigned slinky, heidi is assigned guitar, michelle is assigned quadcopter, zoe is assigned whale, and alice is assigned zebra. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(swap xena carol frisbee whale)", "(swap heidi dave guitar iceskates)", "(swap alice dave zebra iceskates)", "(swap zoe zoe necklace necklace)", "(swap michelle carol quadcopter whale)", "(swap vic michelle slinky quadcopter)", "(swap carol dave whale iceskates)", "(swap carol xena whale frisbee)", "(swap vic vic slinky slinky)", "(swap heidi carol guitar whale)", "(swap vic alice slinky zebra)", "(swap zoe dave necklace iceskates)", "(swap heidi vic guitar slinky)", "(swap dave xena iceskates frisbee)", "(swap alice heidi zebra guitar)", "(swap michelle alice quadcopter zebra)", "(swap michelle xena quadcopter frisbee)", "(swap heidi heidi guitar guitar)", "(swap dave heidi iceskates guitar)", "(swap alice carol zebra whale)", "(swap dave carol iceskates whale)", "(swap michelle zoe quadcopter necklace)", "(swap heidi michelle guitar quadcopter)", "(swap carol alice whale zebra)", "(swap alice xena zebra frisbee)", "(swap michelle heidi quadcopter guitar)", "(swap dave dave iceskates iceskates)", "(swap zoe michelle necklace quadcopter)", "(swap michelle vic quadcopter slinky)", "(swap alice vic zebra slinky)", "(swap carol michelle whale quadcopter)", "(swap vic dave slinky iceskates)", "(swap carol carol whale whale)", "(swap zoe alice necklace zebra)", "(swap alice zoe zebra necklace)", "(swap dave alice iceskates zebra)", "(swap zoe heidi necklace guitar)", "(swap dave michelle iceskates quadcopter)", "(swap michelle michelle quadcopter quadcopter)", "(swap xena dave frisbee iceskates)", "(swap michelle dave quadcopter iceskates)", "(swap vic heidi slinky guitar)", "(swap xena alice frisbee zebra)", "(swap alice michelle zebra quadcopter)", "(swap dave vic iceskates slinky)", "(swap xena vic frisbee slinky)", "(swap vic xena slinky frisbee)", "(swap heidi xena guitar frisbee)", "(swap carol heidi whale guitar)", "(swap xena xena frisbee frisbee)", "(swap heidi zoe guitar necklace)", "(swap xena heidi frisbee guitar)", "(swap heidi alice guitar zebra)"], "no": ["(swap dave zoe iceskates necklace)", "(swap xena michelle frisbee quadcopter)", "(swap alice alice zebra zebra)"], "opt": "3", "yes": ["(swap vic carol slinky whale)", "(swap vic zoe slinky necklace)", "(swap carol vic whale slinky)", "(swap xena zoe frisbee necklace)", "(swap zoe vic necklace slinky)", "(swap carol zoe whale necklace)", "(swap zoe xena necklace frisbee)", "(swap zoe carol necklace whale)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Carol Dave Heidi Michelle Vic Xena Zoe - agent Frisbee Guitar IceSkates Necklace Quadcopter Slinky Whale Zebra - role)\n    (:init (assigned Alice Zebra) (assigned Carol Whale) (assigned Dave IceSkates) (assigned Heidi Guitar) (assigned Michelle Quadcopter) (assigned Vic Slinky) (assigned Xena Frisbee) (assigned Zoe Necklace))\n    (:goal (and (assigned Xena Slinky) (assigned Dave IceSkates) (assigned Alice Zebra) (assigned Michelle Quadcopter) (assigned Vic Necklace) (assigned Heidi Guitar) (assigned Carol Frisbee) (assigned Zoe Whale)))\n)"}
{"id": -3094648237254466022, "group": "goal_closer_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: kevin, ted, alice, heidi, bob, xena, and dave. There are 7 items/roles: yam, leek, quince, valerian, ulluco, mushroom, and parsnip. Currently, ted is assigned valerian, xena is assigned ulluco, heidi is assigned quince, kevin is assigned leek, dave is assigned parsnip, bob is assigned mushroom, and alice is assigned yam. The goal is to reach a state where the following facts hold: ted is assigned leek, kevin is assigned yam, xena is assigned quince, heidi is assigned mushroom, bob is assigned parsnip, dave is assigned ulluco, and alice is assigned valerian. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(swap alice xena yam ulluco)", "(swap bob bob mushroom mushroom)", "(swap alice alice yam yam)", "(swap xena xena ulluco ulluco)", "(swap ted dave valerian parsnip)", "(swap bob heidi mushroom quince)", "(swap heidi dave quince parsnip)", "(swap dave heidi parsnip quince)", "(swap heidi heidi quince quince)", "(swap xena bob ulluco mushroom)", "(swap kevin xena leek ulluco)", "(swap xena ted ulluco valerian)", "(swap dave bob parsnip mushroom)", "(swap dave xena parsnip ulluco)", "(swap xena dave ulluco parsnip)", "(swap xena kevin ulluco leek)", "(swap dave kevin parsnip leek)", "(swap bob ted mushroom valerian)", "(swap ted xena valerian ulluco)", "(swap dave ted parsnip valerian)", "(swap bob kevin mushroom leek)", "(swap alice heidi yam quince)", "(swap alice ted yam valerian)", "(swap heidi ted quince valerian)", "(swap heidi kevin quince leek)", "(swap ted heidi valerian quince)", "(swap kevin heidi leek quince)", "(swap heidi xena quince ulluco)", "(swap kevin dave leek parsnip)", "(swap dave alice parsnip yam)", "(swap kevin kevin leek leek)", "(swap ted ted valerian valerian)", "(swap kevin bob leek mushroom)", "(swap ted bob valerian mushroom)", "(swap heidi alice quince yam)", "(swap xena alice ulluco yam)", "(swap bob xena mushroom ulluco)", "(swap bob dave mushroom parsnip)", "(swap bob alice mushroom yam)"], "no": ["(swap alice bob yam mushroom)", "(swap dave dave parsnip parsnip)", "(swap alice dave yam parsnip)"], "opt": "5", "yes": ["(swap xena heidi ulluco quince)", "(swap heidi bob quince mushroom)", "(swap kevin alice leek yam)", "(swap kevin ted leek valerian)", "(swap alice kevin yam leek)", "(swap ted alice valerian yam)", "(swap ted kevin valerian leek)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Bob Dave Heidi Kevin Ted Xena - agent Leek Mushroom Parsnip Quince Ulluco Valerian Yam - role)\n    (:init (assigned Alice Yam) (assigned Bob Mushroom) (assigned Dave Parsnip) (assigned Heidi Quince) (assigned Kevin Leek) (assigned Ted Valerian) (assigned Xena Ulluco))\n    (:goal (and (assigned Xena Quince) (assigned Heidi Mushroom) (assigned Kevin Yam) (assigned Alice Valerian) (assigned Dave Ulluco) (assigned Ted Leek) (assigned Bob Parsnip)))\n)"}
{"id": -5871743391192331473, "group": "goal_closer_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: carol, alice, zoe, heidi, xena, vic, michelle, and dave. There are 8 items/roles: slinky, iceskates, necklace, guitar, quadcopter, frisbee, zebra, and whale. Currently, zoe is assigned zebra, michelle is assigned whale, carol is assigned frisbee, dave is assigned iceskates, vic is assigned necklace, heidi is assigned slinky, xena is assigned guitar, and alice is assigned quadcopter. The goal is to reach a state where the following facts hold: carol is assigned frisbee, dave is assigned iceskates, vic is assigned necklace, xena is assigned slinky, heidi is assigned guitar, michelle is assigned quadcopter, zoe is assigned whale, and alice is assigned zebra. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(swap heidi michelle slinky whale)", "(swap vic alice necklace quadcopter)", "(swap alice carol quadcopter frisbee)", "(swap alice heidi quadcopter slinky)", "(swap zoe heidi zebra slinky)", "(swap dave michelle iceskates whale)", "(swap carol heidi frisbee slinky)", "(swap xena dave guitar iceskates)", "(swap dave alice iceskates quadcopter)", "(swap heidi alice slinky quadcopter)", "(swap heidi vic slinky necklace)", "(swap vic michelle necklace whale)", "(swap carol zoe frisbee zebra)", "(swap dave zoe iceskates zebra)", "(swap carol carol frisbee frisbee)", "(swap michelle dave whale iceskates)", "(swap zoe dave zebra iceskates)", "(swap heidi dave slinky iceskates)", "(swap alice vic quadcopter necklace)", "(swap michelle michelle whale whale)", "(swap vic dave necklace iceskates)", "(swap michelle vic whale necklace)", "(swap dave dave iceskates iceskates)", "(swap xena zoe guitar zebra)", "(swap dave heidi iceskates slinky)", "(swap michelle carol whale frisbee)", "(swap heidi carol slinky frisbee)", "(swap heidi xena slinky guitar)", "(swap xena michelle guitar whale)", "(swap carol vic frisbee necklace)", "(swap vic carol necklace frisbee)", "(swap vic heidi necklace slinky)", "(swap carol dave frisbee iceskates)", "(swap xena xena guitar guitar)", "(swap vic xena necklace guitar)", "(swap xena carol guitar frisbee)", "(swap dave carol iceskates frisbee)", "(swap vic zoe necklace zebra)", "(swap zoe xena zebra guitar)", "(swap alice xena quadcopter guitar)", "(swap vic vic necklace necklace)", "(swap zoe zoe zebra zebra)", "(swap dave xena iceskates guitar)", "(swap michelle xena whale guitar)", "(swap carol alice frisbee quadcopter)", "(swap heidi zoe slinky zebra)", "(swap zoe vic zebra necklace)", "(swap alice dave quadcopter iceskates)", "(swap xena vic guitar necklace)", "(swap dave vic iceskates necklace)", "(swap michelle heidi whale slinky)", "(swap alice alice quadcopter quadcopter)", "(swap carol xena frisbee guitar)", "(swap zoe carol zebra frisbee)"], "no": ["(swap carol michelle frisbee whale)", "(swap heidi heidi slinky slinky)", "(swap xena alice guitar quadcopter)"], "opt": "3", "yes": ["(swap zoe michelle zebra whale)", "(swap michelle zoe whale zebra)", "(swap zoe alice zebra quadcopter)", "(swap alice zoe quadcopter zebra)", "(swap michelle alice whale quadcopter)", "(swap alice michelle quadcopter whale)", "(swap xena heidi guitar slinky)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Carol Dave Heidi Michelle Vic Xena Zoe - agent Frisbee Guitar IceSkates Necklace Quadcopter Slinky Whale Zebra - role)\n    (:init (assigned Alice Quadcopter) (assigned Carol Frisbee) (assigned Dave IceSkates) (assigned Heidi Slinky) (assigned Michelle Whale) (assigned Vic Necklace) (assigned Xena Guitar) (assigned Zoe Zebra))\n    (:goal (and (assigned Xena Slinky) (assigned Dave IceSkates) (assigned Alice Zebra) (assigned Michelle Quadcopter) (assigned Vic Necklace) (assigned Heidi Guitar) (assigned Carol Frisbee) (assigned Zoe Whale)))\n)"}
{"id": -9146556056947184762, "group": "goal_closer_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: carol, alice, zoe, heidi, xena, vic, michelle, and dave. There are 8 items/roles: slinky, iceskates, necklace, guitar, quadcopter, frisbee, zebra, and whale. Currently, xena is assigned frisbee, michelle is assigned zebra, dave is assigned slinky, heidi is assigned quadcopter, vic is assigned iceskates, zoe is assigned whale, carol is assigned guitar, and alice is assigned necklace. The goal is to reach a state where the following facts hold: carol is assigned frisbee, dave is assigned iceskates, vic is assigned necklace, xena is assigned slinky, heidi is assigned guitar, michelle is assigned quadcopter, zoe is assigned whale, and alice is assigned zebra. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1:?r1 with ?a2:?r2.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(swap vic alice iceskates necklace)", "(swap vic vic iceskates iceskates)", "(swap vic zoe iceskates whale)", "(swap dave dave slinky slinky)", "(swap michelle michelle zebra zebra)", "(swap dave xena slinky frisbee)", "(swap xena vic frisbee iceskates)", "(swap dave michelle slinky zebra)", "(swap xena heidi frisbee quadcopter)", "(swap carol zoe guitar whale)", "(swap dave heidi slinky quadcopter)", "(swap alice carol necklace guitar)", "(swap zoe carol whale guitar)", "(swap michelle dave zebra slinky)", "(swap heidi xena quadcopter frisbee)", "(swap carol michelle guitar zebra)", "(swap zoe heidi whale quadcopter)", "(swap xena dave frisbee slinky)", "(swap vic xena iceskates frisbee)", "(swap heidi heidi quadcopter quadcopter)", "(swap heidi vic quadcopter iceskates)", "(swap xena zoe frisbee whale)", "(swap alice xena necklace frisbee)", "(swap zoe michelle whale zebra)", "(swap carol vic guitar iceskates)", "(swap alice alice necklace necklace)", "(swap carol xena guitar frisbee)", "(swap zoe zoe whale whale)", "(swap dave carol slinky guitar)", "(swap vic carol iceskates guitar)", "(swap vic michelle iceskates zebra)", "(swap michelle vic zebra iceskates)", "(swap vic heidi iceskates quadcopter)", "(swap alice vic necklace iceskates)", "(swap dave vic slinky iceskates)", "(swap zoe xena whale frisbee)", "(swap zoe dave whale slinky)", "(swap alice zoe necklace whale)", "(swap carol alice guitar necklace)", "(swap heidi michelle quadcopter zebra)", "(swap xena carol frisbee guitar)", "(swap michelle xena zebra frisbee)", "(swap dave zoe slinky whale)", "(swap xena michelle frisbee zebra)", "(swap vic dave iceskates slinky)", "(swap zoe alice whale necklace)", "(swap zoe vic whale iceskates)", "(swap michelle carol zebra guitar)", "(swap heidi alice quadcopter necklace)", "(swap alice dave necklace slinky)", "(swap carol carol guitar guitar)", "(swap xena alice frisbee necklace)"], "no": ["(swap michelle zoe zebra whale)", "(swap heidi zoe quadcopter whale)", "(swap xena xena frisbee frisbee)"], "opt": "6", "yes": ["(swap alice michelle necklace zebra)", "(swap michelle heidi zebra quadcopter)", "(swap alice heidi necklace quadcopter)", "(swap carol heidi guitar quadcopter)", "(swap heidi carol quadcopter guitar)", "(swap dave alice slinky necklace)", "(swap heidi dave quadcopter slinky)", "(swap carol dave guitar slinky)", "(swap michelle alice zebra necklace)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Carol Dave Heidi Michelle Vic Xena Zoe - agent Frisbee Guitar IceSkates Necklace Quadcopter Slinky Whale Zebra - role)\n    (:init (assigned Alice Necklace) (assigned Carol Guitar) (assigned Dave Slinky) (assigned Heidi Quadcopter) (assigned Michelle Zebra) (assigned Vic IceSkates) (assigned Xena Frisbee) (assigned Zoe Whale))\n    (:goal (and (assigned Xena Slinky) (assigned Dave IceSkates) (assigned Alice Zebra) (assigned Michelle Quadcopter) (assigned Vic Necklace) (assigned Heidi Guitar) (assigned Carol Frisbee) (assigned Zoe Whale)))\n)"}
{"id": 4980187707675887476, "group": "goal_closer_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: carol, alice, zoe, heidi, xena, vic, michelle, and dave. There are 8 items/roles: slinky, iceskates, necklace, guitar, quadcopter, frisbee, zebra, and whale. Currently, heidi is assigned necklace, carol is assigned frisbee, dave is assigned slinky, michelle is assigned iceskates, xena is assigned whale, vic is assigned guitar, alice is assigned zebra, and zoe is assigned quadcopter. The goal is to reach a state where the following facts hold: carol is assigned frisbee, dave is assigned iceskates, vic is assigned necklace, xena is assigned slinky, heidi is assigned guitar, michelle is assigned quadcopter, zoe is assigned whale, and alice is assigned zebra. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1:?r1 with ?a2:?r2.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(swap dave michelle slinky iceskates)", "(swap dave carol slinky frisbee)", "(swap carol dave frisbee slinky)", "(swap xena xena whale whale)", "(swap xena vic whale guitar)", "(swap carol vic frisbee guitar)", "(swap alice alice zebra zebra)", "(swap dave dave slinky slinky)", "(swap xena zoe whale quadcopter)", "(swap dave zoe slinky quadcopter)", "(swap zoe dave quadcopter slinky)", "(swap heidi michelle necklace iceskates)", "(swap vic dave guitar slinky)", "(swap vic michelle guitar iceskates)", "(swap michelle xena iceskates whale)", "(swap zoe zoe quadcopter quadcopter)", "(swap dave vic slinky guitar)", "(swap heidi alice necklace zebra)", "(swap zoe heidi quadcopter necklace)", "(swap michelle michelle iceskates iceskates)", "(swap carol xena frisbee whale)", "(swap alice xena zebra whale)", "(swap carol zoe frisbee quadcopter)", "(swap vic vic guitar guitar)", "(swap carol heidi frisbee necklace)", "(swap dave xena slinky whale)", "(swap michelle carol iceskates frisbee)", "(swap michelle dave iceskates slinky)", "(swap zoe xena quadcopter whale)", "(swap alice heidi zebra necklace)", "(swap zoe alice quadcopter zebra)", "(swap vic xena guitar whale)", "(swap vic zoe guitar quadcopter)", "(swap alice carol zebra frisbee)", "(swap alice dave zebra slinky)", "(swap zoe michelle quadcopter iceskates)", "(swap xena carol whale frisbee)", "(swap vic carol guitar frisbee)", "(swap xena heidi whale necklace)", "(swap michelle vic iceskates guitar)", "(swap alice zoe zebra quadcopter)", "(swap michelle heidi iceskates necklace)", "(swap xena alice whale zebra)", "(swap dave alice slinky zebra)", "(swap heidi dave necklace slinky)", "(swap xena dave whale slinky)", "(swap heidi heidi necklace necklace)", "(swap michelle alice iceskates zebra)", "(swap zoe vic quadcopter guitar)", "(swap vic alice guitar zebra)", "(swap heidi carol necklace frisbee)", "(swap dave heidi slinky necklace)", "(swap zoe carol quadcopter frisbee)", "(swap heidi xena necklace whale)", "(swap alice vic zebra guitar)", "(swap alice michelle zebra iceskates)", "(swap carol michelle frisbee iceskates)"], "no": ["(swap carol alice frisbee zebra)", "(swap carol carol frisbee frisbee)", "(swap heidi zoe necklace quadcopter)"], "opt": "4", "yes": ["(swap michelle zoe iceskates quadcopter)", "(swap xena michelle whale iceskates)", "(swap vic heidi guitar necklace)", "(swap heidi vic necklace guitar)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Carol Dave Heidi Michelle Vic Xena Zoe - agent Frisbee Guitar IceSkates Necklace Quadcopter Slinky Whale Zebra - role)\n    (:init (assigned Alice Zebra) (assigned Carol Frisbee) (assigned Dave Slinky) (assigned Heidi Necklace) (assigned Michelle IceSkates) (assigned Vic Guitar) (assigned Xena Whale) (assigned Zoe Quadcopter))\n    (:goal (and (assigned Xena Slinky) (assigned Dave IceSkates) (assigned Alice Zebra) (assigned Michelle Quadcopter) (assigned Vic Necklace) (assigned Heidi Guitar) (assigned Carol Frisbee) (assigned Zoe Whale)))\n)"}
{"id": -8071596867334160239, "group": "goal_closer_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: carol, alice, zoe, heidi, xena, vic, michelle, and dave. There are 8 items/roles: slinky, iceskates, necklace, guitar, quadcopter, frisbee, zebra, and whale. Currently, heidi is assigned necklace, michelle is assigned whale, dave is assigned iceskates, xena is assigned slinky, vic is assigned quadcopter, zoe is assigned frisbee, carol is assigned guitar, and alice is assigned zebra. The goal is to reach a state where the following facts hold: carol is assigned frisbee, dave is assigned iceskates, vic is assigned necklace, xena is assigned slinky, heidi is assigned guitar, michelle is assigned quadcopter, zoe is assigned whale, and alice is assigned zebra. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1 with ?a2, ?r1 for ?r2.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(swap zoe alice frisbee zebra)", "(swap xena alice slinky zebra)", "(swap michelle zoe whale frisbee)", "(swap alice dave zebra iceskates)", "(swap zoe zoe frisbee frisbee)", "(swap alice alice zebra zebra)", "(swap dave michelle iceskates whale)", "(swap zoe xena frisbee slinky)", "(swap dave heidi iceskates necklace)", "(swap carol dave guitar iceskates)", "(swap michelle alice whale zebra)", "(swap vic zoe quadcopter frisbee)", "(swap michelle dave whale iceskates)", "(swap vic dave quadcopter iceskates)", "(swap alice vic zebra quadcopter)", "(swap carol heidi guitar necklace)", "(swap michelle carol whale guitar)", "(swap carol vic guitar quadcopter)", "(swap heidi alice necklace zebra)", "(swap xena dave slinky iceskates)", "(swap dave carol iceskates guitar)", "(swap michelle michelle whale whale)", "(swap zoe dave frisbee iceskates)", "(swap vic xena quadcopter slinky)", "(swap xena carol slinky guitar)", "(swap carol carol guitar guitar)", "(swap alice carol zebra guitar)", "(swap heidi carol necklace guitar)", "(swap vic alice quadcopter zebra)", "(swap xena zoe slinky frisbee)", "(swap alice heidi zebra necklace)", "(swap heidi dave necklace iceskates)", "(swap carol xena guitar slinky)", "(swap alice zoe zebra frisbee)", "(swap michelle heidi whale necklace)", "(swap carol michelle guitar whale)", "(swap xena heidi slinky necklace)", "(swap dave vic iceskates quadcopter)", "(swap dave alice iceskates zebra)", "(swap carol alice guitar zebra)", "(swap zoe michelle frisbee whale)", "(swap heidi zoe necklace frisbee)", "(swap xena xena slinky slinky)", "(swap heidi michelle necklace whale)", "(swap michelle xena whale slinky)", "(swap dave xena iceskates slinky)", "(swap heidi xena necklace slinky)", "(swap vic carol quadcopter guitar)", "(swap heidi heidi necklace necklace)", "(swap xena vic slinky quadcopter)", "(swap dave zoe iceskates frisbee)", "(swap xena michelle slinky whale)", "(swap vic vic quadcopter quadcopter)"], "no": ["(swap alice xena zebra slinky)", "(swap dave dave iceskates iceskates)", "(swap alice michelle zebra whale)"], "opt": "4", "yes": ["(swap zoe vic frisbee quadcopter)", "(swap michelle vic whale quadcopter)", "(swap zoe heidi frisbee necklace)", "(swap vic heidi quadcopter necklace)", "(swap vic michelle quadcopter whale)", "(swap heidi vic necklace quadcopter)", "(swap carol zoe guitar frisbee)", "(swap zoe carol frisbee guitar)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Carol Dave Heidi Michelle Vic Xena Zoe - agent Frisbee Guitar IceSkates Necklace Quadcopter Slinky Whale Zebra - role)\n    (:init (assigned Alice Zebra) (assigned Carol Guitar) (assigned Dave IceSkates) (assigned Heidi Necklace) (assigned Michelle Whale) (assigned Vic Quadcopter) (assigned Xena Slinky) (assigned Zoe Frisbee))\n    (:goal (and (assigned Xena Slinky) (assigned Dave IceSkates) (assigned Alice Zebra) (assigned Michelle Quadcopter) (assigned Vic Necklace) (assigned Heidi Guitar) (assigned Carol Frisbee) (assigned Zoe Whale)))\n)"}
{"id": 4486165446806620586, "group": "goal_closer_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: carol, alice, zoe, heidi, xena, vic, michelle, and dave. There are 8 items/roles: slinky, iceskates, necklace, guitar, quadcopter, frisbee, zebra, and whale. Currently, dave is assigned iceskates, heidi is assigned guitar, carol is assigned zebra, vic is assigned quadcopter, alice is assigned frisbee, xena is assigned whale, michelle is assigned slinky, and zoe is assigned necklace. The goal is to reach a state where the following facts hold: carol is assigned frisbee, dave is assigned iceskates, vic is assigned necklace, xena is assigned slinky, heidi is assigned guitar, michelle is assigned quadcopter, zoe is assigned whale, and alice is assigned zebra. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - trade ?r1 of ?a1 for ?r2 of ?a2.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(swap alice alice frisbee frisbee)", "(swap xena alice whale frisbee)", "(swap zoe zoe necklace necklace)", "(swap dave xena iceskates whale)", "(swap dave alice iceskates frisbee)", "(swap xena xena whale whale)", "(swap heidi dave guitar iceskates)", "(swap xena vic whale quadcopter)", "(swap carol xena zebra whale)", "(swap zoe michelle necklace slinky)", "(swap michelle zoe slinky necklace)", "(swap michelle michelle slinky slinky)", "(swap heidi carol guitar zebra)", "(swap alice xena frisbee whale)", "(swap zoe dave necklace iceskates)", "(swap michelle carol slinky zebra)", "(swap alice heidi frisbee guitar)", "(swap xena michelle whale slinky)", "(swap vic dave quadcopter iceskates)", "(swap dave heidi iceskates guitar)", "(swap heidi heidi guitar guitar)", "(swap michelle alice slinky frisbee)", "(swap vic vic quadcopter quadcopter)", "(swap heidi xena guitar whale)", "(swap carol heidi zebra guitar)", "(swap vic xena quadcopter whale)", "(swap heidi michelle guitar slinky)", "(swap alice michelle frisbee slinky)", "(swap carol michelle zebra slinky)", "(swap dave dave iceskates iceskates)", "(swap michelle xena slinky whale)", "(swap zoe carol necklace zebra)", "(swap zoe xena necklace whale)", "(swap dave carol iceskates zebra)", "(swap alice zoe frisbee necklace)", "(swap dave vic iceskates quadcopter)", "(swap xena zoe whale necklace)", "(swap zoe heidi necklace guitar)", "(swap xena heidi whale guitar)", "(swap xena dave whale iceskates)", "(swap zoe vic necklace quadcopter)", "(swap carol vic zebra quadcopter)", "(swap michelle dave slinky iceskates)", "(swap heidi vic guitar quadcopter)", "(swap zoe alice necklace frisbee)", "(swap carol carol zebra zebra)", "(swap vic zoe quadcopter necklace)", "(swap dave michelle iceskates slinky)", "(swap michelle heidi slinky guitar)", "(swap vic michelle quadcopter slinky)", "(swap dave zoe iceskates necklace)", "(swap carol zoe zebra necklace)", "(swap vic heidi quadcopter guitar)", "(swap michelle vic slinky quadcopter)", "(swap xena carol whale zebra)", "(swap vic alice quadcopter frisbee)", "(swap heidi alice guitar frisbee)", "(swap heidi zoe guitar necklace)", "(swap vic carol quadcopter zebra)"], "no": ["(swap carol dave zebra iceskates)", "(swap alice vic frisbee quadcopter)", "(swap alice dave frisbee iceskates)"], "opt": "4", "yes": ["(swap carol alice zebra frisbee)", "(swap alice carol frisbee zebra)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Alice Carol Dave Heidi Michelle Vic Xena Zoe - agent Frisbee Guitar IceSkates Necklace Quadcopter Slinky Whale Zebra - role)\n    (:init (assigned Alice Frisbee) (assigned Carol Zebra) (assigned Dave IceSkates) (assigned Heidi Guitar) (assigned Michelle Slinky) (assigned Vic Quadcopter) (assigned Xena Whale) (assigned Zoe Necklace))\n    (:goal (and (assigned Xena Slinky) (assigned Dave IceSkates) (assigned Alice Zebra) (assigned Michelle Quadcopter) (assigned Vic Necklace) (assigned Heidi Guitar) (assigned Carol Frisbee) (assigned Zoe Whale)))\n)"}
{"id": 198142755163588772, "group": "goal_closer_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 4 agents: steve, vic, bob, and zoe. There are 4 items/roles: book04, book03, book01, and book02. Currently, steve is assigned book01, zoe is assigned book02, bob is assigned book03, and vic is assigned book04. The goal is to reach a state where the following facts hold: steve is assigned book03, zoe is assigned book01, bob is assigned book02, and vic is assigned book04. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - swap ?a1:?r1 with ?a2:?r2.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(swap vic bob book04 book03)", "(swap bob vic book03 book04)", "(swap steve steve book01 book01)", "(swap steve vic book01 book04)", "(swap zoe zoe book02 book02)", "(swap bob bob book03 book03)", "(swap zoe vic book02 book04)"], "no": ["(swap vic vic book04 book04)", "(swap vic zoe book04 book02)", "(swap vic steve book04 book01)"], "opt": "2", "yes": ["(swap steve zoe book01 book02)", "(swap bob steve book03 book01)", "(swap zoe steve book02 book01)", "(swap zoe bob book02 book03)", "(swap bob zoe book03 book02)", "(swap steve bob book01 book03)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-4-12)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Steve Vic Zoe - agent book01 book02 book03 book04 - role)\n    (:init (assigned Bob book03) (assigned Steve book01) (assigned Vic book04) (assigned Zoe book02))\n    (:goal (and (assigned Bob book02) (assigned Steve book03) (assigned Vic book04) (assigned Zoe book01)))\n)"}
{"id": 2244747866632027793, "group": "goal_closer_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: frank, bob, quentin, liam, xena, and vic. There are 6 items/roles: wrench, sander, ratchet, pliers, nibbler, and knead. Currently, vic is assigned nibbler, quentin is assigned ratchet, frank is assigned knead, xena is assigned wrench, bob is assigned sander, and liam is assigned pliers. The goal is to reach a state where the following facts hold: vic is assigned ratchet, bob is assigned nibbler, liam is assigned sander, frank is assigned pliers, quentin is assigned knead, and xena is assigned wrench. The available actions are: (swap ?a1 ?a2 ?r1 ?r2) - exchange ?r1 of ?a1 with ?r2 of ?a2.", "question": "What is the next action that takes us towards the goal?", "answer": {"maybe": ["(swap xena quentin wrench ratchet)", "(swap vic xena nibbler wrench)", "(swap xena frank wrench knead)", "(swap frank frank knead knead)", "(swap vic liam nibbler pliers)", "(swap frank xena knead wrench)", "(swap quentin liam ratchet pliers)", "(swap quentin xena ratchet wrench)", "(swap liam xena pliers wrench)", "(swap bob liam sander pliers)", "(swap xena vic wrench nibbler)", "(swap quentin quentin ratchet ratchet)", "(swap quentin bob ratchet sander)", "(swap frank vic knead nibbler)", "(swap vic vic nibbler nibbler)", "(swap frank bob knead sander)", "(swap frank liam knead pliers)", "(swap vic quentin nibbler ratchet)", "(swap liam vic pliers nibbler)", "(swap liam bob pliers sander)", "(swap bob frank sander knead)", "(swap liam liam pliers pliers)", "(swap bob bob sander sander)", "(swap vic frank nibbler knead)", "(swap bob quentin sander ratchet)", "(swap liam quentin pliers ratchet)", "(swap quentin vic ratchet nibbler)", "(swap xena bob wrench sander)", "(swap liam frank pliers knead)"], "no": ["(swap bob xena sander wrench)", "(swap xena xena wrench wrench)", "(swap xena liam wrench pliers)"], "opt": "4", "yes": ["(swap quentin frank ratchet knead)", "(swap vic bob nibbler sander)", "(swap bob vic sander nibbler)", "(swap frank quentin knead ratchet)"]}, "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects Bob Frank Liam Quentin Vic Xena - agent Knead Nibbler Pliers Ratchet Sander Wrench - role)\n    (:init (assigned Bob Sander) (assigned Frank Knead) (assigned Liam Pliers) (assigned Quentin Ratchet) (assigned Vic Nibbler) (assigned Xena Wrench))\n    (:goal (and (assigned Quentin Knead) (assigned Liam Sander) (assigned Frank Pliers) (assigned Xena Wrench) (assigned Bob Nibbler) (assigned Vic Ratchet)))\n)"}
