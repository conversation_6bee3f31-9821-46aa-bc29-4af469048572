{"id": 4724179342319617331, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: alice, xena, heidi, dave, ted, bob, and kevin. There are 7 items/roles: quince, leek, ulluco, valerian, parsnip, mushroom, and yam. Currently, heidi is assigned yam, kevin is assigned mushroom, bob is assigned parsnip, dave is assigned ulluco, alice is assigned quince, xena is assigned valerian, and ted is assigned leek.", "question": "Is it possible to transition to a state where the following holds: heidi is assigned valerian and bob is assigned valerian?", "answer": "no"}
{"id": 6591384788862151733, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: alice, xena, heidi, carol, dave, zoe, michelle, and vic. There are 8 items/roles: zebra, necklace, guitar, frisbee, whale, slinky, iceskates, and quadcopter. Currently, carol is assigned frisbee, xena is assigned whale, michelle is assigned slinky, vic is assigned necklace, dave is assigned iceskates, zoe is assigned quadcopter, heidi is assigned guitar, and alice is assigned zebra.", "question": "Is it possible to transition to a state where the following holds: xena is assigned zebra and zoe is assigned zebra?", "answer": "no"}
{"id": -2981782850390496734, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: alice, xena, heidi, dave, ted, bob, and kevin. There are 7 items/roles: quince, leek, ulluco, valerian, parsnip, mushroom, and yam. Currently, heidi is assigned quince, kevin is assigned mushroom, bob is assigned parsnip, dave is assigned ulluco, alice is assigned yam, xena is assigned valerian, and ted is assigned leek.", "question": "Is it possible to transition to a state where the following holds: heidi is assigned mushroom and kevin is assigned ulluco?", "answer": "yes"}
{"id": 6637722070391320395, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: alice, xena, heidi, dave, ted, bob, and kevin. There are 7 items/roles: quince, leek, ulluco, valerian, parsnip, mushroom, and yam. Currently, heidi is assigned valerian, bob is assigned parsnip, ted is assigned quince, kevin is assigned leek, alice is assigned mushroom, xena is assigned ulluco, and dave is assigned yam.", "question": "Is it possible to transition to a state where the following holds: heidi is assigned quince and alice is assigned quince?", "answer": "no"}
{"id": -2656658457923006555, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: quentin, frank, xena, liam, bob, and vic. There are 6 items/roles: sander, ratchet, wrench, pliers, knead, and nibbler. Currently, frank is assigned ratchet, vic is assigned sander, liam is assigned knead, bob is assigned wrench, xena is assigned nibbler, and quentin is assigned pliers.", "question": "Is it possible to transition to a state where the following holds: xena is assigned wrench and frank is assigned wrench?", "answer": "no"}
{"id": 4312873891786312159, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: quentin, frank, xena, liam, bob, and vic. There are 6 items/roles: sander, ratchet, wrench, pliers, knead, and nibbler. Currently, vic is assigned nibbler, frank is assigned ratchet, liam is assigned wrench, xena is assigned pliers, quentin is assigned knead, and bob is assigned sander.", "question": "Is it possible to transition to a state where the following holds: liam is assigned ratchet and quentin is assigned wrench?", "answer": "yes"}
{"id": -699973422265378932, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: alice, xena, heidi, dave, ted, bob, and kevin. There are 7 items/roles: quince, leek, ulluco, valerian, parsnip, mushroom, and yam. Currently, heidi is assigned quince, kevin is assigned mushroom, xena is assigned parsnip, dave is assigned leek, ted is assigned ulluco, alice is assigned yam, and bob is assigned valerian.", "question": "Is it possible to transition to a state where the following holds: dave is assigned parsnip and xena is assigned mushroom?", "answer": "yes"}
{"id": 1764250917309631630, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: quentin, frank, xena, liam, bob, and vic. There are 6 items/roles: sander, ratchet, wrench, pliers, knead, and nibbler. Currently, frank is assigned sander, bob is assigned ratchet, vic is assigned knead, xena is assigned wrench, liam is assigned nibbler, and quentin is assigned pliers.", "question": "Is it possible to transition to a state where the following holds: frank is assigned wrench and quentin is assigned ratchet?", "answer": "yes"}
{"id": 1624672500270803855, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: alice, xena, heidi, carol, dave, zoe, michelle, and vic. There are 8 items/roles: zebra, necklace, guitar, frisbee, whale, slinky, iceskates, and quadcopter. Currently, dave is assigned whale, michelle is assigned iceskates, alice is assigned slinky, vic is assigned necklace, heidi is assigned quadcopter, xena is assigned frisbee, zoe is assigned zebra, and carol is assigned guitar.", "question": "Is it possible to transition to a state where the following holds: xena is assigned guitar and xena is assigned zebra?", "answer": "no"}
{"id": 7343057538436520425, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: quentin, frank, xena, liam, bob, and vic. There are 6 items/roles: sander, ratchet, wrench, pliers, knead, and nibbler. Currently, frank is assigned pliers, vic is assigned nibbler, quentin is assigned ratchet, bob is assigned wrench, xena is assigned knead, and liam is assigned sander.", "question": "Is it possible to transition to a state where the following holds: bob is assigned pliers and liam is assigned pliers?", "answer": "no"}
