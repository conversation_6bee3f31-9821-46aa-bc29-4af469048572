{"id": -2510868274308902866, "group": "reachable_atom_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 4 agents: bob, vic, zoe, and steve. There are 4 items/roles: book04, book01, book03, and book02. Currently, bob is assigned book04, steve is assigned book03, vic is assigned book01, and zoe is assigned book02. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-4-12)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects bob steve vic zoe - agent book01 book02 book03 book04 - role)\n    (:init (assigned bob book04) (assigned steve book03) (assigned vic book01) (assigned zoe book02) (not-eq bob steve) (not-eq bob vic) (not-eq bob zoe) (not-eq book01 book02) (not-eq book01 book03) (not-eq book01 book04) (not-eq book02 book01) (not-eq book02 book03) (not-eq book02 book04) (not-eq book03 book01) (not-eq book03 book02) (not-eq book03 book04) (not-eq book04 book01) (not-eq book04 book02) (not-eq book04 book03) (not-eq steve bob) (not-eq steve vic) (not-eq steve zoe) (not-eq vic bob) (not-eq vic steve) (not-eq vic zoe) (not-eq zoe bob) (not-eq zoe steve) (not-eq zoe vic))\n    (:goal (and (assigned bob book02) (assigned steve book03) (assigned vic book04) (assigned zoe book01)))\n)"}
{"id": 4736602248571388443, "group": "reachable_atom_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 6 agents: frank, quentin, xena, liam, bob, and vic. There are 6 items/roles: sander, pliers, wrench, knead, ratchet, and nibbler. Currently, frank is assigned ratchet, quentin is assigned nibbler, liam is assigned knead, xena is assigned wrench, vic is assigned sander, and bob is assigned pliers. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-6-20)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects bob frank liam quentin vic xena - agent knead nibbler pliers ratchet sander wrench - role)\n    (:init (assigned bob pliers) (assigned frank ratchet) (assigned liam knead) (assigned quentin nibbler) (assigned vic sander) (assigned xena wrench) (not-eq bob frank) (not-eq bob liam) (not-eq bob quentin) (not-eq bob vic) (not-eq bob xena) (not-eq frank bob) (not-eq frank liam) (not-eq frank quentin) (not-eq frank vic) (not-eq frank xena) (not-eq knead nibbler) (not-eq knead pliers) (not-eq knead ratchet) (not-eq knead sander) (not-eq knead wrench) (not-eq liam bob) (not-eq liam frank) (not-eq liam quentin) (not-eq liam vic) (not-eq liam xena) (not-eq nibbler knead) (not-eq nibbler pliers) (not-eq nibbler ratchet) (not-eq nibbler sander) (not-eq nibbler wrench) (not-eq pliers knead) (not-eq pliers nibbler) (not-eq pliers ratchet) (not-eq pliers sander) (not-eq pliers wrench) (not-eq quentin bob) (not-eq quentin frank) (not-eq quentin liam) (not-eq quentin vic) (not-eq quentin xena) (not-eq ratchet knead) (not-eq ratchet nibbler) (not-eq ratchet pliers) (not-eq ratchet sander) (not-eq ratchet wrench) (not-eq sander knead) (not-eq sander nibbler) (not-eq sander pliers) (not-eq sander ratchet) (not-eq sander wrench) (not-eq vic bob) (not-eq vic frank) (not-eq vic liam) (not-eq vic quentin) (not-eq vic xena) (not-eq wrench knead) (not-eq wrench nibbler) (not-eq wrench pliers) (not-eq wrench ratchet) (not-eq wrench sander) (not-eq xena bob) (not-eq xena frank) (not-eq xena liam) (not-eq xena quentin) (not-eq xena vic))\n    (:goal (and (assigned quentin knead) (assigned liam sander) (assigned frank pliers) (assigned xena wrench) (assigned bob nibbler) (assigned vic ratchet)))\n)"}
{"id": 7825435938111468796, "group": "reachable_atom_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: heidi, alice, xena, kevin, bob, dave, and ted. There are 7 items/roles: quince, valerian, yam, ulluco, leek, parsnip, and mushroom. Currently, dave is assigned leek, bob is assigned yam, ted is assigned ulluco, kevin is assigned quince, heidi is assigned mushroom, alice is assigned valerian, and xena is assigned parsnip. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice bob dave heidi kevin ted xena - agent leek mushroom parsnip quince ulluco valerian yam - role)\n    (:init (assigned alice valerian) (assigned bob yam) (assigned dave leek) (assigned heidi mushroom) (assigned kevin quince) (assigned ted ulluco) (assigned xena parsnip) (not-eq alice bob) (not-eq alice dave) (not-eq alice heidi) (not-eq alice kevin) (not-eq alice ted) (not-eq alice xena) (not-eq bob alice) (not-eq bob dave) (not-eq bob heidi) (not-eq bob kevin) (not-eq bob ted) (not-eq bob xena) (not-eq dave alice) (not-eq dave bob) (not-eq dave heidi) (not-eq dave kevin) (not-eq dave ted) (not-eq dave xena) (not-eq heidi alice) (not-eq heidi bob) (not-eq heidi dave) (not-eq heidi kevin) (not-eq heidi ted) (not-eq heidi xena) (not-eq kevin alice) (not-eq kevin bob) (not-eq kevin dave) (not-eq kevin heidi) (not-eq kevin ted) (not-eq kevin xena) (not-eq leek mushroom) (not-eq leek parsnip) (not-eq leek quince) (not-eq leek ulluco) (not-eq leek valerian) (not-eq leek yam) (not-eq mushroom leek) (not-eq mushroom parsnip) (not-eq mushroom quince) (not-eq mushroom ulluco) (not-eq mushroom valerian) (not-eq mushroom yam) (not-eq parsnip leek) (not-eq parsnip mushroom) (not-eq parsnip quince) (not-eq parsnip ulluco) (not-eq parsnip valerian) (not-eq parsnip yam) (not-eq quince leek) (not-eq quince mushroom) (not-eq quince parsnip) (not-eq quince ulluco) (not-eq quince valerian) (not-eq quince yam) (not-eq ted alice) (not-eq ted bob) (not-eq ted dave) (not-eq ted heidi) (not-eq ted kevin) (not-eq ted xena) (not-eq ulluco leek) (not-eq ulluco mushroom) (not-eq ulluco parsnip) (not-eq ulluco quince) (not-eq ulluco valerian) (not-eq ulluco yam) (not-eq valerian leek) (not-eq valerian mushroom) (not-eq valerian parsnip) (not-eq valerian quince) (not-eq valerian ulluco) (not-eq valerian yam) (not-eq xena alice) (not-eq xena bob) (not-eq xena dave) (not-eq xena heidi) (not-eq xena kevin) (not-eq xena ted) (not-eq yam leek) (not-eq yam mushroom) (not-eq yam parsnip) (not-eq yam quince) (not-eq yam ulluco) (not-eq yam valerian))\n    (:goal (and (assigned xena quince) (assigned heidi mushroom) (assigned kevin yam) (assigned alice valerian) (assigned dave ulluco) (assigned ted leek) (assigned bob parsnip)))\n)"}
{"id": 125634935073935494, "group": "reachable_atom_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: heidi, alice, xena, dave, zoe, michelle, vic, and carol. There are 8 items/roles: slinky, zebra, guitar, necklace, frisbee, quadcopter, iceskates, and whale. Currently, alice is assigned guitar, carol is assigned iceskates, michelle is assigned zebra, heidi is assigned necklace, vic is assigned quadcopter, zoe is assigned frisbee, dave is assigned slinky, and xena is assigned whale. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice carol dave heidi michelle vic xena zoe - agent frisbee guitar iceskates necklace quadcopter slinky whale zebra - role)\n    (:init (assigned alice guitar) (assigned carol iceskates) (assigned dave slinky) (assigned heidi necklace) (assigned michelle zebra) (assigned vic quadcopter) (assigned xena whale) (assigned zoe frisbee) (not-eq alice carol) (not-eq alice dave) (not-eq alice heidi) (not-eq alice michelle) (not-eq alice vic) (not-eq alice xena) (not-eq alice zoe) (not-eq carol alice) (not-eq carol dave) (not-eq carol heidi) (not-eq carol michelle) (not-eq carol vic) (not-eq carol xena) (not-eq carol zoe) (not-eq dave alice) (not-eq dave carol) (not-eq dave heidi) (not-eq dave michelle) (not-eq dave vic) (not-eq dave xena) (not-eq dave zoe) (not-eq frisbee guitar) (not-eq frisbee iceskates) (not-eq frisbee necklace) (not-eq frisbee quadcopter) (not-eq frisbee slinky) (not-eq frisbee whale) (not-eq frisbee zebra) (not-eq guitar frisbee) (not-eq guitar iceskates) (not-eq guitar necklace) (not-eq guitar quadcopter) (not-eq guitar slinky) (not-eq guitar whale) (not-eq guitar zebra) (not-eq heidi alice) (not-eq heidi carol) (not-eq heidi dave) (not-eq heidi michelle) (not-eq heidi vic) (not-eq heidi xena) (not-eq heidi zoe) (not-eq iceskates frisbee) (not-eq iceskates guitar) (not-eq iceskates necklace) (not-eq iceskates quadcopter) (not-eq iceskates slinky) (not-eq iceskates whale) (not-eq iceskates zebra) (not-eq michelle alice) (not-eq michelle carol) (not-eq michelle dave) (not-eq michelle heidi) (not-eq michelle vic) (not-eq michelle xena) (not-eq michelle zoe) (not-eq necklace frisbee) (not-eq necklace guitar) (not-eq necklace iceskates) (not-eq necklace quadcopter) (not-eq necklace slinky) (not-eq necklace whale) (not-eq necklace zebra) (not-eq quadcopter frisbee) (not-eq quadcopter guitar) (not-eq quadcopter iceskates) (not-eq quadcopter necklace) (not-eq quadcopter slinky) (not-eq quadcopter whale) (not-eq quadcopter zebra) (not-eq slinky frisbee) (not-eq slinky guitar) (not-eq slinky iceskates) (not-eq slinky necklace) (not-eq slinky quadcopter) (not-eq slinky whale) (not-eq slinky zebra) (not-eq vic alice) (not-eq vic carol) (not-eq vic dave) (not-eq vic heidi) (not-eq vic michelle) (not-eq vic xena) (not-eq vic zoe) (not-eq whale frisbee) (not-eq whale guitar) (not-eq whale iceskates) (not-eq whale necklace) (not-eq whale quadcopter) (not-eq whale slinky) (not-eq whale zebra) (not-eq xena alice) (not-eq xena carol) (not-eq xena dave) (not-eq xena heidi) (not-eq xena michelle) (not-eq xena vic) (not-eq xena zoe) (not-eq zebra frisbee) (not-eq zebra guitar) (not-eq zebra iceskates) (not-eq zebra necklace) (not-eq zebra quadcopter) (not-eq zebra slinky) (not-eq zebra whale) (not-eq zoe alice) (not-eq zoe carol) (not-eq zoe dave) (not-eq zoe heidi) (not-eq zoe michelle) (not-eq zoe vic) (not-eq zoe xena))\n    (:goal (and (assigned xena slinky) (assigned dave iceskates) (assigned alice zebra) (assigned michelle quadcopter) (assigned vic necklace) (assigned heidi guitar) (assigned carol frisbee) (assigned zoe whale)))\n)"}
{"id": -3292605887638611916, "group": "reachable_atom_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: heidi, alice, xena, kevin, bob, dave, and ted. There are 7 items/roles: quince, valerian, yam, ulluco, leek, parsnip, and mushroom. Currently, xena is assigned quince, heidi is assigned yam, dave is assigned leek, bob is assigned parsnip, ted is assigned valerian, kevin is assigned mushroom, and alice is assigned ulluco. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice bob dave heidi kevin ted xena - agent leek mushroom parsnip quince ulluco valerian yam - role)\n    (:init (assigned alice ulluco) (assigned bob parsnip) (assigned dave leek) (assigned heidi yam) (assigned kevin mushroom) (assigned ted valerian) (assigned xena quince) (not-eq alice bob) (not-eq alice dave) (not-eq alice heidi) (not-eq alice kevin) (not-eq alice ted) (not-eq alice xena) (not-eq bob alice) (not-eq bob dave) (not-eq bob heidi) (not-eq bob kevin) (not-eq bob ted) (not-eq bob xena) (not-eq dave alice) (not-eq dave bob) (not-eq dave heidi) (not-eq dave kevin) (not-eq dave ted) (not-eq dave xena) (not-eq heidi alice) (not-eq heidi bob) (not-eq heidi dave) (not-eq heidi kevin) (not-eq heidi ted) (not-eq heidi xena) (not-eq kevin alice) (not-eq kevin bob) (not-eq kevin dave) (not-eq kevin heidi) (not-eq kevin ted) (not-eq kevin xena) (not-eq leek mushroom) (not-eq leek parsnip) (not-eq leek quince) (not-eq leek ulluco) (not-eq leek valerian) (not-eq leek yam) (not-eq mushroom leek) (not-eq mushroom parsnip) (not-eq mushroom quince) (not-eq mushroom ulluco) (not-eq mushroom valerian) (not-eq mushroom yam) (not-eq parsnip leek) (not-eq parsnip mushroom) (not-eq parsnip quince) (not-eq parsnip ulluco) (not-eq parsnip valerian) (not-eq parsnip yam) (not-eq quince leek) (not-eq quince mushroom) (not-eq quince parsnip) (not-eq quince ulluco) (not-eq quince valerian) (not-eq quince yam) (not-eq ted alice) (not-eq ted bob) (not-eq ted dave) (not-eq ted heidi) (not-eq ted kevin) (not-eq ted xena) (not-eq ulluco leek) (not-eq ulluco mushroom) (not-eq ulluco parsnip) (not-eq ulluco quince) (not-eq ulluco valerian) (not-eq ulluco yam) (not-eq valerian leek) (not-eq valerian mushroom) (not-eq valerian parsnip) (not-eq valerian quince) (not-eq valerian ulluco) (not-eq valerian yam) (not-eq xena alice) (not-eq xena bob) (not-eq xena dave) (not-eq xena heidi) (not-eq xena kevin) (not-eq xena ted) (not-eq yam leek) (not-eq yam mushroom) (not-eq yam parsnip) (not-eq yam quince) (not-eq yam ulluco) (not-eq yam valerian))\n    (:goal (and (assigned xena quince) (assigned heidi mushroom) (assigned kevin yam) (assigned alice valerian) (assigned dave ulluco) (assigned ted leek) (assigned bob parsnip)))\n)"}
{"id": -3508976289369940857, "group": "reachable_atom_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 4 agents: bob, vic, zoe, and steve. There are 4 items/roles: book04, book01, book03, and book02. Currently, zoe is assigned book02, bob is assigned book03, steve is assigned book01, and vic is assigned book04. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-4-12)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects bob steve vic zoe - agent book01 book02 book03 book04 - role)\n    (:init (assigned bob book03) (assigned steve book01) (assigned vic book04) (assigned zoe book02) (not-eq bob steve) (not-eq bob vic) (not-eq bob zoe) (not-eq book01 book02) (not-eq book01 book03) (not-eq book01 book04) (not-eq book02 book01) (not-eq book02 book03) (not-eq book02 book04) (not-eq book03 book01) (not-eq book03 book02) (not-eq book03 book04) (not-eq book04 book01) (not-eq book04 book02) (not-eq book04 book03) (not-eq steve bob) (not-eq steve vic) (not-eq steve zoe) (not-eq vic bob) (not-eq vic steve) (not-eq vic zoe) (not-eq zoe bob) (not-eq zoe steve) (not-eq zoe vic))\n    (:goal (and (assigned bob book02) (assigned steve book03) (assigned vic book04) (assigned zoe book01)))\n)"}
{"id": 5056726055485026310, "group": "reachable_atom_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: heidi, alice, xena, kevin, bob, dave, and ted. There are 7 items/roles: quince, valerian, yam, ulluco, leek, parsnip, and mushroom. Currently, xena is assigned leek, ted is assigned parsnip, dave is assigned ulluco, bob is assigned quince, kevin is assigned yam, heidi is assigned mushroom, and alice is assigned valerian. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice bob dave heidi kevin ted xena - agent leek mushroom parsnip quince ulluco valerian yam - role)\n    (:init (assigned alice valerian) (assigned bob quince) (assigned dave ulluco) (assigned heidi mushroom) (assigned kevin yam) (assigned ted parsnip) (assigned xena leek) (not-eq alice bob) (not-eq alice dave) (not-eq alice heidi) (not-eq alice kevin) (not-eq alice ted) (not-eq alice xena) (not-eq bob alice) (not-eq bob dave) (not-eq bob heidi) (not-eq bob kevin) (not-eq bob ted) (not-eq bob xena) (not-eq dave alice) (not-eq dave bob) (not-eq dave heidi) (not-eq dave kevin) (not-eq dave ted) (not-eq dave xena) (not-eq heidi alice) (not-eq heidi bob) (not-eq heidi dave) (not-eq heidi kevin) (not-eq heidi ted) (not-eq heidi xena) (not-eq kevin alice) (not-eq kevin bob) (not-eq kevin dave) (not-eq kevin heidi) (not-eq kevin ted) (not-eq kevin xena) (not-eq leek mushroom) (not-eq leek parsnip) (not-eq leek quince) (not-eq leek ulluco) (not-eq leek valerian) (not-eq leek yam) (not-eq mushroom leek) (not-eq mushroom parsnip) (not-eq mushroom quince) (not-eq mushroom ulluco) (not-eq mushroom valerian) (not-eq mushroom yam) (not-eq parsnip leek) (not-eq parsnip mushroom) (not-eq parsnip quince) (not-eq parsnip ulluco) (not-eq parsnip valerian) (not-eq parsnip yam) (not-eq quince leek) (not-eq quince mushroom) (not-eq quince parsnip) (not-eq quince ulluco) (not-eq quince valerian) (not-eq quince yam) (not-eq ted alice) (not-eq ted bob) (not-eq ted dave) (not-eq ted heidi) (not-eq ted kevin) (not-eq ted xena) (not-eq ulluco leek) (not-eq ulluco mushroom) (not-eq ulluco parsnip) (not-eq ulluco quince) (not-eq ulluco valerian) (not-eq ulluco yam) (not-eq valerian leek) (not-eq valerian mushroom) (not-eq valerian parsnip) (not-eq valerian quince) (not-eq valerian ulluco) (not-eq valerian yam) (not-eq xena alice) (not-eq xena bob) (not-eq xena dave) (not-eq xena heidi) (not-eq xena kevin) (not-eq xena ted) (not-eq yam leek) (not-eq yam mushroom) (not-eq yam parsnip) (not-eq yam quince) (not-eq yam ulluco) (not-eq yam valerian))\n    (:goal (and (assigned xena quince) (assigned heidi mushroom) (assigned kevin yam) (assigned alice valerian) (assigned dave ulluco) (assigned ted leek) (assigned bob parsnip)))\n)"}
{"id": -8655383456330860649, "group": "reachable_atom_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: heidi, alice, xena, dave, zoe, michelle, vic, and carol. There are 8 items/roles: slinky, zebra, guitar, necklace, frisbee, quadcopter, iceskates, and whale. Currently, vic is assigned guitar, xena is assigned quadcopter, zoe is assigned whale, michelle is assigned iceskates, heidi is assigned necklace, alice is assigned zebra, carol is assigned frisbee, and dave is assigned slinky. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice carol dave heidi michelle vic xena zoe - agent frisbee guitar iceskates necklace quadcopter slinky whale zebra - role)\n    (:init (assigned alice zebra) (assigned carol frisbee) (assigned dave slinky) (assigned heidi necklace) (assigned michelle iceskates) (assigned vic guitar) (assigned xena quadcopter) (assigned zoe whale) (not-eq alice carol) (not-eq alice dave) (not-eq alice heidi) (not-eq alice michelle) (not-eq alice vic) (not-eq alice xena) (not-eq alice zoe) (not-eq carol alice) (not-eq carol dave) (not-eq carol heidi) (not-eq carol michelle) (not-eq carol vic) (not-eq carol xena) (not-eq carol zoe) (not-eq dave alice) (not-eq dave carol) (not-eq dave heidi) (not-eq dave michelle) (not-eq dave vic) (not-eq dave xena) (not-eq dave zoe) (not-eq frisbee guitar) (not-eq frisbee iceskates) (not-eq frisbee necklace) (not-eq frisbee quadcopter) (not-eq frisbee slinky) (not-eq frisbee whale) (not-eq frisbee zebra) (not-eq guitar frisbee) (not-eq guitar iceskates) (not-eq guitar necklace) (not-eq guitar quadcopter) (not-eq guitar slinky) (not-eq guitar whale) (not-eq guitar zebra) (not-eq heidi alice) (not-eq heidi carol) (not-eq heidi dave) (not-eq heidi michelle) (not-eq heidi vic) (not-eq heidi xena) (not-eq heidi zoe) (not-eq iceskates frisbee) (not-eq iceskates guitar) (not-eq iceskates necklace) (not-eq iceskates quadcopter) (not-eq iceskates slinky) (not-eq iceskates whale) (not-eq iceskates zebra) (not-eq michelle alice) (not-eq michelle carol) (not-eq michelle dave) (not-eq michelle heidi) (not-eq michelle vic) (not-eq michelle xena) (not-eq michelle zoe) (not-eq necklace frisbee) (not-eq necklace guitar) (not-eq necklace iceskates) (not-eq necklace quadcopter) (not-eq necklace slinky) (not-eq necklace whale) (not-eq necklace zebra) (not-eq quadcopter frisbee) (not-eq quadcopter guitar) (not-eq quadcopter iceskates) (not-eq quadcopter necklace) (not-eq quadcopter slinky) (not-eq quadcopter whale) (not-eq quadcopter zebra) (not-eq slinky frisbee) (not-eq slinky guitar) (not-eq slinky iceskates) (not-eq slinky necklace) (not-eq slinky quadcopter) (not-eq slinky whale) (not-eq slinky zebra) (not-eq vic alice) (not-eq vic carol) (not-eq vic dave) (not-eq vic heidi) (not-eq vic michelle) (not-eq vic xena) (not-eq vic zoe) (not-eq whale frisbee) (not-eq whale guitar) (not-eq whale iceskates) (not-eq whale necklace) (not-eq whale quadcopter) (not-eq whale slinky) (not-eq whale zebra) (not-eq xena alice) (not-eq xena carol) (not-eq xena dave) (not-eq xena heidi) (not-eq xena michelle) (not-eq xena vic) (not-eq xena zoe) (not-eq zebra frisbee) (not-eq zebra guitar) (not-eq zebra iceskates) (not-eq zebra necklace) (not-eq zebra quadcopter) (not-eq zebra slinky) (not-eq zebra whale) (not-eq zoe alice) (not-eq zoe carol) (not-eq zoe dave) (not-eq zoe heidi) (not-eq zoe michelle) (not-eq zoe vic) (not-eq zoe xena))\n    (:goal (and (assigned xena slinky) (assigned dave iceskates) (assigned alice zebra) (assigned michelle quadcopter) (assigned vic necklace) (assigned heidi guitar) (assigned carol frisbee) (assigned zoe whale)))\n)"}
{"id": -8270037416850250563, "group": "reachable_atom_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 7 agents: heidi, alice, xena, kevin, bob, dave, and ted. There are 7 items/roles: quince, valerian, yam, ulluco, leek, parsnip, and mushroom. Currently, dave is assigned valerian, xena is assigned quince, ted is assigned parsnip, kevin is assigned yam, alice is assigned ulluco, heidi is assigned mushroom, and bob is assigned leek. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-7-28)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice bob dave heidi kevin ted xena - agent leek mushroom parsnip quince ulluco valerian yam - role)\n    (:init (assigned alice ulluco) (assigned bob leek) (assigned dave valerian) (assigned heidi mushroom) (assigned kevin yam) (assigned ted parsnip) (assigned xena quince) (not-eq alice bob) (not-eq alice dave) (not-eq alice heidi) (not-eq alice kevin) (not-eq alice ted) (not-eq alice xena) (not-eq bob alice) (not-eq bob dave) (not-eq bob heidi) (not-eq bob kevin) (not-eq bob ted) (not-eq bob xena) (not-eq dave alice) (not-eq dave bob) (not-eq dave heidi) (not-eq dave kevin) (not-eq dave ted) (not-eq dave xena) (not-eq heidi alice) (not-eq heidi bob) (not-eq heidi dave) (not-eq heidi kevin) (not-eq heidi ted) (not-eq heidi xena) (not-eq kevin alice) (not-eq kevin bob) (not-eq kevin dave) (not-eq kevin heidi) (not-eq kevin ted) (not-eq kevin xena) (not-eq leek mushroom) (not-eq leek parsnip) (not-eq leek quince) (not-eq leek ulluco) (not-eq leek valerian) (not-eq leek yam) (not-eq mushroom leek) (not-eq mushroom parsnip) (not-eq mushroom quince) (not-eq mushroom ulluco) (not-eq mushroom valerian) (not-eq mushroom yam) (not-eq parsnip leek) (not-eq parsnip mushroom) (not-eq parsnip quince) (not-eq parsnip ulluco) (not-eq parsnip valerian) (not-eq parsnip yam) (not-eq quince leek) (not-eq quince mushroom) (not-eq quince parsnip) (not-eq quince ulluco) (not-eq quince valerian) (not-eq quince yam) (not-eq ted alice) (not-eq ted bob) (not-eq ted dave) (not-eq ted heidi) (not-eq ted kevin) (not-eq ted xena) (not-eq ulluco leek) (not-eq ulluco mushroom) (not-eq ulluco parsnip) (not-eq ulluco quince) (not-eq ulluco valerian) (not-eq ulluco yam) (not-eq valerian leek) (not-eq valerian mushroom) (not-eq valerian parsnip) (not-eq valerian quince) (not-eq valerian ulluco) (not-eq valerian yam) (not-eq xena alice) (not-eq xena bob) (not-eq xena dave) (not-eq xena heidi) (not-eq xena kevin) (not-eq xena ted) (not-eq yam leek) (not-eq yam mushroom) (not-eq yam parsnip) (not-eq yam quince) (not-eq yam ulluco) (not-eq yam valerian))\n    (:goal (and (assigned xena quince) (assigned heidi mushroom) (assigned kevin yam) (assigned alice valerian) (assigned dave ulluco) (assigned ted leek) (assigned bob parsnip)))\n)"}
{"id": -7719466170715395092, "group": "reachable_atom_gen", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. There are 8 agents: heidi, alice, xena, dave, zoe, michelle, vic, and carol. There are 8 items/roles: slinky, zebra, guitar, necklace, frisbee, quadcopter, iceskates, and whale. Currently, xena is assigned slinky, zoe is assigned whale, dave is assigned quadcopter, michelle is assigned necklace, alice is assigned zebra, vic is assigned iceskates, carol is assigned frisbee, and heidi is assigned guitar. The available propositions are: (assigned ?a ?r) - ?a is assigned ?r.", "question": "What proposition can never hold in any potentially reachable state?", "answer": [], "PDDL_domain": "(define (domain swap)\n    (:requirements :typing)\n    (:types agent role - object)\n    (:predicates (assigned ?a - agent ?r - role)  (not-eq ?x - object ?y - object))\n    (:action swap\n        :parameters (?a1 - agent ?a2 - agent ?r1 - role ?r2 - role)\n        :precondition (and (assigned ?a1 ?r1) (assigned ?a2 ?r2) (not-eq ?a1 ?a2) (not-eq ?r1 ?r2))\n        :effect (and (not (assigned ?a1 ?r1)) (not (assigned ?a2 ?r2)) (assigned ?a1 ?r2) (assigned ?a2 ?r1))\n    )\n)", "PDDL_problem": "(define (problem swap-8-36)\n    (:domain swap)\n    (:requirements :typing)\n    (:objects alice carol dave heidi michelle vic xena zoe - agent frisbee guitar iceskates necklace quadcopter slinky whale zebra - role)\n    (:init (assigned alice zebra) (assigned carol frisbee) (assigned dave quadcopter) (assigned heidi guitar) (assigned michelle necklace) (assigned vic iceskates) (assigned xena slinky) (assigned zoe whale) (not-eq alice carol) (not-eq alice dave) (not-eq alice heidi) (not-eq alice michelle) (not-eq alice vic) (not-eq alice xena) (not-eq alice zoe) (not-eq carol alice) (not-eq carol dave) (not-eq carol heidi) (not-eq carol michelle) (not-eq carol vic) (not-eq carol xena) (not-eq carol zoe) (not-eq dave alice) (not-eq dave carol) (not-eq dave heidi) (not-eq dave michelle) (not-eq dave vic) (not-eq dave xena) (not-eq dave zoe) (not-eq frisbee guitar) (not-eq frisbee iceskates) (not-eq frisbee necklace) (not-eq frisbee quadcopter) (not-eq frisbee slinky) (not-eq frisbee whale) (not-eq frisbee zebra) (not-eq guitar frisbee) (not-eq guitar iceskates) (not-eq guitar necklace) (not-eq guitar quadcopter) (not-eq guitar slinky) (not-eq guitar whale) (not-eq guitar zebra) (not-eq heidi alice) (not-eq heidi carol) (not-eq heidi dave) (not-eq heidi michelle) (not-eq heidi vic) (not-eq heidi xena) (not-eq heidi zoe) (not-eq iceskates frisbee) (not-eq iceskates guitar) (not-eq iceskates necklace) (not-eq iceskates quadcopter) (not-eq iceskates slinky) (not-eq iceskates whale) (not-eq iceskates zebra) (not-eq michelle alice) (not-eq michelle carol) (not-eq michelle dave) (not-eq michelle heidi) (not-eq michelle vic) (not-eq michelle xena) (not-eq michelle zoe) (not-eq necklace frisbee) (not-eq necklace guitar) (not-eq necklace iceskates) (not-eq necklace quadcopter) (not-eq necklace slinky) (not-eq necklace whale) (not-eq necklace zebra) (not-eq quadcopter frisbee) (not-eq quadcopter guitar) (not-eq quadcopter iceskates) (not-eq quadcopter necklace) (not-eq quadcopter slinky) (not-eq quadcopter whale) (not-eq quadcopter zebra) (not-eq slinky frisbee) (not-eq slinky guitar) (not-eq slinky iceskates) (not-eq slinky necklace) (not-eq slinky quadcopter) (not-eq slinky whale) (not-eq slinky zebra) (not-eq vic alice) (not-eq vic carol) (not-eq vic dave) (not-eq vic heidi) (not-eq vic michelle) (not-eq vic xena) (not-eq vic zoe) (not-eq whale frisbee) (not-eq whale guitar) (not-eq whale iceskates) (not-eq whale necklace) (not-eq whale quadcopter) (not-eq whale slinky) (not-eq whale zebra) (not-eq xena alice) (not-eq xena carol) (not-eq xena dave) (not-eq xena heidi) (not-eq xena michelle) (not-eq xena vic) (not-eq xena zoe) (not-eq zebra frisbee) (not-eq zebra guitar) (not-eq zebra iceskates) (not-eq zebra necklace) (not-eq zebra quadcopter) (not-eq zebra slinky) (not-eq zebra whale) (not-eq zoe alice) (not-eq zoe carol) (not-eq zoe dave) (not-eq zoe heidi) (not-eq zoe michelle) (not-eq zoe vic) (not-eq zoe xena))\n    (:goal (and (assigned xena slinky) (assigned dave iceskates) (assigned alice zebra) (assigned michelle quadcopter) (assigned vic necklace) (assigned heidi guitar) (assigned carol frisbee) (assigned zoe whale)))\n)"}
