{"id": -6075815408890411444, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x0-y3, loc-x2-y3, loc-x1-y3, loc-x3-y0, loc-x3-y3, loc-x2-y0, loc-x2-y2, loc-x1-y2, loc-x3-y2, loc-x0-y1, loc-x2-y1, loc-x0-y2, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. push box to place loc-x2-y0 from place loc-x3-y0. B. push box to place loc-x0-y2 from place loc-x1-y2. C. push box to place loc-x2-y2 from place loc-x2-y3. D. move from place loc-x2-y2 to place loc-x3-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["push box to place loc-x2-y0 from place loc-x3-y0", "push box to place loc-x0-y2 from place loc-x1-y2", "push box to place loc-x2-y2 from place loc-x2-y3", "move from place loc-x2-y2 to place loc-x3-y2"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -3511075786723286968, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y2, loc-x2-y3, and loc-x0-y4. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x3-y1, loc-x3-y0, loc-x1-y0, loc-x2-y0, loc-x2-y2, loc-x0-y0, loc-x3-y2, loc-x0-y1, loc-x2-y1, loc-x0-y2, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. push box to place loc-x3-y0 from place loc-x2-y0. B. move from place loc-x3-y0 to place loc-x3-y1. C. push box to place loc-x2-y2 from place loc-x3-y2. D. push box to place loc-x3-y2 from place loc-x3-y3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["push box to place loc-x3-y0 from place loc-x2-y0", "move from place loc-x3-y0 to place loc-x3-y1", "push box to place loc-x2-y2 from place loc-x3-y2", "push box to place loc-x3-y2 from place loc-x3-y3"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 924450961983325525, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x3-y1.The following places have been visited: loc-x3-y1, loc-x3-y0, loc-x1-y0, loc-x2-y0, loc-x0-y0, loc-x0-y1, and loc-x0-y2.", "question": "Which of the following actions can eventually be applied? A. check that position loc-x2-y1 is connected to position loc-x3-y1. B. travel from loc-x3-y2 to loc-x3-y3. C. check that position loc-x0-y0 is connected to position loc-x1-y0. D. check that position loc-x1-y3 is connected to position loc-x1-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["check that position loc-x2-y1 is connected to position loc-x3-y1", "travel from loc-x3-y2 to loc-x3-y3", "check that position loc-x0-y0 is connected to position loc-x1-y0", "check that position loc-x1-y3 is connected to position loc-x1-y2"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -7147522314818090201, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x1-y0, loc-x0-y2, and loc-x3-y3. Currently, the robot is in place loc-x2-y1.The following places have been visited: loc-x0-y3, loc-x2-y3, loc-x1-y3, loc-x3-y0, loc-x2-y2, loc-x1-y2, loc-x3-y2, loc-x0-y1, loc-x2-y1, loc-x2-y0, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. transition from the current position loc-x1-y2 to the next position loc-x1-y3. B. push box to place loc-x2-y1 from place loc-x1-y1. C. push box to place loc-x0-y1 from place loc-x0-y0. D. push box to place loc-x2-y1 from place loc-x2-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transition from the current position loc-x1-y2 to the next position loc-x1-y3", "push box to place loc-x2-y1 from place loc-x1-y1", "push box to place loc-x0-y1 from place loc-x0-y0", "push box to place loc-x2-y1 from place loc-x2-y0"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 969811954434223403, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x0-y4, loc-x3-y3, loc-x1-y4, loc-x2-y1, loc-x0-y3, loc-x1-y0, loc-x2-y4, loc-x2-y0, loc-x0-y2, loc-x1-y1, loc-x3-y0, loc-x1-y3, loc-x1-y2, loc-x0-y0, loc-x3-y2, loc-x0-y1, loc-x3-y1, and loc-x3-y4.", "question": "Which of the following actions can eventually be applied? A. move to place loc-x2-y0 from place loc-x3-y0. B. push box to place loc-x1-y4 from place loc-x0-y4. C. push box to place loc-x1-y0 from place loc-x1-y1. D. push box to place loc-x1-y1 from place loc-x0-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move to place loc-x2-y0 from place loc-x3-y0", "push box to place loc-x1-y4 from place loc-x0-y4", "push box to place loc-x1-y0 from place loc-x1-y1", "push box to place loc-x1-y1 from place loc-x0-y1"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 3878761865268448015, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x1-y2.The following places have been visited: loc-x3-y0, loc-x1-y2, loc-x2-y1, loc-x0-y1, loc-x2-y0, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. navigate from loc-x3-y3 to loc-x3-y2. B. push box to place loc-x2-y1 from place loc-x2-y2. C. push box to place loc-x2-y2 from place loc-x1-y2. D. push box to place loc-x0-y3 from place loc-x0-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["navigate from loc-x3-y3 to loc-x3-y2", "push box to place loc-x2-y1 from place loc-x2-y2", "push box to place loc-x2-y2 from place loc-x1-y2", "push box to place loc-x0-y3 from place loc-x0-y2"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 8642872307201871194, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x0-y3, loc-x2-y3, loc-x1-y3, loc-x3-y0, loc-x3-y3, loc-x2-y0, loc-x2-y2, loc-x0-y0, loc-x3-y2, loc-x0-y1, loc-x2-y1, loc-x0-y2, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. check that position loc-x3-y2 is connected to position loc-x2-y2. B. check that position loc-x1-y3 is connected to position loc-x1-y2. C. travel from loc-x1-y1 to loc-x2-y1. D. check that position loc-x1-y1 is connected to position loc-x1-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["check that position loc-x3-y2 is connected to position loc-x2-y2", "check that position loc-x1-y3 is connected to position loc-x1-y2", "travel from loc-x1-y1 to loc-x2-y1", "check that position loc-x1-y1 is connected to position loc-x1-y2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 4342940561922644601, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x3-y1, loc-x3-y0, loc-x1-y3, loc-x1-y0, loc-x2-y0, loc-x1-y2, loc-x0-y0, loc-x0-y1, loc-x2-y1, loc-x0-y2, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. push box to place loc-x2-y4 from place loc-x3-y4. B. go to loc-x0-y4 from loc-x1-y4. C. push box to place loc-x2-y1 from place loc-x1-y1. D. push box to place loc-x1-y0 from place loc-x0-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["push box to place loc-x2-y4 from place loc-x3-y4", "go to loc-x0-y4 from loc-x1-y4", "push box to place loc-x2-y1 from place loc-x1-y1", "push box to place loc-x1-y0 from place loc-x0-y0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -6167993661751947168, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x1-y0, loc-x0-y2, and loc-x3-y3. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x3-y0, loc-x1-y3, loc-x2-y2, loc-x1-y2, loc-x2-y0, loc-x2-y1, loc-x2-y3, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. check that position loc-x1-y3 is connected to position loc-x2-y3. B. navigate from loc-x2-y2 to loc-x2-y1. C. check that position loc-x0-y0 is connected to position loc-x0-y1. D. check that position loc-x3-y0 is connected to position loc-x2-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["check that position loc-x1-y3 is connected to position loc-x2-y3", "navigate from loc-x2-y2 to loc-x2-y1", "check that position loc-x0-y0 is connected to position loc-x0-y1", "check that position loc-x3-y0 is connected to position loc-x2-y0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 62243796472324350, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y2, loc-x2-y3, and loc-x0-y4. Currently, the robot is in place loc-x0-y1.The following places have been visited: loc-x1-y0, loc-x0-y0, loc-x0-y1, loc-x0-y2, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. push box to place loc-x3-y2 from place loc-x3-y3. B. push box to place loc-x1-y1 from place loc-x2-y1. C. push box to place loc-x1-y0 from place loc-x2-y0. D. go to loc-x0-y0 from loc-x1-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["push box to place loc-x3-y2 from place loc-x3-y3", "push box to place loc-x1-y1 from place loc-x2-y1", "push box to place loc-x1-y0 from place loc-x2-y0", "go to loc-x0-y0 from loc-x1-y0"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -7006016526142761494, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3, loc-x0-y0, and loc-x3-y3. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x3-y1, loc-x2-y3, loc-x1-y3, loc-x3-y0, loc-x2-y2, loc-x1-y2, loc-x3-y2, loc-x0-y1, loc-x2-y1, loc-x0-y2, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. push box to place loc-x3-y2 from place loc-x2-y2. B. go to loc-x2-y2 from loc-x1-y2. C. push box to place loc-x0-y1 from place loc-x0-y2. D. push box to place loc-x3-y1 from place loc-x3-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["push box to place loc-x3-y2 from place loc-x2-y2", "go to loc-x2-y2 from loc-x1-y2", "push box to place loc-x0-y1 from place loc-x0-y2", "push box to place loc-x3-y1 from place loc-x3-y2"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 2532374315830691894, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x0-y0. Currently, the robot is in place loc-x1-y2.The following places have been visited: loc-x0-y3, loc-x2-y3, loc-x1-y3, loc-x3-y3, loc-x2-y2, loc-x1-y2, loc-x2-y1, loc-x0-y1, loc-x0-y2, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. check that position loc-x1-y2 is connected to position loc-x0-y2. B. check that position loc-x1-y3 is connected to position loc-x2-y3. C. check that position loc-x0-y1 is connected to position loc-x1-y1. D. move to place loc-x1-y1 from place loc-x0-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["check that position loc-x1-y2 is connected to position loc-x0-y2", "check that position loc-x1-y3 is connected to position loc-x2-y3", "check that position loc-x0-y1 is connected to position loc-x1-y1", "move to place loc-x1-y1 from place loc-x0-y1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -3177572764891637084, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y0.The following places have been visited: loc-x0-y3, loc-x3-y0, loc-x1-y3, loc-x1-y0, loc-x2-y0, loc-x1-y2, loc-x0-y0, loc-x0-y1, and loc-x0-y2.", "question": "Which of the following actions can eventually be applied? A. check that position loc-x2-y1 is connected to position loc-x1-y1. B. transition from the current position loc-x0-y1 to the next position loc-x1-y1. C. check that position loc-x3-y3 is connected to position loc-x2-y3. D. check that position loc-x1-y3 is connected to position loc-x0-y3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["check that position loc-x2-y1 is connected to position loc-x1-y1", "transition from the current position loc-x0-y1 to the next position loc-x1-y1", "check that position loc-x3-y3 is connected to position loc-x2-y3", "check that position loc-x1-y3 is connected to position loc-x0-y3"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 1841772750305831144, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3 and loc-x0-y0. Currently, the robot is in place loc-x0-y1.The following places have been visited: loc-x3-y1, loc-x3-y0, loc-x1-y0, loc-x3-y2, loc-x0-y1, loc-x3-y3, loc-x2-y0, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. check that position loc-x2-y3 is connected to position loc-x1-y3. B. check that position loc-x1-y1 is connected to position loc-x0-y1. C. check that position loc-x2-y3 is connected to position loc-x2-y2. D. travel from the current position loc-x1-y2 to the next position loc-x1-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["check that position loc-x2-y3 is connected to position loc-x1-y3", "check that position loc-x1-y1 is connected to position loc-x0-y1", "check that position loc-x2-y3 is connected to position loc-x2-y2", "travel from the current position loc-x1-y2 to the next position loc-x1-y1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 2324105307705323185, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x0-y3, loc-x2-y3, loc-x1-y3, loc-x2-y2, loc-x1-y2, and loc-x0-y2.", "question": "Which of the following actions can eventually be applied? A. push box to place loc-x1-y0 from place loc-x1-y1. B. push box to place loc-x2-y1 from place loc-x2-y2. C. move to place loc-x3-y2 from place loc-x3-y3. D. push box to place loc-x3-y0 from place loc-x3-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["push box to place loc-x1-y0 from place loc-x1-y1", "push box to place loc-x2-y1 from place loc-x2-y2", "move to place loc-x3-y2 from place loc-x3-y3", "push box to place loc-x3-y0 from place loc-x3-y1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
