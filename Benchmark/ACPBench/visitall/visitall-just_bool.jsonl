{"id": 2339363515767582098, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x3-y0.Place loc-x3-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"go to loc-x2-y0 from loc-x3-y0, go to loc-x2-y1 from loc-x2-y0, go to loc-x1-y1 from loc-x2-y1, go to loc-x1-y2 from loc-x1-y1, go to loc-x2-y2 from loc-x1-y2, go to loc-x3-y2 from loc-x2-y2, go to loc-x3-y3 from loc-x3-y2, go to loc-x2-y3 from loc-x3-y3, go to loc-x1-y3 from loc-x2-y3, go to loc-x0-y3 from loc-x1-y3, go to loc-x0-y2 from loc-x0-y3, go to loc-x0-y1 from loc-x0-y2, go to loc-x0-y0 from loc-x0-y1, go to loc-x0-y1 from loc-x0-y0, go to loc-x1-y1 from loc-x0-y1\"; can the following action be removed from this plan and still have a valid plan: go to loc-x2-y0 from loc-x3-y0?", "answer": "no"}
{"id": -139889112813956311, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x3-y1, loc-x1-y0, and loc-x0-y2. Currently, the robot is in place loc-x2-y1.Place loc-x2-y1 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"navigate from loc-x2-y1 to loc-x2-y0, navigate from loc-x2-y0 to loc-x3-y0, navigate from loc-x3-y0 to loc-x2-y0, navigate from loc-x2-y0 to loc-x2-y1, navigate from loc-x2-y1 to loc-x2-y2, navigate from loc-x2-y2 to loc-x3-y2, navigate from loc-x3-y2 to loc-x2-y2, navigate from loc-x2-y2 to loc-x2-y3, navigate from loc-x2-y3 to loc-x1-y3, navigate from loc-x1-y3 to loc-x0-y3, navigate from loc-x0-y3 to loc-x1-y3, navigate from loc-x1-y3 to loc-x2-y3, navigate from loc-x2-y3 to loc-x2-y2, navigate from loc-x2-y2 to loc-x1-y2, navigate from loc-x1-y2 to loc-x1-y1, navigate from loc-x1-y1 to loc-x0-y1, navigate from loc-x0-y1 to loc-x0-y0, navigate from loc-x0-y0 to loc-x0-y1\"; can the following action be removed from this plan and still have a valid plan: navigate from loc-x2-y3 to loc-x2-y2?", "answer": "no"}
{"id": 928900863794676562, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x3-y0.Place loc-x3-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"move to place loc-x2-y0 from place loc-x3-y0, move to place loc-x2-y1 from place loc-x2-y0, move to place loc-x2-y2 from place loc-x2-y1, move to place loc-x3-y2 from place loc-x2-y2, move to place loc-x3-y3 from place loc-x3-y2, move to place loc-x2-y3 from place loc-x3-y3, move to place loc-x3-y3 from place loc-x2-y3, move to place loc-x2-y3 from place loc-x3-y3, move to place loc-x1-y3 from place loc-x2-y3, move to place loc-x0-y3 from place loc-x1-y3, move to place loc-x0-y2 from place loc-x0-y3, move to place loc-x1-y2 from place loc-x0-y2, move to place loc-x1-y1 from place loc-x1-y2, move to place loc-x0-y1 from place loc-x1-y1, move to place loc-x0-y0 from place loc-x0-y1\"; can the following action be removed from this plan and still have a valid plan: move to place loc-x2-y1 from place loc-x2-y0?", "answer": "no"}
{"id": 4664708134759721139, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y0.Place loc-x1-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x3-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"move from place loc-x1-y0 to place loc-x2-y0, move from place loc-x2-y0 to place loc-x2-y1, move from place loc-x2-y1 to place loc-x1-y1, move from place loc-x1-y1 to place loc-x1-y0, move from place loc-x1-y0 to place loc-x0-y0, move from place loc-x0-y0 to place loc-x0-y1, move from place loc-x0-y1 to place loc-x0-y2, move from place loc-x0-y2 to place loc-x0-y3, move from place loc-x0-y3 to place loc-x1-y3, move from place loc-x1-y3 to place loc-x1-y2, move from place loc-x1-y2 to place loc-x2-y2, move from place loc-x2-y2 to place loc-x2-y3, move from place loc-x2-y3 to place loc-x3-y3, move from place loc-x3-y3 to place loc-x3-y2, move from place loc-x3-y2 to place loc-x3-y1, move from place loc-x3-y1 to place loc-x3-y0\"; can the following action be removed from this plan and still have a valid plan: move from place loc-x0-y0 to place loc-x0-y1?", "answer": "no"}
{"id": -2286539557956697782, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y0.Place loc-x1-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x3-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"move from place loc-x1-y0 to place loc-x1-y1, move from place loc-x1-y1 to place loc-x1-y2, move from place loc-x1-y2 to place loc-x2-y2, move from place loc-x2-y2 to place loc-x2-y1, move from place loc-x2-y1 to place loc-x2-y0, move from place loc-x2-y0 to place loc-x3-y0, move from place loc-x3-y0 to place loc-x3-y1, move from place loc-x3-y1 to place loc-x3-y2, move from place loc-x3-y2 to place loc-x3-y3, move from place loc-x3-y3 to place loc-x2-y3, move from place loc-x2-y3 to place loc-x1-y3, move from place loc-x1-y3 to place loc-x0-y3, move from place loc-x0-y3 to place loc-x0-y2, move from place loc-x0-y2 to place loc-x0-y1, move from place loc-x0-y1 to place loc-x0-y0, move from place loc-x0-y0 to place loc-x1-y0\"; can the following action be removed from this plan and still have a valid plan: move from place loc-x0-y0 to place loc-x1-y0?", "answer": "yes"}
{"id": 2362222126872726663, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x1-y2, and loc-x0-y4. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y4 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y4 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x1-y4 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"go to loc-x0-y3 from loc-x0-y2, go to loc-x1-y3 from loc-x0-y3, go to loc-x1-y4 from loc-x1-y3, go to loc-x2-y4 from loc-x1-y4, go to loc-x3-y4 from loc-x2-y4, go to loc-x3-y3 from loc-x3-y4, go to loc-x3-y2 from loc-x3-y3, go to loc-x2-y2 from loc-x3-y2, go to loc-x2-y1 from loc-x2-y2, go to loc-x1-y1 from loc-x2-y1, go to loc-x1-y0 from loc-x1-y1, go to loc-x0-y0 from loc-x1-y0, go to loc-x0-y1 from loc-x0-y0, go to loc-x1-y1 from loc-x0-y1, go to loc-x1-y0 from loc-x1-y1, go to loc-x2-y0 from loc-x1-y0, go to loc-x3-y0 from loc-x2-y0, go to loc-x3-y1 from loc-x3-y0\"; can the following action be removed from this plan and still have a valid plan: go to loc-x3-y0 from loc-x2-y0?", "answer": "no"}
{"id": -350564967936887876, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y0.Place loc-x1-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x3-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"transition from the current position loc-x1-y0 to the next position loc-x0-y0, transition from the current position loc-x0-y0 to the next position loc-x0-y1, transition from the current position loc-x0-y1 to the next position loc-x1-y1, transition from the current position loc-x1-y1 to the next position loc-x2-y1, transition from the current position loc-x2-y1 to the next position loc-x2-y0, transition from the current position loc-x2-y0 to the next position loc-x3-y0, transition from the current position loc-x3-y0 to the next position loc-x3-y1, transition from the current position loc-x3-y1 to the next position loc-x3-y2, transition from the current position loc-x3-y2 to the next position loc-x3-y3, transition from the current position loc-x3-y3 to the next position loc-x2-y3, transition from the current position loc-x2-y3 to the next position loc-x2-y2, transition from the current position loc-x2-y2 to the next position loc-x2-y3, transition from the current position loc-x2-y3 to the next position loc-x1-y3, transition from the current position loc-x1-y3 to the next position loc-x0-y3, transition from the current position loc-x0-y3 to the next position loc-x0-y2, transition from the current position loc-x0-y2 to the next position loc-x1-y2\"; can the following action be removed from this plan and still have a valid plan: transition from the current position loc-x0-y0 to the next position loc-x0-y1?", "answer": "no"}
{"id": -7605946517573176589, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x1-y0.Place loc-x1-y0 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x3-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"move to place loc-x0-y0 from place loc-x1-y0, move to place loc-x0-y1 from place loc-x0-y0, move to place loc-x0-y2 from place loc-x0-y1, move to place loc-x0-y3 from place loc-x0-y2, move to place loc-x1-y3 from place loc-x0-y3, move to place loc-x2-y3 from place loc-x1-y3, move to place loc-x2-y2 from place loc-x2-y3, move to place loc-x1-y2 from place loc-x2-y2, move to place loc-x1-y1 from place loc-x1-y2, move to place loc-x2-y1 from place loc-x1-y1, move to place loc-x2-y0 from place loc-x2-y1, move to place loc-x3-y0 from place loc-x2-y0, move to place loc-x3-y1 from place loc-x3-y0, move to place loc-x3-y2 from place loc-x3-y1, move to place loc-x3-y3 from place loc-x3-y2\"; can the following action be removed from this plan and still have a valid plan: move to place loc-x3-y0 from place loc-x2-y0?", "answer": "no"}
{"id": -8002491087609043275, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x1-y2, and loc-x0-y4. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y4 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y4 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x1-y4 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"transition from the current position loc-x0-y2 to the next position loc-x0-y3, transition from the current position loc-x0-y3 to the next position loc-x1-y3, transition from the current position loc-x1-y3 to the next position loc-x1-y4, transition from the current position loc-x1-y4 to the next position loc-x2-y4, transition from the current position loc-x2-y4 to the next position loc-x3-y4, transition from the current position loc-x3-y4 to the next position loc-x3-y3, transition from the current position loc-x3-y3 to the next position loc-x3-y2, transition from the current position loc-x3-y2 to the next position loc-x3-y1, transition from the current position loc-x3-y1 to the next position loc-x3-y0, transition from the current position loc-x3-y0 to the next position loc-x2-y0, transition from the current position loc-x2-y0 to the next position loc-x1-y0, transition from the current position loc-x1-y0 to the next position loc-x0-y0, transition from the current position loc-x0-y0 to the next position loc-x0-y1, transition from the current position loc-x0-y1 to the next position loc-x1-y1, transition from the current position loc-x1-y1 to the next position loc-x2-y1, transition from the current position loc-x2-y1 to the next position loc-x2-y2, transition from the current position loc-x2-y2 to the next position loc-x2-y1\"; can the following action be removed from this plan and still have a valid plan: transition from the current position loc-x3-y1 to the next position loc-x3-y0?", "answer": "no"}
{"id": 9105253077038962047, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x1-y2, and loc-x0-y4. Currently, the robot is in place loc-x0-y2.Place loc-x0-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x3-y4 has been visited, Place loc-x3-y1 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y4 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x1-y4 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"navigate from loc-x0-y2 to loc-x0-y3, navigate from loc-x0-y3 to loc-x1-y3, navigate from loc-x1-y3 to loc-x1-y4, navigate from loc-x1-y4 to loc-x2-y4, navigate from loc-x2-y4 to loc-x3-y4, navigate from loc-x3-y4 to loc-x3-y3, navigate from loc-x3-y3 to loc-x3-y2, navigate from loc-x3-y2 to loc-x2-y2, navigate from loc-x2-y2 to loc-x2-y1, navigate from loc-x2-y1 to loc-x3-y1, navigate from loc-x3-y1 to loc-x3-y0, navigate from loc-x3-y0 to loc-x2-y0, navigate from loc-x2-y0 to loc-x1-y0, navigate from loc-x1-y0 to loc-x0-y0, navigate from loc-x0-y0 to loc-x0-y1, navigate from loc-x0-y1 to loc-x1-y1, navigate from loc-x1-y1 to loc-x2-y1, navigate from loc-x2-y1 to loc-x3-y1\"; can the following action be removed from this plan and still have a valid plan: navigate from loc-x2-y1 to loc-x3-y1?", "answer": "yes"}
{"id": 8781957024954838143, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x0-y0. Currently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, and Place loc-x3-y1 has been visited.", "question": "Given the plan: \"navigate from loc-x3-y3 to loc-x2-y3, navigate from loc-x2-y3 to loc-x3-y3, navigate from loc-x3-y3 to loc-x3-y2, navigate from loc-x3-y2 to loc-x3-y1, navigate from loc-x3-y1 to loc-x3-y0, navigate from loc-x3-y0 to loc-x2-y0, navigate from loc-x2-y0 to loc-x1-y0, navigate from loc-x1-y0 to loc-x1-y1, navigate from loc-x1-y1 to loc-x0-y1, navigate from loc-x0-y1 to loc-x0-y2, navigate from loc-x0-y2 to loc-x0-y3, navigate from loc-x0-y3 to loc-x1-y3, navigate from loc-x1-y3 to loc-x1-y2, navigate from loc-x1-y2 to loc-x2-y2, navigate from loc-x2-y2 to loc-x2-y1\"; can the following action be removed from this plan and still have a valid plan: navigate from loc-x3-y3 to loc-x3-y2?", "answer": "no"}
{"id": -7058996813788868725, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3 and loc-x0-y0. Currently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, and Place loc-x3-y1 has been visited.", "question": "Given the plan: \"travel from loc-x3-y3 to loc-x2-y3, travel from loc-x2-y3 to loc-x1-y3, travel from loc-x1-y3 to loc-x1-y2, travel from loc-x1-y2 to loc-x2-y2, travel from loc-x2-y2 to loc-x3-y2, travel from loc-x3-y2 to loc-x2-y2, travel from loc-x2-y2 to loc-x2-y1, travel from loc-x2-y1 to loc-x3-y1, travel from loc-x3-y1 to loc-x3-y0, travel from loc-x3-y0 to loc-x2-y0, travel from loc-x2-y0 to loc-x1-y0, travel from loc-x1-y0 to loc-x1-y1, travel from loc-x1-y1 to loc-x1-y2, travel from loc-x1-y2 to loc-x0-y2, travel from loc-x0-y2 to loc-x0-y1\"; can the following action be removed from this plan and still have a valid plan: travel from loc-x2-y3 to loc-x1-y3?", "answer": "no"}
{"id": -5976098153454642118, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x0-y0. Currently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, and Place loc-x3-y1 has been visited.", "question": "Given the plan: \"move to place loc-x3-y2 from place loc-x3-y3, move to place loc-x3-y1 from place loc-x3-y2, move to place loc-x3-y0 from place loc-x3-y1, move to place loc-x2-y0 from place loc-x3-y0, move to place loc-x1-y0 from place loc-x2-y0, move to place loc-x1-y1 from place loc-x1-y0, move to place loc-x2-y1 from place loc-x1-y1, move to place loc-x1-y1 from place loc-x2-y1, move to place loc-x0-y1 from place loc-x1-y1, move to place loc-x0-y2 from place loc-x0-y1, move to place loc-x0-y3 from place loc-x0-y2, move to place loc-x1-y3 from place loc-x0-y3, move to place loc-x2-y3 from place loc-x1-y3, move to place loc-x2-y2 from place loc-x2-y3, move to place loc-x1-y2 from place loc-x2-y2\"; can the following action be removed from this plan and still have a valid plan: move to place loc-x1-y0 from place loc-x2-y0?", "answer": "no"}
{"id": 570687605183040984, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x0-y3.Place loc-x0-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x3-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"travel from loc-x0-y3 to loc-x0-y2, travel from loc-x0-y2 to loc-x0-y1, travel from loc-x0-y1 to loc-x0-y0, travel from loc-x0-y0 to loc-x1-y0, travel from loc-x1-y0 to loc-x1-y1, travel from loc-x1-y1 to loc-x2-y1, travel from loc-x2-y1 to loc-x2-y0, travel from loc-x2-y0 to loc-x3-y0, travel from loc-x3-y0 to loc-x3-y1, travel from loc-x3-y1 to loc-x3-y2, travel from loc-x3-y2 to loc-x3-y3, travel from loc-x3-y3 to loc-x2-y3, travel from loc-x2-y3 to loc-x2-y2, travel from loc-x2-y2 to loc-x1-y2, travel from loc-x1-y2 to loc-x1-y3\"; can the following action be removed from this plan and still have a valid plan: travel from loc-x0-y2 to loc-x0-y1?", "answer": "no"}
{"id": 3692863935243343437, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3 and loc-x0-y0. Currently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, and Place loc-x3-y1 has been visited.", "question": "Given the plan: \"move from place loc-x3-y3 to place loc-x3-y2, move from place loc-x3-y2 to place loc-x3-y1, move from place loc-x3-y1 to place loc-x2-y1, move from place loc-x2-y1 to place loc-x2-y0, move from place loc-x2-y0 to place loc-x3-y0, move from place loc-x3-y0 to place loc-x2-y0, move from place loc-x2-y0 to place loc-x1-y0, move from place loc-x1-y0 to place loc-x1-y1, move from place loc-x1-y1 to place loc-x0-y1, move from place loc-x0-y1 to place loc-x0-y2, move from place loc-x0-y2 to place loc-x1-y2, move from place loc-x1-y2 to place loc-x1-y3, move from place loc-x1-y3 to place loc-x2-y3, move from place loc-x2-y3 to place loc-x2-y2, move from place loc-x2-y2 to place loc-x3-y2\"; can the following action be removed from this plan and still have a valid plan: move from place loc-x2-y2 to place loc-x3-y2?", "answer": "yes"}
