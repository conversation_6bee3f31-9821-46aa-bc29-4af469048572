{"id": 3878761865268448015, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x2-y3.The following places have been visited: loc-x3-y0, loc-x0-y3, loc-x3-y2, loc-x0-y2, loc-x2-y0, loc-x2-y3, loc-x1-y3, loc-x3-y3, loc-x2-y2, loc-x1-y2, and loc-x2-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x2-y0 and the robot is in place loc-x2-y3. B. the robot is in place loc-x1-y2. C. the robot is in place loc-x2-y2 and the robot is in place loc-x2-y1. D. the robot is in place loc-x2-y0 and the robot is in place loc-x1-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y0 and the robot is in place loc-x2-y3", "the robot is in place loc-x1-y2", "the robot is in place loc-x2-y2 and the robot is in place loc-x2-y1", "the robot is in place loc-x2-y0 and the robot is in place loc-x1-y1"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 4420995701107034806, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x2-y3.The following places have been visited: loc-x3-y0, loc-x2-y0, loc-x2-y3, loc-x3-y3, loc-x1-y1, loc-x2-y2, loc-x1-y2, and loc-x2-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x2-y0 and the robot is in place loc-x0-y2. B. the robot is in place loc-x2-y3 and the robot is in place loc-x1-y1. C. Place loc-x3-y2 has been visited and the robot is in place loc-x3-y2. D. the robot is in place loc-x0-y2 and the robot is in place loc-x3-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y0 and the robot is in place loc-x0-y2", "the robot is in place loc-x2-y3 and the robot is in place loc-x1-y1", "Place loc-x3-y2 has been visited and the robot is in place loc-x3-y2", "the robot is in place loc-x0-y2 and the robot is in place loc-x3-y2"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 3211262035088721601, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x1-y4.The following places have been visited: loc-x3-y0, loc-x0-y4, loc-x0-y3, loc-x3-y2, loc-x0-y0, loc-x0-y2, loc-x2-y0, loc-x1-y3, loc-x3-y1, loc-x1-y1, loc-x1-y4, loc-x2-y2, loc-x1-y2, loc-x0-y1, loc-x1-y0, and loc-x2-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x0-y4 and the robot is in place loc-x2-y1. B. the robot is in place loc-x2-y2 and the robot is in place loc-x3-y3. C. Place loc-x2-y4 has been visited. D. the robot is in place loc-x0-y2 and the robot is in place loc-x0-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x0-y4 and the robot is in place loc-x2-y1", "the robot is in place loc-x2-y2 and the robot is in place loc-x3-y3", "Place loc-x2-y4 has been visited", "the robot is in place loc-x0-y2 and the robot is in place loc-x0-y1"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 9117448111502219264, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x3-y0, loc-x3-y2, loc-x2-y0, loc-x3-y3, loc-x2-y2, and loc-x2-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Place loc-x2-y3 has been visited and the robot is in place loc-x2-y3. B. the robot is in place loc-x1-y2 and the robot is in place loc-x0-y2. C. the robot is in place loc-x0-y1 and the robot is in place loc-x0-y3. D. the robot is in place loc-x3-y2 and the robot is in place loc-x0-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Place loc-x2-y3 has been visited and the robot is in place loc-x2-y3", "the robot is in place loc-x1-y2 and the robot is in place loc-x0-y2", "the robot is in place loc-x0-y1 and the robot is in place loc-x0-y3", "the robot is in place loc-x3-y2 and the robot is in place loc-x0-y1"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -1158805352936933551, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x1-y0.The following places have been visited: loc-x3-y0, loc-x0-y4, loc-x0-y3, loc-x3-y2, loc-x3-y4, loc-x0-y2, loc-x2-y0, loc-x3-y3, loc-x1-y4, loc-x2-y4, loc-x1-y0, and loc-x3-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x2-y1 and the robot is in place loc-x3-y2. B. Place loc-x0-y0 has been visited. C. the robot is in place loc-x2-y1 and the robot is in place loc-x3-y2. D. the robot is in place loc-x2-y4 and the robot is in place loc-x3-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y1 and the robot is in place loc-x3-y2", "Place loc-x0-y0 has been visited", "the robot is in place loc-x2-y1 and the robot is in place loc-x3-y2", "the robot is in place loc-x2-y4 and the robot is in place loc-x3-y0"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -4221505914593037373, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x0-y2, loc-x3-y3, and loc-x1-y0. Currently, the robot is in place loc-x2-y1.The following places have been visited: loc-x0-y3, loc-x3-y2, loc-x0-y0, loc-x2-y0, loc-x2-y3, loc-x1-y3, loc-x1-y1, loc-x2-y2, loc-x2-y1, loc-x1-y2, and loc-x0-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x2-y2 and the robot is in place loc-x0-y1. B. the robot is in place loc-x3-y2 and the robot is in place loc-x2-y2. C. the robot is in place loc-x2-y1 and the robot is in place loc-x2-y3. D. the robot is in place loc-x0-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y2 and the robot is in place loc-x0-y1", "the robot is in place loc-x3-y2 and the robot is in place loc-x2-y2", "the robot is in place loc-x2-y1 and the robot is in place loc-x2-y3", "the robot is in place loc-x0-y1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -1258566148040957714, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x3-y0, loc-x3-y2, loc-x2-y0, loc-x2-y3, loc-x1-y3, loc-x3-y3, loc-x2-y2, and loc-x2-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x2-y2 and the robot is in place loc-x1-y2. B. the robot is in place loc-x0-y2 and Place loc-x0-y2 has been visited. C. the robot is in place loc-x2-y3 and the robot is in place loc-x3-y3. D. the robot is in place loc-x2-y3 and the robot is in place loc-x1-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y2 and the robot is in place loc-x1-y2", "the robot is in place loc-x0-y2 and Place loc-x0-y2 has been visited", "the robot is in place loc-x2-y3 and the robot is in place loc-x3-y3", "the robot is in place loc-x2-y3 and the robot is in place loc-x1-y1"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -4292600062871153127, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y2, loc-x2-y3, and loc-x0-y4. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x3-y4, loc-x0-y3, loc-x3-y2, loc-x0-y0, loc-x0-y2, loc-x1-y3, loc-x3-y3, loc-x1-y1, loc-x1-y4, loc-x2-y2, loc-x2-y4, loc-x0-y1, loc-x1-y0, and loc-x2-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x1-y1 and the robot is in place loc-x2-y4. B. the robot is in place loc-x0-y2 and the robot is in place loc-x3-y4. C. the robot is in place loc-x0-y0 and the robot is in place loc-x0-y3. D. the robot is in place loc-x2-y0 and Place loc-x2-y0 has been visited.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x1-y1 and the robot is in place loc-x2-y4", "the robot is in place loc-x0-y2 and the robot is in place loc-x3-y4", "the robot is in place loc-x0-y0 and the robot is in place loc-x0-y3", "the robot is in place loc-x2-y0 and Place loc-x2-y0 has been visited"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -6398941987123616115, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x2-y1.The following places have been visited: loc-x3-y0, loc-x0-y3, loc-x3-y2, loc-x0-y0, loc-x0-y2, loc-x2-y0, loc-x2-y3, loc-x1-y3, loc-x3-y1, loc-x3-y3, loc-x2-y2, loc-x2-y1, loc-x1-y2, loc-x0-y1, and loc-x1-y0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x3-y3 and the robot is in place loc-x3-y2. B. the robot is in place loc-x1-y2. C. the robot is in place loc-x0-y2 and the robot is in place loc-x1-y1. D. the robot is in place loc-x2-y0 and the robot is in place loc-x3-y3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x3-y3 and the robot is in place loc-x3-y2", "the robot is in place loc-x1-y2", "the robot is in place loc-x0-y2 and the robot is in place loc-x1-y1", "the robot is in place loc-x2-y0 and the robot is in place loc-x3-y3"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 8419673906829450802, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y2, loc-x2-y3, and loc-x0-y4. Currently, the robot is in place loc-x3-y1.The following places have been visited: loc-x3-y0, loc-x0-y0, loc-x0-y2, loc-x2-y0, loc-x0-y1, loc-x1-y0, and loc-x3-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x2-y0. B. the robot is in place loc-x0-y2 and the robot is in place loc-x0-y3. C. the robot is in place loc-x1-y3 and the robot is in place loc-x3-y1. D. the robot is in place loc-x3-y4 and the robot is in place loc-x3-y3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y0", "the robot is in place loc-x0-y2 and the robot is in place loc-x0-y3", "the robot is in place loc-x1-y3 and the robot is in place loc-x3-y1", "the robot is in place loc-x3-y4 and the robot is in place loc-x3-y3"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -7257112633107301159, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. Currently, the robot is in place loc-x1-y2.The following places have been visited: loc-x0-y3, loc-x3-y2, loc-x2-y3, loc-x1-y3, loc-x3-y3, loc-x1-y1, loc-x2-y2, loc-x1-y2, and loc-x2-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x1-y2 and the robot is in place loc-x1-y0. B. the robot is in place loc-x2-y0 and the robot is in place loc-x0-y3. C. the robot is in place loc-x1-y1 and the robot is in place loc-x0-y2. D. Place loc-x0-y2 has been visited.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x1-y2 and the robot is in place loc-x1-y0", "the robot is in place loc-x2-y0 and the robot is in place loc-x0-y3", "the robot is in place loc-x1-y1 and the robot is in place loc-x0-y2", "Place loc-x0-y2 has been visited"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 1649431623751410392, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y3, loc-x0-y3, and loc-x0-y0. Currently, the robot is in place loc-x0-y2.The following places have been visited: loc-x3-y2, loc-x0-y2, loc-x1-y3, loc-x2-y2, and loc-x1-y2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x2-y3 and the robot is in place loc-x2-y0. B. the robot is in place loc-x0-y1 and the robot is in place loc-x2-y3. C. Place loc-x0-y1 has been visited. D. the robot is in place loc-x3-y2 and the robot is in place loc-x2-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y3 and the robot is in place loc-x2-y0", "the robot is in place loc-x0-y1 and the robot is in place loc-x2-y3", "Place loc-x0-y1 has been visited", "the robot is in place loc-x3-y2 and the robot is in place loc-x2-y1"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -6082913568827927186, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x0-y0.The following places have been visited: loc-x3-y0, loc-x0-y3, loc-x3-y2, loc-x0-y0, loc-x2-y0, loc-x2-y3, loc-x1-y3, loc-x3-y3, loc-x1-y0, and loc-x3-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x3-y3 and the robot is in place loc-x2-y1. B. Place loc-x0-y1 has been visited and the robot is in place loc-x1-y1. C. the robot is in place loc-x2-y1 and the robot is in place loc-x2-y3. D. the robot is in place loc-x0-y2 and the robot is in place loc-x1-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x3-y3 and the robot is in place loc-x2-y1", "Place loc-x0-y1 has been visited and the robot is in place loc-x1-y1", "the robot is in place loc-x2-y1 and the robot is in place loc-x2-y3", "the robot is in place loc-x0-y2 and the robot is in place loc-x1-y1"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 3326112655036367305, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3 and loc-x0-y0. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x3-y0, loc-x3-y2, loc-x0-y2, loc-x2-y0, loc-x2-y3, loc-x1-y3, loc-x3-y3, loc-x1-y1, loc-x2-y2, loc-x1-y2, loc-x0-y1, loc-x1-y0, and loc-x3-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x1-y1 and the robot is in place loc-x0-y1. B. the robot is in place loc-x2-y0 and Place loc-x2-y1 has been visited. C. the robot is in place loc-x1-y3 and the robot is in place loc-x3-y2. D. the robot is in place loc-x1-y3 and the robot is in place loc-x1-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x1-y1 and the robot is in place loc-x0-y1", "the robot is in place loc-x2-y0 and Place loc-x2-y1 has been visited", "the robot is in place loc-x1-y3 and the robot is in place loc-x3-y2", "the robot is in place loc-x1-y3 and the robot is in place loc-x1-y0"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 3020948462958187766, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3 and loc-x0-y0. Currently, the robot is in place loc-x2-y0.The following places have been visited: loc-x3-y2, loc-x0-y2, loc-x2-y0, loc-x2-y3, loc-x1-y3, loc-x3-y1, loc-x3-y3, loc-x1-y1, loc-x2-y2, loc-x1-y2, loc-x0-y1, loc-x1-y0, and loc-x2-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x1-y2 and the robot is in place loc-x3-y3. B. the robot is in place loc-x3-y2 and the robot is in place loc-x2-y3. C. the robot is in place loc-x1-y2 and the robot is in place loc-x2-y1. D. the robot is in place loc-x3-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x1-y2 and the robot is in place loc-x3-y3", "the robot is in place loc-x3-y2 and the robot is in place loc-x2-y3", "the robot is in place loc-x1-y2 and the robot is in place loc-x2-y1", "the robot is in place loc-x3-y1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
