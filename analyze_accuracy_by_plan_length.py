#!/usr/bin/env python3
"""
分析不同plan_length下的准确率
"""

import json
import os
import glob
from collections import defaultdict
from typing import Dict, List, Tuple

def load_original_data() -> Dict[str, int]:
    """
    从原始数据集文件中加载question_id到plan_length的映射
    """
    question_to_plan_length = {}
    
    # 查找所有原始数据文件
    data_patterns = [
        "Benchmark/ARB/*/depots-*-true_false_answer.jsonl",
        "Benchmark/ARB/*/driverlog-*-true_false_answer.jsonl", 
        "Benchmark/ARB/*/grippers-*-true_false_answer.jsonl",
        "Benchmark/ARB/*/mystery-*-true_false_answer.jsonl",
        "Benchmark/ARB/*/satellite-*-true_false_answer.jsonl",
        "Benchmark/ARB/*/spanner-*-true_false_answer.jsonl",
        "Benchmark/ARB/*/visitall-*-true_false_answer.jsonl"
    ]
    
    for pattern in data_patterns:
        files = glob.glob(pattern)
        for file_path in files:
            print(f"Loading original data from: {file_path}")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            data = json.loads(line)
                            question_id = data.get('question_id')
                            plan_length = data.get('plan_length')
                            if question_id and plan_length is not None:
                                question_to_plan_length[question_id] = plan_length
            except Exception as e:
                print(f"Error loading {file_path}: {e}")
    
    print(f"Loaded {len(question_to_plan_length)} question-plan_length mappings")
    return question_to_plan_length

def load_response_data() -> List[Dict]:
    """
    从response文件中加载模型回答数据，并添加任务类型信息
    """
    response_data = []

    # 查找所有response文件
    response_files = glob.glob("*-response.jsonl")

    for file_path in response_files:
        print(f"Loading response data from: {file_path}")
        # 从文件名提取任务类型
        task_type = extract_task_type_from_filename(file_path)

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        data = json.loads(line)
                        # 添加任务类型信息
                        data['task_type'] = task_type
                        response_data.append(data)
        except Exception as e:
            print(f"Error loading {file_path}: {e}")

    print(f"Loaded {len(response_data)} response records")
    return response_data

def extract_task_type_from_filename(filename: str) -> str:
    """从文件名中提取任务类型"""
    if 'action_executability' in filename:
        return 'action_executability'
    elif 'effects' in filename:
        return 'effects'
    elif 'fluent_tracking' in filename:
        return 'fluent_tracking'
    elif 'state_tracking' in filename:
        return 'state_tracking'
    else:
        return 'unknown'

def analyze_accuracy_by_domain_and_plan_length(question_to_plan_length: Dict[str, int],
                                              response_data: List[Dict]) -> Dict[str, Dict[int, Dict]]:
    """
    按论域和plan_length分析准确率（混合所有任务类型）
    """
    # 按论域和plan_length分组统计
    stats_by_domain_and_length = defaultdict(lambda: defaultdict(lambda: {'correct': 0, 'total': 0, 'details': []}))

    for response in response_data:
        question_id = response.get('question_id')
        if not question_id:
            continue

        # 获取plan_length
        plan_length = question_to_plan_length.get(question_id)
        if plan_length is None:
            continue

        # 获取论域信息
        domain = response.get('domain', 'unknown')

        # 获取任务类型（用于记录详细信息）
        task_type = response.get('task_type', 'unknown')

        # 获取模型回答和正确答案
        model_answer = response.get('answer')  # 0 for False, 1 for True
        correct_label = response.get('label')  # "False" or "True"

        if model_answer is None or correct_label is None:
            continue

        # 转换正确答案为数字格式
        correct_answer = 1 if correct_label.lower() == 'true' else 0

        # 判断是否正确
        is_correct = (model_answer == correct_answer)

        # 统计（按论域和plan_length）
        stats_by_domain_and_length[domain][plan_length]['total'] += 1
        if is_correct:
            stats_by_domain_and_length[domain][plan_length]['correct'] += 1

        # 记录详细信息
        stats_by_domain_and_length[domain][plan_length]['details'].append({
            'question_id': question_id,
            'model_answer': model_answer,
            'correct_answer': correct_answer,
            'is_correct': is_correct,
            'task_type': task_type
        })

    return dict(stats_by_domain_and_length)

def print_results(stats_by_domain_and_length: Dict[str, Dict[int, Dict]]):
    """
    打印结果 - 按论域和plan_length统计
    """
    print("\n" + "="*80)
    print("不同论域和plan_length下的准确率统计（混合所有任务类型）")
    print("="*80)

    # 按论域分组显示
    for domain in sorted(stats_by_domain_and_length.keys()):
        print(f"\n{'='*20} {domain.upper()} {'='*20}")

        stats_by_length = stats_by_domain_and_length[domain]
        sorted_lengths = sorted(stats_by_length.keys())

        for plan_length in sorted_lengths:
            stats = stats_by_length[plan_length]
            accuracy = stats['correct'] / stats['total'] if stats['total'] > 0 else 0

            print(f"\nPlan Length: {plan_length}")
            print(f"  总题目数: {stats['total']}")
            print(f"  正确数量: {stats['correct']}")
            print(f"  准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")

            # 按任务类型统计
            task_stats = defaultdict(lambda: {'correct': 0, 'total': 0})
            for detail in stats['details']:
                task_type = detail['task_type']
                task_stats[task_type]['total'] += 1
                if detail['is_correct']:
                    task_stats[task_type]['correct'] += 1

            print(f"  按任务类型分布:")
            for task_type in sorted(task_stats.keys()):
                t_stats = task_stats[task_type]
                t_accuracy = t_stats['correct'] / t_stats['total'] if t_stats['total'] > 0 else 0
                print(f"    {task_type}: {t_stats['correct']}/{t_stats['total']} ({t_accuracy:.4f})")

def main():
    print("开始分析不同论域和plan_length下的准确率（混合所有任务类型）...")

    # 加载原始数据
    question_to_plan_length = load_original_data()

    # 加载response数据
    response_data = load_response_data()

    # 分析准确率
    stats_by_domain_and_length = analyze_accuracy_by_domain_and_plan_length(question_to_plan_length, response_data)

    # 打印结果
    print_results(stats_by_domain_and_length)

    # 重点关注plan_length为1, 10, 19的情况
    print("\n" + "="*80)
    print("重点关注的plan_length统计 (1, 10, 19) - 按论域分组")
    print("="*80)

    target_lengths = [1, 10, 19]

    # 创建汇总表格
    print(f"\n{'论域':<12} {'Plan Length 1':<15} {'Plan Length 10':<16} {'Plan Length 19':<16}")
    print("-" * 70)

    for domain in sorted(stats_by_domain_and_length.keys()):
        stats_by_length = stats_by_domain_and_length[domain]
        row = f"{domain:<12}"

        for length in target_lengths:
            if length in stats_by_length:
                stats = stats_by_length[length]
                accuracy = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
                row += f" {accuracy:.4f}({stats['correct']}/{stats['total']})"
                if length < 19:  # 不是最后一列
                    row += f"{'':>4}"
            else:
                row += f" {'N/A':<15}"

        print(row)

    # 汇总统计（所有论域合并）
    print(f"\n{'='*80}")
    print("汇总统计 (所有论域合并)")
    print("="*80)

    combined_stats = defaultdict(lambda: {'correct': 0, 'total': 0})
    for domain, stats_by_length in stats_by_domain_and_length.items():
        for length, stats in stats_by_length.items():
            combined_stats[length]['correct'] += stats['correct']
            combined_stats[length]['total'] += stats['total']

    for length in target_lengths:
        if length in combined_stats:
            stats = combined_stats[length]
            accuracy = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
            print(f"Plan Length {length}: {accuracy:.4f} ({accuracy*100:.2f}%) - {stats['correct']}/{stats['total']}")
        else:
            print(f"Plan Length {length}: 没有找到数据")

if __name__ == "__main__":
    main()
