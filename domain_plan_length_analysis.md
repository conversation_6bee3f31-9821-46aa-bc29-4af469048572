# ARB Benchmark 按论域和Plan Length的准确率分析

## 📊 核心统计表格

按照 **(论域, Plan Length)** 划分，混合所有四个任务类型的准确率：

| 论域 | Plan Length 1 | Plan Length 10 | Plan Length 19 | 下降幅度 |
|------|---------------|----------------|----------------|----------|
| **satellite** | **97.83%** (45/46) | **93.02%** (40/43) | **86.49%** (32/37) | **-11.34%** |
| **grippers** | **96.77%** (60/62) | **96.61%** (57/59) | **90.38%** (47/52) | **-6.39%** |
| **driverlog** | **96.55%** (28/29) | **95.35%** (41/43) | **82.05%** (32/39) | **-14.50%** |
| **spanner** | **95.45%** (42/44) | **92.00%** (46/50) | **82.00%** (41/50) | **-13.45%** |
| **mystery** | **94.87%** (37/39) | **93.55%** (29/31) | **93.94%** (31/33) | **-0.93%** |
| **depots** | **93.88%** (46/49) | **82.93%** (34/41) | **91.07%** (51/56) | **-2.81%** |
| **visitall** | **92.31%** (48/52) | **91.38%** (53/58) | **88.89%** (48/54) | **-3.42%** |

### 🎯 关键发现

#### 1. 论域难度排序（Plan Length 1基准）
1. **satellite** (97.83%) - 最容易
2. **grippers** (96.77%) - 很容易  
3. **driverlog** (96.55%) - 很容易
4. **spanner** (95.45%) - 容易
5. **mystery** (94.87%) - 中等
6. **depots** (93.88%) - 中等偏难
7. **visitall** (92.31%) - 最难

#### 2. Plan Length敏感度分析
- **最敏感**: driverlog (-14.50%), spanner (-13.45%), satellite (-11.34%)
- **最稳定**: mystery (-0.93%), depots (-2.81%), visitall (-3.42%)
- **中等敏感**: grippers (-6.39%)

#### 3. 异常模式识别
- **depots**: Plan Length 10时表现异常差（82.93%），但Plan Length 19时恢复（91.07%）
- **mystery**: 几乎不受Plan Length影响，表现非常稳定
- **driverlog**: 在长序列规划中表现显著下降

### 📈 详细分析

#### 表现最稳定的论域
1. **mystery**: 准确率变化最小（-0.93%），在所有Plan Length下都保持93%+
2. **visitall**: 虽然基础准确率较低，但下降幅度小（-3.42%）
3. **depots**: 除了Plan Length 10的异常外，整体稳定

#### 最具挑战性的论域
1. **driverlog**: 长序列规划时准确率大幅下降至82.05%
2. **spanner**: 从95.45%下降到82.00%，降幅达13.45%
3. **satellite**: 虽然基础能力强，但对复杂度敏感

#### 特殊模式
- **depots**: 呈现"V"型模式，Plan Length 10时最差，Plan Length 19时恢复
- **grippers**: 在Plan Length 1和10时几乎相同，但Plan Length 19时有明显下降
- **mystery**: 表现最一致，几乎不受Plan Length影响

### 🔍 任务类型分布洞察

通过观察各论域内的任务类型分布，我们发现：

#### 普遍模式
- **effects** 任务在大多数论域中表现最稳定
- **state_tracking** 任务通常是最具挑战性的
- **fluent_tracking** 在短序列中表现优秀，但在长序列中有所下降

#### 论域特异性
- **mystery**: 在所有任务类型中都表现稳定
- **driverlog**: fluent_tracking和state_tracking在长序列中显著下降
- **spanner**: state_tracking始终是弱项

### 💡 实际应用建议

1. **短期规划应用** (Plan Length ≤ 10):
   - 所有论域都可靠，准确率90%+
   - satellite, grippers, driverlog表现最佳

2. **长期规划应用** (Plan Length ≥ 19):
   - mystery最可靠（93.94%）
   - 避免在driverlog和spanner中使用复杂规划
   - satellite需要额外验证

3. **论域选择策略**:
   - **稳定性优先**: mystery, visitall, depots
   - **高准确率优先**: satellite, grippers（短中期）
   - **需要谨慎**: driverlog, spanner（长期规划）

### 📋 汇总统计

**所有论域合并的整体表现**:
- Plan Length 1: **95.33%** (306/321)
- Plan Length 10: **92.31%** (300/325)  
- Plan Length 19: **87.85%** (282/321)

总体下降幅度: **7.48%**

---

*基于1002个response记录，涵盖7个论域和4种任务类型的混合分析*
