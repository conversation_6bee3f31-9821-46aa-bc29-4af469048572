# 安装必要的库
# pip install datasets

from datasets import load_dataset
import os
import json

# 设置下载目录
download_dir = "./data"
os.makedirs(download_dir, exist_ok=True)

# 所有可用的配置
configs = [
    'acp_app_bool', 'acp_app_mcq', 'acp_areach_bool', 'acp_areach_mcq', 
    'acp_just_bool', 'acp_just_mcq', 'acp_land_bool', 'acp_land_mcq', 
    'acp_prog_bool', 'acp_prog_mcq', 'acp_reach_bool', 'acp_reach_mcq', 
    'acp_val_bool', 'acp_val_mcq', 'acp_app_gen', 'acp_areach_gen', 
    'acp_just_gen', 'acp_land_gen', 'acp_prog_gen', 'acp_nexta_gen', 
    'acp_reach_gen', 'acp_val_gen'
]

# 下载每个配置并保存为JSONL
for config in configs:
    print(f"正在处理配置: {config}")
    
    try:
        # 下载特定配置的数据集
        dataset = load_dataset("ibm-research/acp_bench", config, cache_dir=download_dir)
        
        # 为每个配置创建一个目录
        config_dir = os.path.join(download_dir, config)
        os.makedirs(config_dir, exist_ok=True)
        
        # 保存为JSONL文件
        for split_name, split_dataset in dataset.items():
            output_file = os.path.join(config_dir, f"{split_name}.jsonl")
            
            with open(output_file, 'w', encoding='utf-8') as f:
                for example in split_dataset:
                    # 将每个样本转换为JSON字符串并写入一行
                    f.write(json.dumps(example) + '\n')
            
            print(f"  已保存 {split_name} 数据集到 {output_file}")
    
    except Exception as e:
        print(f"处理配置 {config} 时出错: {e}")
        continue

print("所有数据集下载和转换完成！")
