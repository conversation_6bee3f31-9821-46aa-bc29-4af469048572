#!/usr/bin/env python3
"""
生成详细的准确率分析报告，包括可视化图表
"""

import json
import os
import glob
import matplotlib.pyplot as plt
import pandas as pd
from collections import defaultdict
from typing import Dict, List, Tuple

def load_original_data() -> Dict[str, int]:
    """从原始数据集文件中加载question_id到plan_length的映射"""
    question_to_plan_length = {}
    
    data_patterns = [
        "Benchmark/ARB/*/depots-*-true_false_answer.jsonl",
        "Benchmark/ARB/*/driverlog-*-true_false_answer.jsonl", 
        "Benchmark/ARB/*/grippers-*-true_false_answer.jsonl",
        "Benchmark/ARB/*/mystery-*-true_false_answer.jsonl",
        "Benchmark/ARB/*/satellite-*-true_false_answer.jsonl",
        "Benchmark/ARB/*/spanner-*-true_false_answer.jsonl",
        "Benchmark/ARB/*/visitall-*-true_false_answer.jsonl"
    ]
    
    for pattern in data_patterns:
        files = glob.glob(pattern)
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            data = json.loads(line)
                            question_id = data.get('question_id')
                            plan_length = data.get('plan_length')
                            if question_id and plan_length is not None:
                                question_to_plan_length[question_id] = plan_length
            except Exception as e:
                print(f"Error loading {file_path}: {e}")
    
    return question_to_plan_length

def load_response_data() -> List[Dict]:
    """从response文件中加载模型回答数据"""
    response_data = []
    response_files = glob.glob("*-response.jsonl")
    
    for file_path in response_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        data = json.loads(line)
                        response_data.append(data)
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
    
    return response_data

def analyze_accuracy_by_plan_length(question_to_plan_length: Dict[str, int], 
                                  response_data: List[Dict]) -> Dict[int, Dict]:
    """按plan_length分析准确率"""
    stats_by_length = defaultdict(lambda: {'correct': 0, 'total': 0, 'details': []})
    
    for response in response_data:
        question_id = response.get('question_id')
        if not question_id:
            continue
            
        plan_length = question_to_plan_length.get(question_id)
        if plan_length is None:
            continue
            
        model_answer = response.get('answer')
        correct_label = response.get('label')
        
        if model_answer is None or correct_label is None:
            continue
            
        correct_answer = 1 if correct_label.lower() == 'true' else 0
        is_correct = (model_answer == correct_answer)
        
        stats_by_length[plan_length]['total'] += 1
        if is_correct:
            stats_by_length[plan_length]['correct'] += 1
            
        stats_by_length[plan_length]['details'].append({
            'question_id': question_id,
            'model_answer': model_answer,
            'correct_answer': correct_answer,
            'is_correct': is_correct,
            'domain': response.get('domain', 'unknown')
        })
    
    return dict(stats_by_length)

def create_visualizations(stats_by_length: Dict[int, Dict]):
    """创建可视化图表"""
    # 准备数据
    lengths = sorted(stats_by_length.keys())
    accuracies = []
    totals = []
    
    for length in lengths:
        stats = stats_by_length[length]
        accuracy = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
        accuracies.append(accuracy)
        totals.append(stats['total'])
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 图1: 准确率随plan_length变化
    ax1.plot(lengths, accuracies, 'bo-', linewidth=2, markersize=8)
    ax1.set_xlabel('Plan Length')
    ax1.set_ylabel('Accuracy')
    ax1.set_title('Accuracy vs Plan Length')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0.8, 1.0)
    
    # 标注重点数据点
    target_lengths = [1, 10, 19]
    for length in target_lengths:
        if length in stats_by_length:
            idx = lengths.index(length)
            ax1.annotate(f'{accuracies[idx]:.3f}', 
                        (length, accuracies[idx]), 
                        textcoords="offset points", 
                        xytext=(0,10), 
                        ha='center',
                        fontweight='bold',
                        color='red')
    
    # 图2: 每个plan_length的题目数量
    ax2.bar(lengths, totals, alpha=0.7, color='skyblue')
    ax2.set_xlabel('Plan Length')
    ax2.set_ylabel('Number of Questions')
    ax2.set_title('Number of Questions by Plan Length')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('accuracy_by_plan_length.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 创建按domain分组的热力图
    domains = ['depots', 'driverlog', 'grippers', 'mystery', 'satellite', 'spanner', 'visitall']
    target_lengths = [1, 10, 19]
    
    # 准备热力图数据
    heatmap_data = []
    for domain in domains:
        row = []
        for length in target_lengths:
            if length in stats_by_length:
                domain_stats = defaultdict(lambda: {'correct': 0, 'total': 0})
                for detail in stats_by_length[length]['details']:
                    d = detail['domain']
                    domain_stats[d]['total'] += 1
                    if detail['is_correct']:
                        domain_stats[d]['correct'] += 1
                
                if domain in domain_stats and domain_stats[domain]['total'] > 0:
                    accuracy = domain_stats[domain]['correct'] / domain_stats[domain]['total']
                    row.append(accuracy)
                else:
                    row.append(0)
            else:
                row.append(0)
        heatmap_data.append(row)
    
    # 创建热力图
    fig, ax = plt.subplots(figsize=(8, 6))
    im = ax.imshow(heatmap_data, cmap='RdYlGn', aspect='auto', vmin=0.7, vmax=1.0)
    
    # 设置标签
    ax.set_xticks(range(len(target_lengths)))
    ax.set_xticklabels([f'Length {l}' for l in target_lengths])
    ax.set_yticks(range(len(domains)))
    ax.set_yticklabels(domains)
    
    # 添加数值标注
    for i in range(len(domains)):
        for j in range(len(target_lengths)):
            text = ax.text(j, i, f'{heatmap_data[i][j]:.3f}',
                          ha="center", va="center", color="black", fontweight='bold')
    
    ax.set_title('Accuracy Heatmap by Domain and Plan Length')
    plt.colorbar(im, ax=ax, label='Accuracy')
    plt.tight_layout()
    plt.savefig('accuracy_heatmap_by_domain.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_report(stats_by_length: Dict[int, Dict]):
    """生成详细报告"""
    report = []
    report.append("# ARB Benchmark 准确率分析报告")
    report.append("## 按Plan Length分析\n")
    
    # 总体统计
    target_lengths = [1, 10, 19]
    report.append("### 重点关注的Plan Length统计\n")
    report.append("| Plan Length | 准确率 | 正确数/总数 | 准确率变化 |")
    report.append("|-------------|--------|-------------|------------|")
    
    prev_accuracy = None
    for length in target_lengths:
        if length in stats_by_length:
            stats = stats_by_length[length]
            accuracy = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
            change = ""
            if prev_accuracy is not None:
                diff = accuracy - prev_accuracy
                change = f"{diff:+.4f}"
            report.append(f"| {length} | {accuracy:.4f} ({accuracy*100:.2f}%) | {stats['correct']}/{stats['total']} | {change} |")
            prev_accuracy = accuracy
    
    # 按domain详细分析
    report.append("\n### 按Domain详细分析\n")
    
    for length in target_lengths:
        if length not in stats_by_length:
            continue
            
        report.append(f"#### Plan Length {length}\n")
        
        # 按domain统计
        domain_stats = defaultdict(lambda: {'correct': 0, 'total': 0})
        for detail in stats_by_length[length]['details']:
            domain = detail['domain']
            domain_stats[domain]['total'] += 1
            if detail['is_correct']:
                domain_stats[domain]['correct'] += 1
        
        report.append("| Domain | 准确率 | 正确数/总数 |")
        report.append("|--------|--------|-------------|")
        
        for domain in sorted(domain_stats.keys()):
            d_stats = domain_stats[domain]
            d_accuracy = d_stats['correct'] / d_stats['total'] if d_stats['total'] > 0 else 0
            report.append(f"| {domain} | {d_accuracy:.4f} ({d_accuracy*100:.2f}%) | {d_stats['correct']}/{d_stats['total']} |")
        
        report.append("")
    
    # 趋势分析
    report.append("## 趋势分析\n")
    
    accuracies = []
    for length in target_lengths:
        if length in stats_by_length:
            stats = stats_by_length[length]
            accuracy = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
            accuracies.append(accuracy)
    
    if len(accuracies) >= 2:
        trend = "下降" if accuracies[-1] < accuracies[0] else "上升"
        total_change = accuracies[-1] - accuracies[0]
        report.append(f"- 从Plan Length 1到19，准确率整体呈现**{trend}**趋势")
        report.append(f"- 总体变化: {total_change:+.4f} ({total_change*100:+.2f}%)")
        report.append(f"- Plan Length 1: {accuracies[0]:.4f}")
        report.append(f"- Plan Length 10: {accuracies[1]:.4f}")
        report.append(f"- Plan Length 19: {accuracies[2]:.4f}")
    
    # 保存报告
    with open('accuracy_analysis_report.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("报告已保存到 accuracy_analysis_report.md")

def main():
    print("生成详细的准确率分析报告...")
    
    # 加载数据
    question_to_plan_length = load_original_data()
    response_data = load_response_data()
    
    # 分析准确率
    stats_by_length = analyze_accuracy_by_plan_length(question_to_plan_length, response_data)
    
    # 生成可视化图表
    create_visualizations(stats_by_length)
    
    # 生成详细报告
    generate_report(stats_by_length)
    
    print("分析完成！")

if __name__ == "__main__":
    main()
