# ARB Benchmark Plan Length 准确率分析总结

## 主要发现

根据对ARB benchmark数据集中不同plan_length的准确率分析，我们得到以下关键结果：

### 📊 核心统计数据

| Plan Length | 准确率 | 正确数/总数 | 相比前一长度的变化 |
|-------------|--------|-------------|-------------------|
| **1** | **95.33%** | 306/321 | - |
| **10** | **92.31%** | 300/325 | **-3.02%** |
| **19** | **87.85%** | 282/321 | **-4.46%** |

### 🔍 关键观察

1. **明显的下降趋势**: 随着plan_length的增加，模型准确率呈现明显下降趋势
   - 从plan_length=1到plan_length=19，准确率下降了**7.48个百分点**
   - 每增加约9个plan步骤，准确率平均下降约3-4个百分点

2. **性能表现**:
   - **Plan Length 1**: 95.33% - 表现最佳，接近人类水平
   - **Plan Length 10**: 92.31% - 仍然保持较高准确率
   - **Plan Length 19**: 87.85% - 准确率有所下降但仍在可接受范围

### 📈 按Domain分析

#### Plan Length 1 (最佳表现)
- **最佳domain**: satellite (97.83%)
- **最差domain**: visitall (92.31%)
- **整体表现**: 所有domain都超过92%

#### Plan Length 10 (中等复杂度)
- **最佳domain**: grippers (96.61%)
- **最差domain**: depots (82.93%)
- **性能分化**: 开始出现domain间的明显差异

#### Plan Length 19 (最高复杂度)
- **最佳domain**: mystery (93.94%)
- **最差domain**: spanner (82.00%)
- **挑战增加**: 多个domain准确率降至80-90%区间

### 🎯 结论与建议

1. **复杂度影响**: Plan length是影响模型性能的重要因素，更长的计划序列对模型构成更大挑战

2. **Domain差异**: 不同domain对plan length的敏感度不同：
   - **相对稳定**: mystery, grippers
   - **敏感度高**: depots, driverlog, spanner

3. **实际应用**: 
   - 对于短期规划任务(plan_length ≤ 10)，模型表现优秀
   - 对于长期复杂规划(plan_length ≥ 19)，需要额外的优化策略

### 📁 生成文件

本次分析生成了以下文件：
- `accuracy_analysis_report.md` - 详细分析报告
- `accuracy_by_plan_length.png` - 准确率趋势图
- `accuracy_heatmap_by_domain.png` - 按domain的热力图
- `analyze_accuracy_by_plan_length.py` - 分析脚本
- `generate_accuracy_report.py` - 报告生成脚本

---

*分析基于1002个response记录和1889个原始数据映射*
