{"question_id": "870ce869-ddcf-40d4-9394-144ef8699895", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: instrument3 that is on satellite1 is turned on, instrument0 on satellite0 is switched on, satellite1 turns to groundstation5 from phenomenon10, calibration of instrument3 which is on satellite1 to groundstation5 is complete, satellite1 turns to phenomenon16 from groundstation5, image of phenomenon16 is taken with instrument3 on satellite1 in image3, satellite1 turns to phenomenon17 from phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, satellite1 turns from phenomenon17 to planet11, image of planet11 is taken with instrument3 on satellite1 in image3, from planet11, satellite1 turns to planet13, satellite1's instrument3 takes an image of planet13 in image0, from groundstation2, satellite1 turns to groundstation7, satellite1's instrument3 takes an image of planet14 in image0, from planet14, satellite1 turns to star15, instrument3 which is on satellite1 takes an image of star15 in image2, satellite0 turns from groundstation3 to star1, instrument0 is calibrated on satellite0 to star1 and satellite0 turns from star1 to phenomenon10. Is the action: satellite1 turns from groundstation2 to groundstation7 executable at step 13, True or False?", "initial_state": "We can find following objects in the initial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. for star9, instrument0 is calibrated, infrared1 is compatible with instrument0, instrument0 is calibrated for star1, instrument0 supports image3, satellite0 has instrument0 on board. ::: Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. for groundstation0, instrument1 is calibrated, image0 is compatible with instrument1, image3 is compatible with instrument1, infrared1 is supported by instrument1, satellite0 carries instrument1 on board. ::: Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, image2 is compatible with instrument2, image3 is compatible with instrument2, instrument2 is on board satellite0. ::: Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. satellite1 carries instrument3 on board, calibration of instrument3 for star6 is complete, instrument3 is calibrated for star8, instrument3 is calibrated for groundstation5, image0 is supported by instrument3, image2 is compatible with instrument3, image3 is supported by instrument3. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. satellite0 carries instrument1 on board, instrument2 is on board satellite0, satellite0 has instrument0 on board, satellite0 has power available, groundstation3 is where satellite0 is pointed. ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. satellite1 carries instrument3 on board, satellite1 has power available, and satellite1 is pointing to phenomenon10. ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["instrument3 that is on satellite1 is turned on", "instrument0 on satellite0 is switched on", "satellite1 turns to groundstation5 from phenomenon10", "calibration of instrument3 which is on satellite1 to groundstation5 is complete", "satellite1 turns to phenomenon16 from groundstation5", "image of phenomenon16 is taken with instrument3 on satellite1 in image3", "satellite1 turns to phenomenon17 from phenomenon16", "instrument3 which is on satellite1 takes an image of phenomenon17 in image3", "satellite1 turns from phenomenon17 to planet11", "image of planet11 is taken with instrument3 on satellite1 in image3", "from planet11 satellite1 turns to planet13", "satellite1's instrument3 takes an image of planet13 in image0", "from groundstation2 satellite1 turns to groundstation7", "satellite1's instrument3 takes an image of planet14 in image0", "from planet14 satellite1 turns to star15", "instrument3 which is on satellite1 takes an image of star15 in image2", "satellite0 turns from groundstation3 to star1", "instrument0 is calibrated on satellite0 to star1", "satellite0 turns from star1 to phenomenon10"], "state_progression": ["Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to groundstation5, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to groundstation5, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon16, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon16, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon17, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon17, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet11, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet11, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet13, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet13, doesn't have power supply.", "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite1: carries instrument3, pointing to planet13, doesn't have power supply.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite1: carries instrument3, pointing to planet13. ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 12, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "58fee7c0-6cb8-4593-b533-fe83148ea38f", "domain": "satellite", "question": "Is it possible to execute it, True or False? Given the initial condition, the following actions are planned to be performed: image of star12 is taken with instrument3 on satellite1 in spectrograph1, instrument3 that is on satellite1 is calibrated to star6, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, on satellite0, instrument0 is switched on, on satellite1, instrument3 is switched on, satellite1 turns from planet14 to star10, satellite1 turns to planet14 from star6, satellite1 turns to star12 from star10, satellite1 turns to star6 from groundstation4 and satellite1's instrument3 takes an image of planet14 in spectrograph1.", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.", "action_sequence": ["An image of star12 is taken with instrument3 on satellite1 in spectrograph1", "Instrument3 that is on satellite1 is calibrated to star6", "Instrument3 which is on satellite1 takes an image of star10 in spectrograph1", "On satellite0 instrument0 is switched on", "On satellite1 instrument3 is switched on", "Satellite1 turns from planet14 to star10", "Satellite1 turns to planet14 from star6", "Satellite1 turns to star12 from star10", "Satellite1 turns to star6 from groundstation4", "Satellite1's instrument3 takes an image of planet14 in spectrograph1"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.\nBased on the domain description, an instrument on a satellite can 'take' an image of the target in the specific format. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing the target, the instrument is on the satellite, the instrument is turned on, the instrument is calibrated, the instrument supports the specific format.\nthe satellite is currently pointing the target ::: Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4. ===> NOT SATISFY (pointing to groundstation4, not star12)\nthe instrument is on the satellite ::: Instrument3: on satellite1 ===> SATISFY\nthe instrument is turned on ::: Instrument3: on satellite1, is calibrated ===> SATISFY (assuming \"is calibrated\" implies \"turned on\" based on previous examples)\nthe instrument is calibrated ::: Instrument3: on satellite1, is calibrated ===> SATISFY\nthe instrument supports the specific format ::: Instrument3: compatible with spectrograph1, compatible with spectrograph2 ===> SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "c6a6753d-0407-48c7-a6f1-ea944b6b6622", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: instrument1 on satellite0 is switched on, satellite0 turns to groundstation0 from groundstation2, instrument1 that is on satellite0 is calibrated to groundstation0, from groundstation0, satellite0 turns to planet11, instrument1 which is on satellite0 takes an image of planet11 in image5, satellite0's instrument1 takes an image of planet11 in image6, satellite0 turns to planet13 from planet11, image of planet13 is taken with instrument1 on satellite0 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2, instrument1 that is on satellite1 is calibrated to groundstation8, instrument1 which is on satellite0 takes an image of star10 in image6, satellite0's instrument1 takes an image of star10 in spectrograph2, instrument1 that is on satellite0 is turned off, on satellite0, instrument2 is switched on, satellite0 turns to star4 from star10, calibration of instrument2 which is on satellite0 to star4 is complete, from star4, satellite0 turns to star16, satellite0's instrument2 takes an image of star16 in image0 and on satellite0, instrument2 is switched off. Is the action: instrument1 that is on satellite1 is calibrated to groundstation8 executable at step 10, True or False?", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation2, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "action_sequence": ["instrument1 on satellite0 is switched on", "satellite0 turns to groundstation0 from groundstation2", "instrument1 that is on satellite0 is calibrated to groundstation0", "from groundstation0 satellite0 turns to planet11", "instrument1 which is on satellite0 takes an image of planet11 in image5", "satellite0's instrument1 takes an image of planet11 in image6", "satellite0 turns to planet13 from planet11", "image of planet13 is taken with instrument1 on satellite0 in image5", "image of planet13 is taken with instrument1 on satellite0 in spectrograph2", "instrument1 that is on satellite1 is calibrated to groundstation8", "instrument1 which is on satellite0 takes an image of star10 in image6", "satellite0's instrument1 takes an image of star10 in spectrograph2", "instrument1 that is on satellite0 is turned off", "on satellite0 instrument2 is switched on", "satellite0 turns to star4 from star10", "calibration of instrument2 which is on satellite0 to star4 is complete", "from star4 satellite0 turns to star16", "satellite0's instrument2 takes an image of star16 in image0", "on satellite0 instrument2 is switched off"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation2, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Satellite1: carries instrument4, pointing to planet13, has power supply.\nBased on the domain description, an instrument on the satellite can be \"calibrated\" for a target. This action is executable only if all following preconditions are satisfied: the instrument is on the satellite, the instrument can be calibrated using the target(calibration target), the satellite is pointing to the calibration target, the instrument is switched(turned) on.\nthe instrument is on the satellite ::: Instrument1: on satellite0 ===> NOT SATISFY (Instrument1 is on satellite0, not satellite1)\nthe instrument can be calibrated using the target(calibration target) ::: Instrument1: groundstation0 is its calibration target, groundstation6 is its calibration target ===> NOT SATISFY (groundstation8 is not listed as a calibration target for Instrument1)\nthe satellite is pointing to the calibration target ::: Satellite1: pointing to planet13 ===> NOT SATISFY (satellite1 is pointing to planet13, not groundstation8)\nthe instrument is switched(turned) on ::: Instrument1: switched on ===> SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 9, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "5c27f458-6357-4bec-bd13-7499d33dddf8", "domain": "satellite", "question": "Is it possible to execute it, True or False? Given the initial condition, the following actions are planned to be performed: from phenomenon16, satellite1 turns to phenomenon17, from planet14, satellite1 turns to star15, image of phenomenon17 is taken with instrument3 on satellite1 in image3, image of planet13 is taken with instrument3 on satellite1 in image0, instrument0 that is on satellite0 is calibrated to star1, instrument3 is calibrated on satellite1 to groundstation5, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, on satellite0, instrument0 is switched on, on satellite1, instrument3 is switched on, satellite0 turns from groundstation3 to star1, satellite0 turns to phenomenon10 from star1, satellite1 turns from phenomenon17 to planet11, satellite1 turns from planet11 to planet13, satellite1 turns to groundstation5 from phenomenon10, satellite1 turns to phenomenon16 from groundstation5, satellite1 turns to planet14 from planet13, satellite1's instrument3 takes an image of planet11 in image3, satellite1's instrument3 takes an image of planet14 in image0 and satellite1's instrument3 takes an image of star15 in image2.", "initial_state": "We can find following objects in the intial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. satellite0 has instrument0 on board, for star9, instrument0 is calibrated, instrument0 is calibrated for star1, infrared1 is compatible with instrument0, instrument0 supports image3. ::: Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. for groundstation0, instrument1 is calibrated, image0 is compatible with instrument1, image3 is compatible with instrument1, infrared1 is supported by instrument1, satellite0 carries instrument1 on board, ::: Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, image2 is compatible with instrument2, image3 is compatible with instrument2, instrument2 is on board satellite0. ::: Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. satellite1 carries instrument3 on board, calibration of instrument3 for star6 is complete, instrument3 is calibrated for star8, instrument3 is calibrated for groundstation5, image0 is supported by instrument3, image2 is compatible with instrument3, image3 is supported by instrument3. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. satellite0 carries instrument1 on board, instrument2 is on board satellite0, satellite0 has instrument0 on board, satellite0 has power available, groundstation3 is where satellite0 is pointed. ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. satellite1 carries instrument3 on board, satellite1 has power available, and satellite1 is pointing to phenomenon10. ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["From phenomenon16 satellite1 turns to phenomenon17", "From planet14 satellite1 turns to star15", "Image of phenomenon17 is taken with instrument3 on satellite1 in image3", "Image of planet13 is taken with instrument3 on satellite1 in image0", "Instrument0 that is on satellite0 is calibrated to star1", "Instrument3 is calibrated on satellite1 to groundstation5", "Instrument3 which is on satellite1 takes an image of phenomenon16 in image3", "On satellite0 instrument0 is switched on", "On satellite1 instrument3 is switched on", "Satellite0 turns from groundstation3 to star1", "Satellite0 turns to phenomenon10 from star1", "Satellite1 turns from phenomenon17 to planet11", "Satellite1 turns from planet11 to planet13", "Satellite1 turns to groundstation5 from phenomenon10", "Satellite1 turns to phenomenon16 from groundstation5", "Satellite1 turns to planet14 from planet13", "Satellite1's instrument3 takes an image of planet11 in image3", "Satellite1's instrument3 takes an image of planet14 in image0", "Satellite1's instrument3 takes an image of star15 in image2"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite1: carries instrument3, pointing to phenomenon10, has power supply.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply. ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "9472ad25-88e0-4ce9-912b-983e0cb32cb1", "domain": "satellite", "question": "Is it possible to execute it, True or False? Given the initial condition, the following actions are planned to be performed: from phenomenon16, satellite1 turns to phenomenon17, instrument0 on satellite0 is switched on, instrument3 is calibrated on satellite1 to groundstation5, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, instrument3 which is on satellite1 takes an image of planet11 in image3, on satellite1, instrument3 is switched on, satellite1 turns from groundstation5 to phenomenon16, satellite1 turns from phenomenon10 to groundstation5 and satellite1 turns from phenomenon17 to planet11.", "initial_state": "Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["From phenomenon16 satellite1 turns to phenomenon17", "Instrument0 on satellite0 is switched on", "Instrument3 is calibrated on satellite1 to groundstation5", "Instrument3 which is on satellite1 takes an image of phenomenon16 in image3", "Instrument3 which is on satellite1 takes an image of phenomenon17 in image3", "Instrument3 which is on satellite1 takes an image of planet11 in image3", "On satellite1 instrument3 is switched on", "Satellite1 turns from groundstation5 to phenomenon16", "Satellite1 turns from phenomenon10 to groundstation5", "Satellite1 turns from phenomenon17 to planet11"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite1: carries instrument3, pointing to phenomenon10, has power supply.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply. ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "d9b70287-edb1-49cc-a1a0-abe8507dde1f", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: calibration of instrument1 which is on satellite1 to groundstation5 is complete, on satellite0, instrument0 is switched on, from groundstation4, satellite1 turns to star6, calibration of instrument3 which is on satellite1 to star6 is complete, satellite1 turns to planet14 from star6, instrument3 which is on satellite1 takes an image of planet14 in spectrograph1, satellite1 turns to star10 from planet14, satellite1's instrument3 takes an image of star10 in spectrograph1, satellite1 turns to star12 from star10, image of star12 is taken with instrument3 on satellite1 in spectrograph1, satellite1 turns from star12 to star0, from star1, satellite0 turns to groundstation2, instrument0 is calibrated on satellite0 to groundstation2, from groundstation2, satellite0 turns to phenomenon15, satellite0's instrument0 takes an image of phenomenon15 in spectrograph0, satellite0 turns from phenomenon15 to star11, satellite0's instrument0 takes an image of star11 in thermograph4, satellite0 turns to star13 from star11 and instrument0 which is on satellite0 takes an image of star13 in spectrograph0. Is the action: instrument1 is calibrated on satellite1 to groundstation5 executable at step 1, True or False?", "initial_state": "We can find following objects in the intial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, for groundstation4, instrument0 is calibrated, spectrograph0 is compatible with instrument0, thermograph4 is supported by instrument0, instrument0 is on board satellite0. ::: Instrument0: on satellite0, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, instrument1 is calibrated for groundstation4, spectrograph0 is compatible with instrument1, instrument1 supports spectrograph1, instrument1 is on board satellite0. ::: Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument2 supports spectrograph0, instrument2 supports spectrograph2, instrument2 is on board satellite1. ::: Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. instrument3 is calibrated for star6, spectrograph2 is compatible with instrument3, instrument3 supports spectrograph1, instrument3 is on board satellite1. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph2, compatible with spectrograph1. instrument0 is on board satellite0, instrument1 is on board satellite0, satellite0 has power available, star1 is where satellite0 is pointed. ::: Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. instrument2 is on board satellite1, instrument3 is on board satellite1, satellite1 has power, satellite1 is aimed towards groundstation4. ::: Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph2, compatible with spectrograph1. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.", "action_sequence": ["The calibration of instrument1 which is on satellite1 to groundstation5 is complete", "On satellite0 instrument0 is switched on", "From groundstation4 satellite1 turns to star6", "The calibration of instrument3 which is on satellite1 to star6 is complete", "Satellite1 turns to planet14 from star6", "Instrument3 which is on satellite1 takes an image of planet14 in spectrograph1", "Satellite1 turns to star10 from planet14", "Satellite1's instrument3 takes an image of star10 in spectrograph1", "Satellite1 turns to star12 from star10", "An image of star12 is taken with instrument3 on satellite1 in spectrograph1", "Satellite1 turns from star12 to star0", "From star1 satellite0 turns to groundstation2", "Instrument0 is calibrated on satellite0 to groundstation2", "From groundstation2 satellite0 turns to phenomenon15", "Satellite0's instrument0 takes an image of phenomenon15 in spectrograph0", "Satellite0 turns from phenomenon15 to star11", "Satellite0's instrument0 takes an image of star11 in thermograph4", "Satellite0 turns to star13 from star11", "Instrument0 which is on satellite0 takes an image of star13 in spectrograph0"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.\nBased on the domain description, an instrument on the satellite can be \"calibrated\" for a target. This action is executable only if all following preconditions are satisfied: the instrument is on the satellite, the instrument can be calibrated using the target(calibration target), the satellite is pointing to the calibration target, the instrument is switched(turned) on.\nthe instrument is on the satellite ::: Instrument1: on satellite0 ===> NOT SATISFY (Instrument1 is on satellite0, not satellite1)\nthe instrument can be calibrated using the target(calibration target) ::: Instrument1: groundstation5 is not listed as a calibration target ===> NOT SATISFY\nthe satellite is pointing to the calibration target ::: Satellite1: pointing to groundstation4, not groundstation5 ===> NOT SATISFY\nthe instrument is switched(turned) on ::: Instrument1: not mentioned as switched on ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "5c0196b8-0219-472a-a9bc-f2df7ed327cf", "domain": "satellite", "question": "Is it possible to execute it, True or False? Given the initial condition, the following actions are planned to be performed: from phenomenon11, satellite1 turns to phenomenon5, from star1, satellite1 turns to phenomenon10, from star3, satellite1 turns to star1, image of phenomenon10 is taken with instrument1 on satellite1 in spectrograph3, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, instrument1 which is on satellite1 takes an image of phenomenon10 in image5, instrument1 which is on satellite1 takes an image of phenomenon5 in image4, on satellite1, instrument1 is switched on, satellite1 turns from phenomenon10 to phenomenon11 and satellite1 turns to phenomenon9 from phenomenon11.", "initial_state": "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, has power supply, pointing to star3.", "action_sequence": ["From phenomenon11 satellite1 turns to phenomenon5", "From star1 satellite1 turns to phenomenon10", "From star3 satellite1 turns to star1", "Image of phenomenon10 is taken with instrument1 on satellite1 in spectrograph3", "Image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1", "Instrument1 which is on satellite1 takes an image of phenomenon10 in image5", "Instrument1 which is on satellite1 takes an image of phenomenon5 in image4", "On satellite1 instrument1 is switched on", "Satellite1 turns from phenomenon10 to phenomenon11", "Satellite1 turns to phenomenon9 from phenomenon11"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite1: carries instrument1, has power supply, pointing to star3.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite1: carries instrument1, has power supply, pointing to star3. ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "531a7812-fa28-49cc-b491-2c5256e3d318", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: instrument1 that is on satellite0 is turned on, satellite0 turns from groundstation2 to groundstation0, instrument1 that is on satellite0 is calibrated to groundstation0, from groundstation0, satellite0 turns to planet11, satellite0's instrument1 takes an image of planet11 in image5, instrument1 which is on satellite0 takes an image of planet11 in image6, from planet11, satellite0 turns to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2 and satellite0 turns to star10 from planet13. Is the action: satellite0 turns from planet11 to planet13 executable at step 7, True or False?", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation2, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "action_sequence": ["instrument1 that is on satellite0 is turned on", "satellite0 turns from groundstation2 to groundstation0", "instrument1 that is on satellite0 is calibrated to groundstation0", "from groundstation0 satellite0 turns to planet11", "satellite0's instrument1 takes an image of planet11 in image5", "instrument1 which is on satellite0 takes an image of planet11 in image6", "from planet11 satellite0 turns to planet13", "image of planet13 is taken with instrument1 on satellite0 in image5", "instrument1 which is on satellite0 takes an image of planet13 in spectrograph2", "satellite0 turns to star10 from planet13"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation2, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply."], "action_taken": 10, "state_checker_response": "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13 ===> SATISFY\nSince all preconditions are satisfied, this action is executable.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "4bcb7716-82a5-4ecf-9cb8-70e3f5e71b72", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: on satellite0, instrument1 is switched on, satellite0 turns to groundstation0 from groundstation2, instrument1 is calibrated on satellite0 to groundstation0, satellite0 turns from groundstation0 to planet11, instrument2 which is on satellite1 takes an image of groundstation2 in image6, image of planet11 is taken with instrument1 on satellite0 in image6, satellite0 turns from planet11 to planet13, instrument1 which is on satellite0 takes an image of planet13 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2 and satellite0 turns from planet13 to star10. Is the action: satellite1's instrument2 takes an image of groundstation2 in image6 executable at step 5, True or False?", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "action_sequence": ["On satellite0 instrument1 is switched on", "Satellite0 turns to groundstation0 from groundstation2", "Instrument1 is calibrated on satellite0 to groundstation0", "Satellite0 turns from groundstation0 to planet11", "Instrument2 which is on satellite1 takes an image of groundstation2 in image6", "Image of planet11 is taken with instrument1 on satellite0 in image6", "Satellite0 turns from planet11 to planet13", "Instrument1 which is on satellite0 takes an image of planet13 in image5", "Instrument1 which is on satellite0 takes an image of planet13 in spectrograph2", "Satellite0 turns from planet13 to star10"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet11, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Satellite1: carries instrument4, pointing planet13, has power supply.\nBased on the domain description, an instrument on a satellite can 'take' an image of the target in the specific format. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing the target, the instrument is on the satellite, the instrument is turned on, the instrument is calibrated, the instrument supports the specific format.\nthe satellite is currently pointing the target ::: Satellite1: carries instrument4, pointing planet13. The target is groundstation2 ===> NOT SATISFY\nthe instrument is on the satellite ::: Instrument2 is on satellite0, but the action specifies satellite1 ===> NOT SATISFY\nthe instrument is turned on ::: Instrument2 is not mentioned as switched on ===> NOT SATISFY\nthe instrument is calibrated ::: Instrument2: is calibrated ===> SATISFY\nthe instrument supports the specific format ::: Instrument2: compatible with image0, compatible with image1. The format is image6 ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 4, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "83de7d30-e677-42ff-8440-e0b177f6874c", "domain": "satellite", "question": "Is it possible to execute it, True or False? Given the initial condition, the following actions are planned to be performed: from groundstation2, satellite0 turns to groundstation0, image of planet11 is taken with instrument1 on satellite0 in image6, image of planet13 is taken with instrument1 on satellite0 in image5, image of planet13 is taken with instrument1 on satellite0 in spectrograph2, instrument1 is calibrated on satellite0 to groundstation0, instrument1 on satellite0 is switched on, instrument1 which is on satellite0 takes an image of planet11 in image5, satellite0 turns from groundstation0 to planet11, satellite0 turns from planet13 to star10 and satellite0 turns to planet13 from planet11.", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "action_sequence": ["From groundstation2 satellite0 turns to groundstation0", "Image of planet11 is taken with instrument1 on satellite0 in image6", "Image of planet13 is taken with instrument1 on satellite0 in image5", "Image of planet13 is taken with instrument1 on satellite0 in spectrograph2", "Instrument1 is calibrated on satellite0 to groundstation0", "Instrument1 on satellite0 is switched on", "Instrument1 which is on satellite0 takes an image of planet11 in image5", "Satellite0 turns from groundstation0 to planet11", "Satellite0 turns from planet13 to star10", "Satellite0 turns to planet13 from planet11"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation0, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument1: on satellite0, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation0, has power supply.\nBased on the domain description, an instrument on a satellite can 'take' an image of the target in the specific format. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing the target, the instrument is on the satellite, the instrument is turned on, the instrument is calibrated, the instrument supports the specific format.\nthe satellite is currently pointing the target ::: Satellite0: pointing groundstation0. The target is planet11. ===> NOT SATISFY\nthe instrument is on the satellite ::: Instrument1: on satellite0 ===> SATISFY\nthe instrument is turned on ::: Instrument1: is calibrated implies it is turned on ===> SATISFY\nthe instrument is calibrated ::: Instrument1: is calibrated ===> SATISFY\nthe instrument supports the specific format ::: Instrument1: compatible with image6 ===> SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 1, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "393e950f-6bd1-47f4-8be4-4d3a9dafa849", "domain": "satellite", "question": "Given the initial condition, the following actions are planned to be performed: on satellite1, instrument3 is switched on. Is it possible to execute it, True or False?", "initial_state": "Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["On satellite1 instrument3 is switched on"], "state_progression": ["Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply."], "action_taken": 1, "state_checker_response": "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.\nBased on the domain description, an instrument on a satellite can be \"switched(turned) on\". This action is executable only if all following preconditions are satisfied: the instrument is on the satellite, the satellite has power supply.\nthe instrument is on the satellite ::: Instrument3: on satellite1 ===> SATISFY\nthe satellite has power supply ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply ===> SATISFY\nSince all preconditions are satisfied, this action is executable.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "feb6a2e0-8d76-4a21-b2f1-a212fb7d3947", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: instrument1 that is on satellite1 is turned on, satellite1 turns from star3 to star1, instrument1 that is on satellite1 is calibrated to star1, from star1, satellite1 turns to phenomenon10, satellite1's instrument1 takes an image of phenomenon10 in image5, instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3, satellite1 turns to phenomenon11 from phenomenon10, satellite1's instrument1 takes an image of phenomenon11 in spectrograph1, satellite1 turns to phenomenon5 from phenomenon11, satellite1's instrument1 takes an image of phenomenon5 in image4, satellite1's instrument1 takes an image of phenomenon5 in image5, satellite1 turns to phenomenon7 from phenomenon5, image of phenomenon7 is taken with instrument1 on satellite1 in image0, image of phenomenon7 is taken with instrument1 on satellite1 in image4, satellite1 turns to phenomenon9 from phenomenon7, image of phenomenon9 is taken with instrument1 on satellite1 in image5, image of phenomenon9 is taken with instrument1 on satellite1 in spectrograph1, satellite1 turns from phenomenon9 to planet8 and instrument1 which is on satellite1 takes an image of planet8 in image5. Is the action: instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3 executable at step 6, True or False?", "initial_state": "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, has power supply, pointing to star3.", "action_sequence": ["instrument1 that is on satellite1 is turned on", "satellite1 turns from star3 to star1", "instrument1 that is on satellite1 is calibrated to star1", "from star1 satellite1 turns to phenomenon10", "satellite1's instrument1 takes an image of phenomenon10 in image5", "instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3", "satellite1 turns to phenomenon11 from phenomenon10", "satellite1's instrument1 takes an image of phenomenon11 in spectrograph1", "satellite1 turns to phenomenon5 from phenomenon11", "satellite1's instrument1 takes an image of phenomenon5 in image4", "satellite1's instrument1 takes an image of phenomenon5 in image5", "satellite1 turns to phenomenon7 from phenomenon5", "image of phenomenon7 is taken with instrument1 on satellite1 in image0", "image of phenomenon7 is taken with instrument1 on satellite1 in image4", "satellite1 turns to phenomenon9 from phenomenon7", "image of phenomenon9 is taken with instrument1 on satellite1 in image5", "image of phenomenon9 is taken with instrument1 on satellite1 in spectrograph1", "satellite1 turns from phenomenon9 to planet8", "instrument1 which is on satellite1 takes an image of planet8 in image5"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star3.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star1.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star1.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon11.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon11.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon5.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon5.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4, has phenomenon5's image in image5. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon5.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4, has phenomenon5's image in image5. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon7.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4, has phenomenon5's image in image5, has phenomenon7's image in image0. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon7.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4, has phenomenon5's image in image5, has phenomenon7's image in image0, has phenomenon7's image in image4. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon7.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4, has phenomenon5's image in image5, has phenomenon7's image in image0, has phenomenon7's image in image4. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon9.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4, has phenomenon5's image in image5, has phenomenon7's image in image0, has phenomenon7's image in image4, has phenomenon9's image in image5. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon9.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4, has phenomenon5's image in image5, has phenomenon7's image in image0, has phenomenon7's image in image4, has phenomenon9's image in image5, has phenomenon9's image in spectrograph1. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon9.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4, has phenomenon5's image in image5, has phenomenon7's image in image0, has phenomenon7's image in image4, has phenomenon9's image in image5, has phenomenon9's image in spectrograph1. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to planet8.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4, has phenomenon5's image in image5, has phenomenon7's image in image0, has phenomenon7's image in image4, has phenomenon9's image in image5, has phenomenon9's image in spectrograph1, has planet8's image in image5. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to planet8."], "action_taken": 19, "state_checker_response": "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4, has phenomenon5's image in image5, has phenomenon7's image in image0, has phenomenon7's image in image4, has phenomenon9's image in image5, has phenomenon9's image in spectrograph1. Satellite1: carries instrument1, doesn't have power supply, pointing to planet8.\nBased on the domain description, an instrument on a satellite can 'take' an image of the target in the specific format. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing the target, the instrument is on the satellite, the instrument is turned on, the instrument is calibrated, the instrument supports the specific format.\nthe satellite is currently pointing the target ::: Satellite1: carries instrument1, doesn't have power supply, pointing to planet8. ===> SATISFY\nthe instrument is on the satellite ::: Instrument1: on satellite1 ===> SATISFY\nthe instrument is turned on ::: Instrument1: switched on ===> SATISFY\nthe instrument is calibrated ::: Instrument1: is calibrated ===> SATISFY\nthe instrument supports the specific format ::: Instrument1: compatible with image5 ===> SATISFY\nSince all preconditions are satisfied, this action is executable.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "c8d21922-05fb-4c1d-8b0b-d638a9818f9e", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: on satellite1, instrument3 is switched on, on satellite0, instrument0 is switched on, satellite1 turns from groundstation4 to star6, instrument3 is calibrated on satellite1 to star6, from star6, satellite1 turns to planet14, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, from planet14, satellite1 turns to star10, instrument3 which is on satellite1 takes an image of star10 in spectrograph1, satellite1 turns to star12 from star10, image of star12 is taken with instrument3 on satellite1 in spectrograph1, from star12, satellite1 turns to star0, from star1, satellite0 turns to groundstation2, instrument0 that is on satellite0 is calibrated to groundstation2, from groundstation2, satellite0 turns to phenomenon15, instrument0 which is on satellite0 takes an image of phenomenon15 in spectrograph0, satellite0 turns from phenomenon15 to star11, satellite0's instrument0 takes an image of star11 in thermograph4, satellite0 turns to star13 from star11 and instrument0 which is on satellite0 takes an image of star13 in spectrograph0. Is the action: satellite1 turns to star10 from planet14 executable at step 7, True or False?", "initial_state": "We can find following objects in the intial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, for groundstation4, instrument0 is calibrated, instrument0 is on board satellite0, spectrograph0 is compatible with instrument0, thermograph4 is supported by instrument0. ::: Instrument0: on satellite0, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, spectrograph0 is compatible with instrument1. ::: Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument2 supports spectrograph2. ::: Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. instrument3 is calibrated for star6, instrument3 is on board satellite1, instrument3 supports spectrograph1, spectrograph2 is compatible with instrument3. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. instrument0 is on board satellite0, instrument1 is on board satellite0, satellite0 has power available, star1 is where satellite0 is pointed. ::: Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. instrument2 is on board satellite1, instrument3 is on board satellite1, satellite1 has power, satellite1 is aimed towards groundstation4. ::: Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.", "action_sequence": ["On satellite1 instrument3 is switched on", "On satellite0 instrument0 is switched on", "Satellite1 turns from groundstation4 to star6", "Instrument3 is calibrated on satellite1 to star6", "From star6 satellite1 turns to planet14", "Image of planet14 is taken with instrument3 on satellite1 in spectrograph1", "From planet14 satellite1 turns to star10", "Instrument3 which is on satellite1 takes an image of star10 in spectrograph1", "Satellite1 turns to star12 from star10", "Image of star12 is taken with instrument3 on satellite1 in spectrograph1", "From star12 satellite1 turns to star0", "From star1 satellite0 turns to groundstation2", "Instrument0 that is on satellite0 is calibrated to groundstation2", "From groundstation2 satellite0 turns to phenomenon15", "Instrument0 which is on satellite0 takes an image of phenomenon15 in spectrograph0", "Satellite0 turns from phenomenon15 to star11", "Satellite0's instrument0 takes an image of star11 in thermograph4", "Satellite0 turns to star13 from star11", "Instrument0 which is on satellite0 takes an image of star13 in spectrograph0"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to groundstation4.", "Instrument0: on satellite0, switched on, not calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to groundstation4.", "Instrument0: on satellite0, switched on, not calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star6.", "Instrument0: on satellite0, switched on, not calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star6.", "Instrument0: on satellite0, switched on, not calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to planet14.", "Instrument0: on satellite0, switched on, not calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to planet14.", "Instrument0: on satellite0, switched on, not calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star10.", "Instrument0: on satellite0, switched on, not calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star10.", "Instrument0: on satellite0, switched on, not calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star12.", "Instrument0: on satellite0, switched on, not calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star12.", "Instrument0: on satellite0, switched on, not calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, not calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to groundstation2. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to groundstation2. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to phenomenon15. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to phenomenon15. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star11. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0, has star11's image in thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star11. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0, has star11's image in thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star13. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0, has star11's image in thermograph4, has star13's image in spectrograph0. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star13. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0."], "action_taken": 19, "state_checker_response": "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument0: on satellite0, switched on, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0, has star11's image in thermograph4. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star13.\nBased on the domain description, an instrument on a satellite can 'take' an image of the target in the specific format. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing the target, the instrument is on the satellite, the instrument is turned on, the instrument is calibrated, the instrument supports the specific format.\nthe satellite is currently pointing the target ::: Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star13. ===> SATISFY\nthe instrument is on the satellite ::: Instrument0: on satellite0 ===> SATISFY\nthe instrument is turned on ::: Instrument0: on satellite0, switched on ===> SATISFY\nthe instrument is calibrated ::: Instrument0: on satellite0, is calibrated ===> SATISFY\nthe instrument supports the specific format ::: Instrument0: on satellite0, compatible with spectrograph0 ===> SATISFY\nSince all preconditions are satisfied, this action is executable.\nFinal Answer: True.", "answer": 1, "label": "False"}
{"question_id": "dde203f2-bcac-4493-93a8-f75626b8cdb4", "domain": "satellite", "question": "Given the initial condition, the following actions are planned to be performed: instrument1 that is on satellite0 is turned on. Is it possible to execute it, True or False? Question extracted from the given content: Given the initial condition, the following actions are planned to be performed: instrument1 that is on satellite0 is turned on. Is it possible to execute it, True or False?", "initial_state": "We can find following objects in the initial state description: instrument0, instrument1, instrument2, instrument3, instrument4, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. instrument0 is on board satellite0, instrument0 is calibrated for groundstation7, instrument0 is calibrated for star3, instrument0 supports image6. ::: Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. satellite0 carries instrument1 on board, calibration of instrument1 for groundstation0 is complete, instrument1 is calibrated for groundstation6, image5 is supported by instrument1, image6 is supported by instrument1, instrument1 supports spectrograph2. ::: Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. satellite0 carries instrument2 on board, image0 is compatible with instrument2, image1 is compatible with instrument2, instrument2 is calibrated for star4. ::: Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. satellite0 carries instrument3 on board, calibration of instrument3 for groundstation9 is complete, instrument3 supports image1, instrument3 supports spectrograph4, thermograph3 is compatible with instrument3. ::: Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. satellite1 carries instrument4 on board, infrared7 is compatible with instrument4, instrument4 is calibrated for groundstation8, instrument4 supports image1. ::: Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. satellite0 carries instrument0 on board, satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, groundstation2 is where satellite0 is pointed, power is available for satellite0. ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply. satellite1 carries instrument4 on board, satellite1 has power, satellite1 is pointing to planet13. ::: Satellite1: carries instrument4, pointing planet13, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "action_sequence": ["instrument1 that is on satellite0 is turned on"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply."], "action_taken": 1, "state_checker_response": "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument1: on satellite0, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply.\nBased on the domain description, an instrument on a satellite can be \"switched(turned) on\". This action is executable only if all following preconditions are satisfied: the instrument is on the satellite, the satellite has power supply.\nthe instrument is on the satellite ::: Instrument1: on satellite0 ===> SATISFY\nthe satellite has power supply ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply. ===> SATISFY\nSince all preconditions are satisfied, this action is executable.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "8491036c-5bc2-4b8b-b169-f5596f2a723a", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: on satellite1, instrument3 is switched on. Is the action: instrument3 on satellite1 is switched on executable at step 1, True or False?", "initial_state": "We can find following objects in the intial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. for star9, instrument0 is calibrated, infrared1 is compatible with instrument0, instrument0 is calibrated for star1, instrument0 supports image3, satellite0 has instrument0 on board. ::: Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. for groundstation0, instrument1 is calibrated, image0 is compatible with instrument1, image3 is compatible with instrument1, infrared1 is supported by instrument1, satellite0 carries instrument1 on board. ::: Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, image2 is compatible with instrument2, image3 is compatible with instrument2, instrument2 is on board satellite0. ::: Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. satellite1 carries instrument3 on board, calibration of instrument3 for star6 is complete, instrument3 is calibrated for star8, instrument3 is calibrated for groundstation5, image0 is supported by instrument3, image2 is compatible with instrument3, image3 is supported by instrument3. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. satellite0 carries instrument1 on board, instrument2 is on board satellite0, satellite0 has instrument0 on board, satellite0 has power available, groundstation3 is where satellite0 is pointed. ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. satellite1 carries instrument3 on board, satellite1 has power available, and satellite1 is pointing to phenomenon10. ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["instrument3 on satellite1 is switched on at step 1"], "state_progression": ["Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply."], "action_taken": 1, "state_checker_response": "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.\nBased on the domain description, an instrument on a satellite can be \"switched(turned) on\". This action is executable only if all following preconditions are satisfied: the instrument is on the satellite, the satellite has power supply.\nthe instrument is on the satellite ::: Instrument3: on satellite1 ===> SATISFY\nthe satellite has power supply ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply. ===> SATISFY\nSince all preconditions are satisfied, this action is executable.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "9c0177f4-e504-447c-8b07-90bc18842d30", "domain": "satellite", "question": "Given the initial condition, the following actions are planned to be performed: satellite1's instrument1 takes an image of phenomenon5 in image5. Is it possible to execute it, True or False? Question extracted from the given content: Is it possible to execute it, True or False?", "initial_state": "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, has power supply, pointing to star3.", "action_sequence": ["satellite1's instrument1 takes an image of phenomenon5 in image5"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite1: carries instrument1, has power supply, pointing to star3.\nBased on the domain description, an instrument on a satellite can 'take' an image of the target in the specific format. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing the target, the instrument is on the satellite, the instrument is turned on, the instrument is calibrated, the instrument supports the specific format.\nthe satellite is currently pointing the target ::: Satellite1: carries instrument1, has power supply, pointing to star3. The target is phenomenon5. ===> NOT SATISFY\nthe instrument is on the satellite ::: Instrument1: on satellite1 ===> SATISFY\nthe instrument is turned on ::: Instrument1's state is not explicitly given, but since it is calibrated, it must be turned on (as calibration requires the instrument to be turned on). ===> SATISFY\nthe instrument is calibrated ::: Instrument1: is calibrated ===> SATISFY\nthe instrument supports the specific format ::: Instrument1: compatible with image5 ===> SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "b259af63-6e1a-4013-833d-a93edbcf545c", "domain": "satellite", "question": "Given the initial condition, the following actions are planned to be performed: satellite0 turns from phenomenon16 to planet11. Is it possible to execute it, True or False? Question extracted from the given content: Is it possible to execute it, True or False that satellite0 turns from phenomenon16 to planet11?", "initial_state": "We can find following objects in the initial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. for star9, instrument0 is calibrated, instrument0 is calibrated for star1, infrared1 is compatible with instrument0, instrument0 supports image3, satellite0 has instrument0 on board. ::: Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. for groundstation0, instrument1 is calibrated, image0 is compatible with instrument1, image3 is compatible with instrument1, infrared1 is supported by instrument1, satellite0 carries instrument1 on board. ::: Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, image2 is compatible with instrument2, image3 is compatible with instrument2, instrument2 is on board satellite0. ::: Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. satellite1 carries instrument3 on board, calibration of instrument3 for star6 is complete, instrument3 is calibrated for star8, instrument3 is calibrated for groundstation5, image0 is supported by instrument3, image2 is compatible with instrument3, image3 is supported by instrument3. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. satellite0 carries instrument1 on board, instrument2 is on board satellite0, satellite0 has instrument0 on board, satellite0 has power available, groundstation3 is where satellite0 is pointed. ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. satellite1 carries instrument3 on board, satellite1 has power available, and satellite1 is pointing to phenomenon10. ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["satellite0 turns from phenomenon16 to planet11"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "ea485949-73b6-488f-bf41-23be55ea7d42", "domain": "satellite", "question": "In this state, on satellite1, instrument3 is switched on. Is it possible to execute it, True or False?", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.", "action_sequence": ["On satellite1 instrument3 is switched on"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to groundstation4."], "action_taken": 1, "state_checker_response": "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.\nBased on the domain description, an instrument on a satellite can be \"switched(turned) on\". This action is executable only if all following preconditions are satisfied: the instrument is on the satellite, the satellite has power supply.\nthe instrument is on the satellite ::: Instrument3: on satellite1 ===> SATISFY\nthe satellite has power supply ::: Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4. ===> SATISFY\nSince all preconditions are satisfied, this action is executable.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "8676865d-ea5f-4934-b6b7-6d8be8a5a448", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: on satellite1, instrument3 is switched on, instrument0 that is on satellite0 is turned on, satellite1 turns from phenomenon10 to groundstation5, instrument3 is calibrated on satellite1 to groundstation5, from groundstation5, satellite1 turns to phenomenon16, instrument3 which is on satellite1 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, satellite1's instrument3 takes an image of phenomenon17 in image3, satellite1 turns from phenomenon17 to planet11, satellite1's instrument3 takes an image of planet11 in image3, from planet11, satellite1 turns to planet13, image of planet13 is taken with instrument3 on satellite1 in image0, from planet13, satellite1 turns to planet14, image of planet14 is taken with instrument3 on satellite1 in image0, from planet14, satellite1 turns to star15, image of star15 is taken with instrument3 on satellite1 in image2, satellite0 turns to star1 from groundstation3, instrument0 is calibrated on satellite0 to star1 and from star1, satellite0 turns to phenomenon10. Is the action: image of phenomenon16 is taken with instrument3 on satellite1 in image3 executable at step 6, True or False?", "initial_state": "We can find following objects in the initial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. for star9, instrument0 is calibrated, instrument0 is calibrated for star1, infrared1 is compatible with instrument0, instrument0 supports image3, satellite0 has instrument0 on board. ::: Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. for groundstation0, instrument1 is calibrated, image0 is compatible with instrument1, image3 is compatible with instrument1, infrared1 is supported by instrument1, satellite0 carries instrument1 on board. ::: Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, image2 is compatible with instrument2, image3 is compatible with instrument2, instrument2 is on board satellite0. ::: Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. satellite1 carries instrument3 on board, calibration of instrument3 for star6 is complete, instrument3 is calibrated for star8, instrument3 is calibrated for groundstation5, image0 is supported by instrument3, image2 is compatible with instrument3, image3 is supported by instrument3. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. satellite0 carries instrument1 on board, instrument2 is on board satellite0, satellite0 has instrument0 on board, satellite0 has power available, groundstation3 is where satellite0 is pointed. ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. satellite1 carries instrument3 on board, satellite1 has power available, and satellite1 is pointing to phenomenon10. ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["On satellite1 instrument3 is switched on", "On satellite0 instrument0 is turned on", "Satellite1 turns from phenomenon10 to groundstation5", "Instrument3 is calibrated on satellite1 to groundstation5", "From groundstation5 satellite1 turns to phenomenon16", "Instrument3 which is on satellite1 takes an image of phenomenon16 in image3", "From phenomenon16 satellite1 turns to phenomenon17", "Satellite1's instrument3 takes an image of phenomenon17 in image3", "Satellite1 turns from phenomenon17 to planet11", "Satellite1's instrument3 takes an image of planet11 in image3", "From planet11 satellite1 turns to planet13", "Image of planet13 is taken with instrument3 on satellite1 in image0", "From planet13 satellite1 turns to planet14", "Image of planet14 is taken with instrument3 on satellite1 in image0", "From planet14 satellite1 turns to star15", "Image of star15 is taken with instrument3 on satellite1 in image2", "Satellite0 turns to star1 from groundstation3", "Instrument0 is calibrated on satellite0 to star1", "From star1 satellite0 turns to phenomenon10"], "state_progression": ["Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to groundstation5, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to groundstation5, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon16, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon16, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon17, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon17, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet11, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet11, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet13, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet13, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet14, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet14, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to star1, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply.", "Instrument0: on satellite0, switched on, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to star1, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply.", "Instrument0: on satellite0, switched on, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to phenomenon10, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply."], "action_taken": 19, "state_checker_response": "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite0: carries instrument0, carries instrument1, carries instrument2, pointing to star1, doesn't have power supply.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to star1, doesn't have power supply. ===> SATISFY\nSince all preconditions are satisfied, this action is executable.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "0ae8f47b-d863-45c6-a1e5-865c5da1eb1b", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: on satellite1, instrument3 is switched on, on satellite0, instrument0 is switched on, from phenomenon10, satellite1 turns to groundstation5, instrument3 is calibrated on satellite1 to groundstation5, satellite1 turns from groundstation5 to phenomenon16, satellite1's instrument3 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, instrument3 which is on satellite1 takes an image of phenomenon17 in image3, satellite1 turns to planet11 from phenomenon17 and satellite1 turns to groundstation7 from phenomenon10. Is the action: from phenomenon10, satellite1 turns to groundstation7 executable at step 10, True or False?", "initial_state": "We can find following objects in the initial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. for star9, instrument0 is calibrated, infrared1 is compatible with instrument0, instrument0 is calibrated for star1, instrument0 supports image3, satellite0 has instrument0 on board. ::: Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. for groundstation0, instrument1 is calibrated, image0 is compatible with instrument1, image3 is compatible with instrument1, infrared1 is supported by instrument1, satellite0 carries instrument1 on board. ::: Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, image2 is compatible with instrument2, image3 is compatible with instrument2, instrument2 is on board satellite0. ::: Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. satellite1 carries instrument3 on board, calibration of instrument3 for star6 is complete, instrument3 is calibrated for star8, instrument3 is calibrated for groundstation5, image0 is supported by instrument3, image2 is compatible with instrument3, image3 is supported by instrument3. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. satellite0 carries instrument1 on board, instrument2 is on board satellite0, satellite0 has instrument0 on board, satellite0 has power available, groundstation3 is where satellite0 is pointed. ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. satellite1 carries instrument3 on board, satellite1 has power available, and satellite1 is pointing to phenomenon10. ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["On satellite1 instrument3 is switched on", "On satellite0 instrument0 is switched on", "From phenomenon10 satellite1 turns to groundstation5", "Instrument3 is calibrated on satellite1 to groundstation5", "Satellite1 turns from groundstation5 to phenomenon16", "Satellite1's instrument3 takes an image of phenomenon16 in image3", "From phenomenon16 satellite1 turns to phenomenon17", "Instrument3 which is on satellite1 takes an image of phenomenon17 in image3", "Satellite1 turns to planet11 from phenomenon17", "Satellite1 turns to groundstation7 from phenomenon10"], "state_progression": ["Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to groundstation5, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to groundstation5, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon16, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon16, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon17, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon17, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet11, doesn't have power supply.", "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite1: carries instrument3, pointing to planet11, doesn't have power supply.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite1: carries instrument3, pointing to planet11.  ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal answer: False."], "action_taken": 9, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "a850070b-6c01-4ec4-9a5a-9eeceab88c62", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 19 the following actions are planned to be performed: instrument1 on satellite1 is switched on, from star3, satellite1 turns to star1, instrument1 is calibrated on satellite1 to star1, satellite1 turns to phenomenon10 from star1, image of phenomenon10 is taken with instrument1 on satellite1 in image5, instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3, satellite1 turns to phenomenon11 from phenomenon10, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, from phenomenon11, satellite1 turns to phenomenon5, satellite0 turns to star6 from groundstation0, satellite1's instrument1 takes an image of phenomenon5 in image5, satellite1 turns from phenomenon5 to phenomenon7, image of phenomenon7 is taken with instrument1 on satellite1 in image0, image of phenomenon7 is taken with instrument1 on satellite1 in image4, from phenomenon7, satellite1 turns to phenomenon9, satellite1's instrument1 takes an image of phenomenon9 in image5, satellite1's instrument1 takes an image of phenomenon9 in spectrograph1, satellite1 turns from phenomenon9 to planet8 and instrument1 which is on satellite1 takes an image of planet8 in image5. Is the action: from groundstation0, satellite0 turns to star6 executable at step 10, True or False?", "initial_state": "We can find following objects in the intial state description: instrument0, instrument1, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. image2 is supported by instrument0, for star1, instrument0 is calibrated, satellite0 carries instrument0 on board. ::: Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Calibration of instrument1 for star1 is complete, image0 is compatible with instrument1, image2 is compatible with instrument1, instrument1 supports image4, instrument1 supports image5, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1, satellite1 carries instrument1 on board. ::: Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. satellite0 carries instrument0 on board, satellite0 has power available, phenomenon5 is where satellite0 is pointed. ::: Satellite0: carries instrument0, has power supply, pointing to phenomenon5. satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3. ::: Satellite1: carries instrument1, has power supply, pointing to star3. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, has power supply, pointing to star3.", "action_sequence": ["instrument1 on satellite1 is switched on", "satellite1 turns to star1 from star3", "instrument1 is calibrated on satellite1 to star1", "satellite1 turns to phenomenon10 from star1", "image of phenomenon10 is taken with instrument1 on satellite1 in image5", "instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3", "satellite1 turns to phenomenon11 from phenomenon10", "image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1", "satellite1 turns to phenomenon5 from phenomenon11", "satellite0 turns to star6 from groundstation0", "satellite1's instrument1 takes an image of phenomenon5 in image5", "satellite1 turns from phenomenon5 to phenomenon7", "image of phenomenon7 is taken with instrument1 on satellite1 in image0", "image of phenomenon7 is taken with instrument1 on satellite1 in image4", "satellite1 turns to phenomenon9 from phenomenon7", "satellite1's instrument1 takes an image of phenomenon9 in image5", "satellite1's instrument1 takes an image of phenomenon9 in spectrograph1", "satellite1 turns from phenomenon9 to planet8", "instrument1 which is on satellite1 takes an image of planet8 in image5"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star3.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star1.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star1.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon11.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon11.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon5.", "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite0: carries instrument0, has power supply, pointing to phenomenon5.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite0: carries instrument0, has power supply, pointing to phenomenon5. ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal answer: False."], "action_taken": 9, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "ff71c551-4e12-40e9-a5db-e51cef08a80c", "domain": "satellite", "question": "Is it possible to execute it, True or False? Given the initial condition, the following actions are planned to be performed: from groundstation0, satellite0 turns to planet11, from planet11, satellite0 turns to planet13, image of planet13 is taken with instrument1 on satellite0 in image5, instrument1 on satellite0 is switched on, instrument1 that is on satellite0 is calibrated to groundstation0, instrument1 which is on satellite0 takes an image of planet11 in image5, instrument1 which is on satellite0 takes an image of planet13 in spectrograph2, satellite0 turns to groundstation0 from groundstation2, satellite0 turns to star10 from planet13 and satellite0 turns to star12 from phenomenon14.", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "action_sequence": ["From groundstation0 satellite0 turns to planet11", "From planet11 satellite0 turns to planet13", "Image of planet13 is taken with instrument1 on satellite0 in image5", "Instrument1 on satellite0 is switched on", "Instrument1 that is on satellite0 is calibrated to groundstation0", "Instrument1 which is on satellite0 takes an image of planet11 in image5", "Instrument1 which is on satellite0 takes an image of planet13 in spectrograph2", "Satellite0 turns to groundstation0 from groundstation2", "Satellite0 turns to star10 from planet13", "Satellite0 turns to star12 from phenomenon14"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply. ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "046df4ed-b2cd-4755-975a-9487f85e2228", "domain": "satellite", "question": "Given the initial condition, the following actions are planned to be performed: from phenomenon10, satellite1 turns to phenomenon11, from phenomenon11, satellite1 turns to phenomenon5, from phenomenon5, satellite1 turns to phenomenon7, from star3, satellite1 turns to star1, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, image of phenomenon5 is taken with instrument1 on satellite1 in image4, image of phenomenon5 is taken with instrument1 on satellite1 in image5, image of phenomenon7 is taken with instrument1 on satellite1 in image0, instrument1 is calibrated on satellite1 to star1, instrument1 on satellite1 is switched on, instrument1 which is on satellite1 takes an image of phenomenon10 in image5, instrument1 which is on satellite1 takes an image of phenomenon7 in image4, satellite1 turns from phenomenon7 to phenomenon9, satellite1 turns from star1 to phenomenon10, satellite1 turns to planet8 from phenomenon9, satellite1's instrument0 takes an image of planet8 in spectrograph3, satellite1's instrument1 takes an image of phenomenon9 in image5, satellite1's instrument1 takes an image of phenomenon9 in spectrograph1 and satellite1's instrument1 takes an image of planet8 in image5. Is it possible to execute it, True or False?", "initial_state": "We can find following objects in the intial state description: instrument0, instrument1, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. for star1, instrument0 is calibrated, image2 is supported by instrument0, satellite0 carries instrument0 on board. ::: Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Calibration of instrument1 for star1 is complete, image0 is compatible with instrument1, image2 is compatible with instrument1, instrument1 supports image4, instrument1 supports image5, spectrograph1 is compatible with instrument1 and spectrograph3 is supported by instrument1, satellite1 carries instrument1 on board. ::: Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. satellite0 carries instrument0 on board, satellite0 has power available, phenomenon5 is where satellite0 is pointed. ::: Satellite0: carries instrument0, has power supply, pointing to phenomenon5. satellite1 carries instrument1 on board, satellite1 has power, satellite1 is pointing to star3. ::: Satellite1: carries instrument1, has power supply, pointing to star3. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, has power supply, pointing to star3.", "action_sequence": ["From phenomenon10 satellite1 turns to phenomenon11", "From phenomenon11 satellite1 turns to phenomenon5", "From phenomenon5 satellite1 turns to phenomenon7", "From star3 satellite1 turns to star1", "Image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1", "Image of phenomenon5 is taken with instrument1 on satellite1 in image4", "Image of phenomenon5 is taken with instrument1 on satellite1 in image5", "Image of phenomenon7 is taken with instrument1 on satellite1 in image0", "Instrument1 is calibrated on satellite1 to star1", "Instrument1 on satellite1 is switched on", "Instrument1 which is on satellite1 takes an image of phenomenon10 in image5", "Instrument1 which is on satellite1 takes an image of phenomenon7 in image4", "Satellite1 turns from phenomenon7 to phenomenon9", "Satellite1 turns from star1 to phenomenon10", "Satellite1 turns to planet8 from phenomenon9", "Satellite1's instrument0 takes an image of planet8 in spectrograph3", "Satellite1's instrument1 takes an image of phenomenon9 in image5", "Satellite1's instrument1 takes an image of phenomenon9 in spectrograph1", "Satellite1's instrument1 takes an image of planet8 in image5"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite1: carries instrument1, has power supply, pointing to star3.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite1: carries instrument1, has power supply, pointing to star3. ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "278b5a8f-1795-4dfc-abbe-772a55e80d6e", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: instrument3 that is on satellite1 is turned on, instrument0 that is on satellite0 is turned on, satellite1 turns from phenomenon10 to groundstation5, calibration of instrument3 which is on satellite1 to groundstation5 is complete, satellite1 turns to phenomenon16 from groundstation5, satellite1's instrument3 takes an image of phenomenon16 in image3, from phenomenon16, satellite1 turns to phenomenon17, satellite1's instrument3 takes an image of phenomenon17 in image3, satellite1 turns to planet11 from phenomenon17 and satellite1's instrument3 takes an image of planet11 in image3. Is the action: from phenomenon10, satellite1 turns to groundstation5 executable at step 3, True or False?", "initial_state": "Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["instrument3 that is on satellite1 is turned on", "instrument0 that is on satellite0 is turned on", "satellite1 turns from phenomenon10 to groundstation5", "calibration of instrument3 which is on satellite1 to groundstation5 is complete", "satellite1 turns to phenomenon16 from groundstation5", "satellite1's instrument3 takes an image of phenomenon16 in image3", "satellite1 turns to phenomenon17 from phenomenon16", "satellite1's instrument3 takes an image of phenomenon17 in image3", "satellite1 turns to planet11 from phenomenon17", "satellite1's instrument3 takes an image of planet11 in image3"], "state_progression": ["Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to groundstation5, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to groundstation5, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon16, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon16, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon17, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon17, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet11, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet11, doesn't have power supply."], "action_taken": 10, "state_checker_response": "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite1: carries instrument3, pointing to planet11, doesn't have power supply.\nBased on the domain description, an instrument on a satellite can 'take' an image of the target in the specific format. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing the target, the instrument is on the satellite, the instrument is turned on, the instrument is calibrated, the instrument supports the specific format.\nthe satellite is currently pointing the target ::: Satellite1: carries instrument3, pointing to planet11. ===> SATISFY\nthe instrument is on the satellite ::: Instrument3: on satellite1. ===> SATISFY\nthe instrument is turned on ::: Instrument3: on satellite1, switched on. ===> SATISFY\nthe instrument is calibrated ::: Instrument3: on satellite1, is calibrated. ===> SATISFY\nthe instrument supports the specific format ::: Instrument3: on satellite1, compatible with image3. ===> SATISFY\nSince all preconditions are satisfied, this action is executable.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "6a2e04fb-4c04-4efc-b031-cf5f1741bebb", "domain": "satellite", "question": "Is it possible to execute it, True or False? Given the initial condition, the following actions are planned to be performed: calibration of instrument0 which is on satellite0 to star1 is complete, from groundstation3, satellite0 turns to star1, from star1, satellite0 turns to phenomenon10, image of phenomenon16 is taken with instrument3 on satellite1 in image3, image of star15 is taken with instrument3 on satellite1 in image2, instrument0 on satellite0 is switched on, instrument3 that is on satellite1 is calibrated to groundstation5, instrument3 that is on satellite1 is turned on, instrument3 which is on satellite1 takes an image of planet11 in image3, satellite1 turns from phenomenon16 to phenomenon17, satellite1 turns from planet11 to planet13, satellite1 turns from planet14 to star15, satellite1 turns to phenomenon16 from groundstation5, satellite1 turns to planet11 from phenomenon17, satellite1 turns to planet14 from planet13, satellite1's instrument0 takes an image of groundstation0 in image2, satellite1's instrument3 takes an image of phenomenon17 in image3, satellite1's instrument3 takes an image of planet13 in image0 and satellite1's instrument3 takes an image of planet14 in image0.", "initial_state": "We can find following objects in the intial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. for star9, instrument0 is calibrated, infrared1 is compatible with instrument0, instrument0 is calibrated for star1, instrument0 supports image3, satellite0 has instrument0 on board. ::: Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. for groundstation0, instrument1 is calibrated, image0 is compatible with instrument1, image3 is compatible with instrument1, infrared1 is supported by instrument1, satellite0 carries instrument1 on board. ::: Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, image2 is compatible with instrument2, image3 is compatible with instrument2, instrument2 is on board satellite0. ::: Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. satellite1 carries instrument3 on board, calibration of instrument3 for star6 is complete, instrument3 is calibrated for star8, instrument3 is calibrated for groundstation5, image0 is supported by instrument3, image2 is compatible with instrument3, image3 is supported by instrument3. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. satellite0 carries instrument1 on board, instrument2 is on board satellite0, satellite0 has instrument0 on board, satellite0 has power available, groundstation3 is where satellite0 is pointed. ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. satellite1 carries instrument3 on board, satellite1 has power available, and satellite1 is pointing to phenomenon10. ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["The calibration of instrument0 which is on satellite0 to star1 is complete from groundstation3", "Satellite0 turns to star1 from groundstation3", "Satellite0 turns to phenomenon10 from star1", "The image of phenomenon16 is taken with instrument3 on satellite1 in image3", "The image of star15 is taken with instrument3 on satellite1 in image2", "Instrument0 on satellite0 is switched on", "Instrument3 that is on satellite1 is calibrated to groundstation5", "Instrument3 that is on satellite1 is turned on", "Instrument3 which is on satellite1 takes an image of planet11 in image3", "Satellite1 turns from phenomenon16 to phenomenon17", "Satellite1 turns from planet11 to planet13", "Satellite1 turns from planet14 to star15", "Satellite1 turns to phenomenon16 from groundstation5", "Satellite1 turns to planet11 from phenomenon17", "Satellite1 turns to planet14 from planet13", "Satellite1's instrument0 takes an image of groundstation0 in image2", "Satellite1's instrument3 takes an image of phenomenon17 in image3", "Satellite1's instrument3 takes an image of planet13 in image0", "Satellite1's instrument3 takes an image of planet14 in image0"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply.\nBased on the domain description, an instrument on the satellite can be \"calibrated\" for a target. This action is executable only if all following preconditions are satisfied: the instrument is on the satellite, the instrument can be calibrated using the target(calibration target), the satellite is pointing to the calibration target, the instrument is switched(turned) on.\nthe instrument is on the satellite ::: Instrument0: on satellite0 ===> SATISFY\nthe instrument can be calibrated using the target(calibration target) ::: Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, ===> SATISFY\nthe satellite is pointing to the calibration target ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3. ===> NOT SATISFY (satellite is pointing to groundstation3, not star1)\nthe instrument is switched(turned) on ::: Instrument0: on satellite0, is calibrated, ===> NOT SATISFY (instrument is calibrated but not explicitly stated as switched on)\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "058f8516-cd98-41f2-a220-b3235bf2ac2c", "domain": "satellite", "question": "Is it possible to execute it, True or False? In this state, is it possible to execute the following actions: calibration of instrument3 which is on satellite1 to star6 is complete, from groundstation4, satellite1 turns to star6, from star10, satellite1 turns to star12, from star6, satellite1 turns to planet14, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, image of star12 is taken with instrument3 on satellite1 in spectrograph1, instrument3 that is on satellite1 is turned on, satellite1 turns to star1 from groundstation5, satellite1 turns to star10 from planet14 and satellite1's instrument3 takes an image of star10 in spectrograph1. Respond with True or False.", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.", "action_sequence": ["The calibration of instrument3 which is on satellite1 to star6 is complete", "Satellite1 turns to star6 from groundstation4", "Satellite1 turns to star12 from star10", "Satellite1 turns to planet14 from star6", "The image of planet14 is taken with instrument3 on satellite1 in spectrograph1", "The image of star12 is taken with instrument3 on satellite1 in spectrograph1", "Instrument3 that is on satellite1 is turned on", "Satellite1 turns to star1 from groundstation5", "Satellite1 turns to star10 from planet14", "Satellite1's instrument3 takes an image of star10 in spectrograph1"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.\nBased on the domain description, an instrument on the satellite can be \"calibrated\" for a target. This action is executable only if all following preconditions are satisfied: the instrument is on the satellite, the instrument can be calibrated using the target(calibration target), the satellite is pointing to the calibration target, the instrument is switched(turned) on.\nthe instrument is on the satellite ::: Instrument3: on satellite1 ===> SATISFY\nthe instrument can be calibrated using the target(calibration target) ::: Instrument3: star6 is its calibration target ===> SATISFY\nthe satellite is pointing to the calibration target ::: Satellite1: pointing to groundstation4 ===> NOT SATISFY (should be pointing to star6)\nthe instrument is switched(turned) on ::: Instrument3: is calibrated (implies it was turned on) ===> SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "b08bcaf9-34ff-4051-af51-4a13b501da9e", "domain": "satellite", "question": "Is the action: instrument1 on satellite1 is switched on executable at step 1, True or False?", "initial_state": "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, has power supply, pointing to star3.", "action_sequence": ["instrument1 on satellite1 is switched on at step 1"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star3."], "action_taken": 1, "state_checker_response": "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite1: carries instrument1, has power supply, pointing to star3.\nBased on the domain description, an instrument on a satellite can be \"switched(turned) on\". This action is executable only if all following preconditions are satisfied: the instrument is on the satellite, the satellite has power supply.\nthe instrument is on the satellite ::: Instrument1: on satellite1 ===> SATISFY\nthe satellite has power supply ::: Satellite1: carries instrument1, has power supply, pointing to star3 ===> SATISFY\nSince all preconditions are satisfied, this action is executable.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "476081a9-2b78-4971-a9b2-aa54b087b174", "domain": "satellite", "question": "Is it possible to execute it, True or False? Given the initial condition, the following actions are planned to be performed: calibration of instrument2 which is on satellite0 to star4 is complete, from groundstation2, satellite0 turns to groundstation0, from star10, satellite0 turns to star4, from star4, satellite0 turns to star16, image of planet11 is taken with instrument1 on satellite0 in image6, instrument1 is calibrated on satellite0 to groundstation0, instrument1 which is on satellite0 takes an image of planet11 in image5, instrument1 which is on satellite0 takes an image of star10 in image6, instrument1 which is on satellite0 takes an image of star10 in spectrograph2, instrument2 on satellite0 is switched off, instrument2 that is on satellite0 is turned on, on satellite0, instrument1 is switched off, on satellite0, instrument1 is switched on, satellite0 turns from planet11 to planet13, satellite0 turns to planet11 from groundstation0, satellite0 turns to star10 from planet13, satellite0's instrument1 takes an image of planet13 in image5, satellite0's instrument1 takes an image of planet13 in spectrograph2 and satellite0's instrument2 takes an image of star16 in image0.", "initial_state": "We can find following objects in the intial state description: instrument0, instrument1, instrument2, instrument3, instrument4, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. instrument0 is on board satellite0, instrument0 is calibrated for groundstation7, instrument0 is calibrated for star3, instrument0 supports image6. ::: Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. calibration of instrument1 for groundstation0 is complete, instrument1 is calibrated for groundstation6, image5 is supported by instrument1, image6 is supported by instrument1, instrument1 supports spectrograph2, satellite0 carries instrument1 on board. ::: Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. image0 is compatible with instrument2, image1 is compatible with instrument2, instrument2 is calibrated for star4, satellite0 carries instrument2 on board. ::: Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. calibration of instrument3 for groundstation9 is complete, instrument3 supports image1, instrument3 supports spectrograph4, thermograph3 is compatible with instrument3, satellite0 carries instrument3 on board. ::: Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. infrared7 is compatible with instrument4, instrument4 is calibrated for groundstation8, instrument4 supports image1, satellite1 carries instrument4 on board. ::: Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, instrument0 is on board satellite0, groundstation2 is where satellite0 is pointed, power is available for satellite0. ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply. satellite1 carries instrument4 on board, satellite1 has power, satellite1 is pointing to planet13. ::: Satellite1: carries instrument4, pointing planet13, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, groundstation6 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with infrared7, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "action_sequence": ["The calibration of instrument2 which is on satellite0 to star4 is complete", "From groundstation2 satellite0 turns to groundstation0", "From star10 satellite0 turns to star4", "From star4 satellite0 turns to star16", "Image of planet11 is taken with instrument1 on satellite0 in image6", "Instrument1 is calibrated on satellite0 to groundstation0", "Instrument1 which is on satellite0 takes an image of planet11 in image5", "Instrument1 which is on satellite0 takes an image of star10 in image6", "Instrument1 which is on satellite0 takes an image of star10 in spectrograph2", "Instrument2 on satellite0 is switched off", "Instrument2 that is on satellite0 is turned on", "On satellite0 instrument1 is switched off", "On satellite0 instrument1 is switched on", "Satellite0 turns from planet11 to planet13", "Satellite0 turns to planet11 from groundstation0", "Satellite0 turns to star10 from planet13", "Satellite0's instrument1 takes an image of planet13 in image5", "Satellite0's instrument1 takes an image of planet13 in spectrograph2", "Satellite0's instrument2 takes an image of star16 in image0"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply.\nBased on the domain description, an instrument on the satellite can be \"calibrated\" for a target. This action is executable only if all following preconditions are satisfied: the instrument is on the satellite, the instrument can be calibrated using the target(calibration target), the satellite is pointing to the calibration target, the instrument is switched(turned) on.\nthe instrument is on the satellite ::: Instrument2: on satellite0 ===> SATISFY\nthe instrument can be calibrated using the target(calibration target) ::: Instrument2: on satellite0, is calibrated, star4 is its calibration target ===> SATISFY\nthe satellite is pointing to the calibration target ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2. ===> NOT SATISFY (pointing to groundstation2, not star4)\nthe instrument is switched(turned) on ::: Instrument2: on satellite0, is calibrated (but not explicitly stated as switched on) ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "dd2ee378-40f0-4040-88c2-06b4b46462e3", "domain": "satellite", "question": "Is the action: satellite1 turns from groundstation9 to star16 executable at step 1, True or False?", "initial_state": "We can find following objects in the intial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. Calibration of instrument0 for groundstation2 is complete, calibration of instrument0 for star0 is complete, for groundstation4, instrument0 is calibrated, spectrograph0 is compatible with instrument0, thermograph4 is supported by instrument0, instrument0 is on board satellite0. ::: Instrument0: on satellite0, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Calibration of instrument1 for groundstation2 is complete, calibration of instrument1 for star8 is complete, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, spectrograph0 is compatible with instrument1. ::: Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Calibration of instrument2 for groundstation4 is complete, calibration of instrument2 for groundstation9 is complete, for star7, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument2 is on board satellite1, instrument2 supports spectrograph0, instrument2 supports spectrograph2. ::: Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. instrument3 is calibrated for star6, instrument3 is on board satellite1, instrument3 supports spectrograph1, spectrograph2 is compatible with instrument3. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. instrument0 is on board satellite0, instrument1 is on board satellite0, satellite0 has power available, star1 is where satellite0 is pointed. ::: Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. instrument2 is on board satellite1, instrument3 is on board satellite1, satellite1 has power, satellite1 is aimed towards groundstation4. ::: Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.", "action_sequence": ["satellite1 turns to star16 from groundstation9"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4. ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "07c0a4c6-2bb2-4bd4-bf26-0c994f7452a3", "domain": "satellite", "question": "Given the initial condition, the following actions are planned to be performed: on satellite1, instrument1 is switched on. Is it possible to execute it, True or False? Question extracted from the given content: Is it possible to execute the action of switching on instrument1 on satellite1, True or False?", "initial_state": "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, has power supply, pointing to star3.", "action_sequence": ["instrument1 is switched on on satellite1"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star3."], "action_taken": 1, "state_checker_response": "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite1: carries instrument1, has power supply, pointing to star3.\nBased on the domain description, an instrument on a satellite can be \"switched(turned) on\". This action is executable only if all following preconditions are satisfied: the instrument is on the satellite, the satellite has power supply.\nthe instrument is on the satellite ::: Instrument1: on satellite1 ===> SATISFY\nthe satellite has power supply ::: Satellite1: carries instrument1, has power supply, pointing to star3. ===> SATISFY\nSince all preconditions are satisfied, this action is executable.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "37536d9f-d7c0-4509-b663-343b0f1bbbc8", "domain": "satellite", "question": "Given the initial condition, the following actions are planned to be performed: instrument0 which is on satellite1 takes an image of star8 in spectrograph2. Is it possible to execute it, True or False?", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, pointing star1, has power supply. Satellite1: carries instrument2, carries instrument3, pointing groundstation4, has power supply.", "action_sequence": ["instrument0 which is on satellite1 takes an image of star8 in spectrograph2"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument0: on satellite0, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Satellite1: carries instrument2, carries instrument3, pointing groundstation4, has power supply.\nBased on the domain description, an instrument on a satellite can 'take' an image of the target in the specific format. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing the target, the instrument is on the satellite, the instrument is turned on, the instrument is calibrated, the instrument supports the specific format.\nthe satellite is currently pointing the target ::: Satellite1: carries instrument2, carries instrument3, pointing groundstation4. ===> NOT SATISFY (target is star8, but satellite1 is pointing to groundstation4)\nthe instrument is on the satellite ::: Instrument0: on satellite0 ===> NOT SATISFY (instrument0 is on satellite0, not satellite1)\nthe instrument is turned on ::: Instrument0: on satellite0, is calibrated ===> NOT SATISFY (no information about turned on)\nthe instrument is calibrated ::: Instrument0: on satellite0, is calibrated ===> SATISFY\nthe instrument supports the specific format ::: Instrument0: compatible with spectrograph0, compatible with thermograph4 ===> NOT SATISFY (does not support spectrograph2)\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "6b8a1a1b-5918-4f27-85e9-80cbc21d4b30", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: from groundstation0, satellite0 turns to star1. In this state, is the action: satellite0 turns to star1 from groundstation0 executable at step 1, True or False?", "initial_state": "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, has power supply, pointing to star3.", "action_sequence": ["From groundstation0 satellite0 turns to star1"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite0: carries instrument0, has power supply, pointing to phenomenon5.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite0: carries instrument0, has power supply, pointing to phenomenon5. ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "29a02dc6-63e7-4753-a725-64b4907cf089", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: instrument1 that is on satellite1 is turned on, satellite1 turns to star1 from star3, instrument1 that is on satellite1 is calibrated to star1, satellite1 turns to phenomenon10 from star1, image of phenomenon10 is taken with instrument1 on satellite1 in image5, instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3, satellite1 turns to phenomenon11 from phenomenon10, image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1, instrument1 that is on satellite1 is calibrated to star1 and satellite1's instrument1 takes an image of phenomenon5 in image4. Is the action: calibration of instrument1 which is on satellite1 to star1 is complete executable at step 9, True or False?", "initial_state": "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, has power supply, pointing to star3.", "action_sequence": ["instrument1 that is on satellite1 is turned on", "satellite1 turns to star1 from star3", "instrument1 that is on satellite1 is calibrated to star1", "satellite1 turns to phenomenon10 from star1", "image of phenomenon10 is taken with instrument1 on satellite1 in image5", "instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3", "satellite1 turns to phenomenon11 from phenomenon10", "image of phenomenon11 is taken with instrument1 on satellite1 in spectrograph1", "instrument1 that is on satellite1 is calibrated to star1", "satellite1's instrument1 takes an image of phenomenon5 in image4"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star3.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star1.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star1.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon11.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon11.", "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon11.\nBased on the domain description, an instrument on the satellite can be \"calibrated\" for a target. This action is executable only if all following preconditions are satisfied: the instrument is on the satellite, the instrument can be calibrated using the target(calibration target), the satellite is pointing to the calibration target, the instrument is switched(turned) on.\nthe instrument is on the satellite ::: Instrument1: on satellite1 ===> SATISFY\nthe instrument can be calibrated using the target(calibration target) ::: Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target ===> SATISFY\nthe satellite is pointing to the calibration target ::: Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon11. ===> NOT SATISFY\nthe instrument is switched(turned) on ::: Instrument1: on satellite1, switched on ===> SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 8, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "09579b98-12b9-4d15-80ed-8c63b114973e", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 1 the following actions are planned to be performed: satellite0 turns to groundstation3 from planet14. Is the action: satellite0 turns from planet14 to groundstation3 executable at step 1, True or False?", "initial_state": "We can find following objects in the initial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. for star9, instrument0 is calibrated, infrared1 is compatible with instrument0, instrument0 is calibrated for star1, instrument0 supports image3, satellite0 has instrument0 on board. ::: Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. for groundstation0, instrument1 is calibrated, image0 is compatible with instrument1, image3 is compatible with instrument1, infrared1 is supported by instrument1, satellite0 carries instrument1 on board. ::: Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Calibration of instrument2 for groundstation5 is complete, calibration of instrument2 for groundstation7 is complete, calibration of instrument2 for star9 is complete, image2 is compatible with instrument2, image3 is compatible with instrument2, instrument2 is on board satellite0. ::: Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. satellite1 carries instrument3 on board, calibration of instrument3 for star6 is complete, instrument3 is calibrated for star8, instrument3 is calibrated for groundstation5, image0 is supported by instrument3, image2 is compatible with instrument3, image3 is supported by instrument3. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. satellite0 carries instrument1 on board, instrument2 is on board satellite0, satellite0 has instrument0 on board, satellite0 has power available, groundstation3 is where satellite0 is pointed. ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. satellite1 carries instrument3 on board, satellite1 has power available, and satellite1 is pointing to phenomenon10. ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star9 is its calibration target, star1 is its calibration target, compatible with infrared1, compatible with image3. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with image3, compatible with infrared1. Instrument2: on satellite0, is calibrated, groundstation5 is its calibration target, groundstation7 is its calibration target, star9 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["satellite0 turns to groundstation3 from planet14"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal Answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "7101bf1c-2abd-4671-ae2f-ef751780b56f", "domain": "satellite", "question": "Given the initial condition, for steps 1 through 10 the following actions are planned to be performed: instrument1 on satellite1 is switched on, from star3, satellite1 turns to star1, instrument1 that is on satellite1 is calibrated to star1, from star1, satellite1 turns to phenomenon10, satellite1's instrument1 takes an image of phenomenon10 in image5, instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3, from phenomenon10, satellite1 turns to phenomenon11, satellite1's instrument1 takes an image of phenomenon11 in spectrograph1, satellite1 turns to phenomenon5 from phenomenon11 and satellite1's instrument1 takes an image of phenomenon5 in image4. Is the action: instrument1 which is on satellite1 takes an image of phenomenon10 in image5 executable at step 5, True or False?", "initial_state": "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, has power supply, pointing to star3.", "action_sequence": ["instrument1 on satellite1 is switched on", "from star3 satellite1 turns to star1", "instrument1 that is on satellite1 is calibrated to star1", "from star1 satellite1 turns to phenomenon10", "satellite1's instrument1 takes an image of phenomenon10 in image5", "instrument1 which is on satellite1 takes an image of phenomenon10 in spectrograph3", "from phenomenon10 satellite1 turns to phenomenon11", "satellite1's instrument1 takes an image of phenomenon11 in spectrograph1", "satellite1 turns to phenomenon5 from phenomenon11", "satellite1's instrument1 takes an image of phenomenon5 in image4"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star3.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star1.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star1.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon11.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon11.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon5.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon5."], "action_taken": 10, "state_checker_response": "In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nInstrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon5.\nBased on the domain description, an instrument on a satellite can 'take' an image of the target in the specific format. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing the target, the instrument is on the satellite, the instrument is turned on, the instrument is calibrated, the instrument supports the specific format.\nthe satellite is currently pointing the target ::: Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon5. ===> SATISFY\nthe instrument is on the satellite ::: Instrument1: on satellite1 ===> SATISFY\nthe instrument is turned on ::: Instrument1: switched on ===> SATISFY\nthe instrument is calibrated ::: Instrument1: is calibrated ===> SATISFY\nthe instrument supports the specific format ::: Instrument1: compatible with image4 ===> SATISFY\nSince all preconditions are satisfied, this action is executable.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "01ef05f0-56ed-4df6-bb6c-9cca43d2f657", "domain": "satellite", "question": "Is it possible to execute it, True or False? Given the initial condition, the following actions are planned to be performed: from groundstation2, satellite0 turns to phenomenon15, from star1, satellite0 turns to groundstation2, image of phenomenon15 is taken with instrument0 on satellite0 in spectrograph0, image of planet14 is taken with instrument3 on satellite1 in spectrograph1, image of star10 is taken with instrument3 on satellite1 in spectrograph1, instrument0 is calibrated on satellite0 to groundstation2, instrument0 which is on satellite0 takes an image of star10 in thermograph4, instrument0 which is on satellite0 takes an image of star11 in thermograph4, instrument0 which is on satellite0 takes an image of star13 in spectrograph0, instrument3 on satellite1 is switched on, instrument3 that is on satellite1 is calibrated to star6, on satellite0, instrument0 is switched on, satellite0 turns from phenomenon15 to star11, satellite0 turns from star11 to star13, satellite1 turns from groundstation4 to star6, satellite1 turns from star12 to star0, satellite1 turns from star6 to planet14, satellite1 turns to star10 from planet14 and satellite1's instrument3 takes an image of star12 in spectrograph1.", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation2 is its calibration target, star0 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, groundstation2 is its calibration target, star8 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with spectrograph1. Instrument2: on satellite1, is calibrated, groundstation4 is its calibration target, groundstation9 is its calibration target, star7 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.", "action_sequence": ["From groundstation2 satellite0 turns to phenomenon15", "From star1 satellite0 turns to groundstation2", "Image of phenomenon15 is taken with instrument0 on satellite0 in spectrograph0", "Image of planet14 is taken with instrument3 on satellite1 in spectrograph1", "Image of star10 is taken with instrument3 on satellite1 in spectrograph1", "Instrument0 is calibrated on satellite0 to groundstation2", "Instrument0 which is on satellite0 takes an image of star10 in thermograph4", "Instrument0 which is on satellite0 takes an image of star11 in thermograph4", "Instrument0 which is on satellite0 takes an image of star13 in spectrograph0", "Instrument3 on satellite1 is switched on", "Instrument3 that is on satellite1 is calibrated to star6", "On satellite0 instrument0 is switched on", "Satellite0 turns from phenomenon15 to star11", "Satellite0 turns from star11 to star13", "Satellite1 turns from groundstation4 to star6", "Satellite1 turns from star12 to star0", "Satellite1 turns from star6 to planet14", "Satellite1 turns to star10 from planet14", "Satellite1's instrument3 takes an image of star12 in spectrograph1"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite0: carries instrument0, carries instrument1, has power supply, pointing to star1.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
{"question_id": "8f4c2e65-054d-49d7-8a44-966dd29fc4e4", "domain": "satellite", "question": "Is it possible to execute it, True or False? Given the initial condition, the following actions are planned to be performed: from phenomenon7, satellite1 turns to phenomenon9, from phenomenon9, satellite1 turns to planet8, image of phenomenon10 is taken with instrument1 on satellite1 in image5, image of phenomenon10 is taken with instrument1 on satellite1 in spectrograph3, image of planet8 is taken with instrument1 on satellite1 in image5, instrument1 that is on satellite1 is calibrated to star1, instrument1 which is on satellite1 takes an image of phenomenon7 in image0, instrument1 which is on satellite1 takes an image of phenomenon7 in image4, on satellite1, instrument1 is switched on, satellite1 turns from phenomenon10 to phenomenon11, satellite1 turns from phenomenon11 to phenomenon5, satellite1 turns from star1 to phenomenon10, satellite1 turns from star3 to star1, satellite1 turns to phenomenon7 from phenomenon5, satellite1's instrument1 takes an image of phenomenon11 in spectrograph1, satellite1's instrument1 takes an image of phenomenon5 in image4, satellite1's instrument1 takes an image of phenomenon5 in image5, satellite1's instrument1 takes an image of phenomenon9 in image5 and satellite1's instrument1 takes an image of phenomenon9 in spectrograph1.", "initial_state": "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, has power supply, pointing to star3.", "action_sequence": ["From phenomenon7 satellite1 turns to phenomenon9", "From phenomenon9 satellite1 turns to planet8", "Image of phenomenon10 is taken with instrument1 on satellite1 in image5", "Image of phenomenon10 is taken with instrument1 on satellite1 in spectrograph3", "Image of planet8 is taken with instrument1 on satellite1 in image5", "Instrument1 that is on satellite1 is calibrated to star1", "Instrument1 which is on satellite1 takes an image of phenomenon7 in image0", "Instrument1 which is on satellite1 takes an image of phenomenon7 in image4", "On satellite1 instrument1 is switched on", "Satellite1 turns from phenomenon10 to phenomenon11", "Satellite1 turns from phenomenon11 to phenomenon5", "Satellite1 turns from star1 to phenomenon10", "Satellite1 turns from star3 to star1", "Satellite1 turns to phenomenon7 from phenomenon5", "Satellite1's instrument1 takes an image of phenomenon11 in spectrograph1", "Satellite1's instrument1 takes an image of phenomenon5 in image4", "Satellite1's instrument1 takes an image of phenomenon5 in image5", "Satellite1's instrument1 takes an image of phenomenon9 in image5", "Satellite1's instrument1 takes an image of phenomenon9 in spectrograph1"], "state_progression": ["In order to check whether this action is executable or not, we first need to find states of objects related to this action, then check whether all preconditions of this action are satisfied.\nSatellite1: carries instrument1, has power supply, pointing to star3.\nBased on the domain description, a satellite can 'turn' from target A to target B. This action is executable only if all following preconditions are satisfied: the satellite is currently pointing target A.\nthe satellite is currently pointing target A ::: Satellite1: carries instrument1, has power supply, pointing to star3. ===> NOT SATISFY\nSince not all preconditions are satisfied, the action is not executable.\nFinal answer: False."], "action_taken": 0, "state_checker_response": "N/A for AE task", "answer": 0, "label": "False"}
