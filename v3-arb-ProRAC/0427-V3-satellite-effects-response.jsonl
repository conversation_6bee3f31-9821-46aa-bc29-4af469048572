{"question_id": "36a6a9b8-a25d-4776-b526-1c7fc84ec0be", "domain": "satellite", "question": "In this state, if satellite0's instrument1 takes an image of star10 in image6, is it True or False that image of star10 exists in image6?", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "action_sequence": ["On satellite0 instrument1 is switched on", "From groundstation2 satellite0 turns to groundstation0", "Instrument1 that is on satellite0 is calibrated to groundstation0", "Satellite0 turns from groundstation0 to planet11", "Satellite0's instrument1 takes an image of planet11 in image5", "Satellite0's instrument1 takes an image of planet11 in image6", "Satellite0 turns to planet13 from planet11", "Instrument1 which is on satellite0 takes an image of planet13 in image5", "Image of planet13 is taken with instrument1 on satellite0 in spectrograph2", "Satellite0 turns to star10 from planet13"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet11, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet11, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet11, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet13, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet13, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet13, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star10, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply."], "action_taken": 10, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \" satellite0's instrument1 takes an image of star10 in image6\". Based on the domain description, this action is executable(instrument 1 is turned on, is calibrated, is on satellite0, satellite0 is pointing star10, instrument1 supports image6)\nAfter taking the action, the curremt states of all objects should be: Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star10, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.\nThen, we compare each proposition in the question one by one.\nimage of star10 exists in image6 ::: Instrument1: has star10's image in image6. ===> MATCH\nSince all propositions in the question match with the curent state, so the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "ef094b86-f2bf-458c-af72-3862abcc0e17", "domain": "satellite", "question": "In this state, if satellite0's instrument0 takes an image of phenomenon10 in infrared1, is it True or False that image of phenomenon10 exists in infrared1?", "initial_state": "Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["On satellite1 instrument3 is switched on", "On satellite0 instrument0 is switched on", "From phenomenon10 satellite1 turns to groundstation5", "Calibration of instrument3 which is on satellite1 to groundstation5 is complete", "Satellite1 turns from groundstation5 to phenomenon16", "Instrument3 which is on satellite1 takes an image of phenomenon16 in image3", "From phenomenon16 satellite1 turns to phenomenon17", "Instrument3 which is on satellite1 takes an image of phenomenon17 in image3", "From phenomenon17 satellite1 turns to planet11", "Instrument3 which is on satellite1 takes an image of planet11 in image3", "From planet11 satellite1 turns to planet13", "Instrument3 which is on satellite1 takes an image of planet13 in image0", "From planet13 satellite1 turns to planet14", "Image of planet14 is taken with instrument3 on satellite1 in image0", "Satellite1 turns to star15 from planet14", "Instrument3 which is on satellite1 takes an image of star15 in image2", "Satellite0 turns from groundstation3 to star1", "Calibration of instrument0 which is on satellite0 to star1 is complete", "From star1 satellite0 turns to phenomenon10 to reach the current state"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to groundstation5, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to groundstation5, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon16, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon16, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon17, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon17, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet11, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet11, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet13, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet13, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet14, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet14, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to star1, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply.", "Instrument0: on satellite0, switched on, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to star1, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply.", "Instrument0: on satellite0, switched on, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to phenomenon10, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply."], "action_taken": 19, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"satellite0's instrument0 takes an image of phenomenon10 in infrared1\". Based on the domain description, this action is executable(instrument0 is turned on, is calibrated, is on satellite0, satellite0 is pointing phenomenon10, instrument0 supports infrared1)\nAfter taking the action, the current states of all objects should be: Instrument0: on satellite0, switched on, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1, has phenomenon10's image in infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to phenomenon10, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply.\nThen, we compare each proposition in the question one by one.\nimage of phenomenon10 exists in infrared1 ::: Instrument0: on satellite0, switched on, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1, has phenomenon10's image in infrared1. ===> MATCH\nSince all propositions in the question match with the current state, so the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "00d7decb-e1d4-4c47-bc9b-45f13a6654ea", "domain": "satellite", "question": "In this state, if instrument3 that is on satellite0 is turned on, is it True or False that satellite0 has power?", "initial_state": "After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation2, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "action_sequence": ["instrument1 on satellite0 is switched on", "satellite0 turns from groundstation2 to groundstation0", "instrument1 that is on satellite0 is calibrated to groundstation0", "satellite0 turns to planet11 from groundstation0", "satellite0's instrument1 takes an image of planet11 in image5", "instrument1 which is on satellite0 takes an image of planet11 in image6", "satellite0 turns from planet11 to planet13", "image of planet13 is taken with instrument1 on satellite0 in image5", "satellite0's instrument1 takes an image of planet13 in spectrograph2", "satellite0 turns from planet13 to star10", "instrument1 which is on satellite0 takes an image of star10 in image6", "satellite0's instrument1 takes an image of star10 in spectrograph2", "on satellite0 instrument1 is switched off", "instrument2 on satellite0 is switched on", "satellite0 turns from star10 to star4", "instrument2 is calibrated on satellite0 to star4", "from star4 satellite0 turns to star16", "instrument2 which is on satellite0 takes an image of star16 in image0", "on satellite0 instrument2 is switched off"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation2, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star10, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, not calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, not calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star4, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star4, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star16, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1, has star16's image in image0. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star16, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched off, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1, has star16's image in image0. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star16, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply."], "action_taken": 19, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"instrument3 that is on satellite0 is turned on\". Based on the domain description, this action is executable(instrument3 is on satellite0, satellite0 has power supply)\nAfter taking the action, the curremt states of all objects should be: Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched off, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1, has star16's image in image0. Instrument3: on satellite0, turned on, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star16, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.\nThen, we compare each proposition in the question one by one.\nsatellite0 has power ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star16, doesn't have power supply. ===> NOT MATCH\nSince there is a proposition in the question doesn't match with the current state, the question is false.\nFinal Answer: False.", "answer": 0, "label": "False"}
{"question_id": "048a68dc-f48b-4784-8e4c-781fa02ca16b", "domain": "satellite", "question": "In this state, if from star12, satellite1 turns to star0, is it True or False that satellite0 is not aimed towards groundstation3 and star1 is not where satellite1 is pointed?", "initial_state": "Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to groundstation4, has power supply.", "action_sequence": ["instrument3 that is on satellite1 is turned on", "on satellite0 instrument0 is switched on", "satellite1 turns from groundstation4 to star6", "calibration of instrument3 which is on satellite1 to star6 is complete", "from star6 satellite1 turns to planet14", "image of planet14 is taken with instrument3 on satellite1 in spectrograph1", "satellite1 turns to star10 from planet14", "satellite1's instrument3 takes an image of star10 in spectrograph1", "satellite1 turns from star10 to star12", "satellite1's instrument3 takes an image of star12 in spectrograph1"], "state_progression": ["Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to groundstation4, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to groundstation4, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to star6, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to star6, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to planet14, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to planet14, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to star10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to star10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to star12, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to star12, doesn't have power supply."], "action_taken": 10, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"from star12, satellite1 turns to star0\". Based on the domain description, this action is executable(satellite1 is currently pointing to star12)\nAfter taking the action, the current states of all objects should be: Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to star0, doesn't have power supply.\nThen, we compare each proposition in the question one by one.\nsatellite0 is not aimed towards groundstation3, ::: Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. ===> MATCH\nstar1 is not where satellite1 is pointed, ::: Satellite1: carries instrument2, carries instrument3, pointing to star0, doesn't have power supply. ===> MATCH\nSince all propositions in the question match with the current state, the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "e6c95c86-250e-4f89-9b83-b135e3c79c3b", "domain": "satellite", "question": "In this state, if instrument1 which is on satellite1 takes an image of phenomenon5 in image5, is it True or False that there is an image of phenomenon5 in image5?", "initial_state": "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, has power supply, pointing to star3.", "action_sequence": ["On satellite1 instrument1 is switched on", "Satellite1 turns from star3 to star1", "Calibration of instrument1 which is on satellite1 to star1 is complete", "Satellite1 turns from star1 to phenomenon10", "Satellite1's instrument1 takes an image of phenomenon10 in image5", "Satellite1's instrument1 takes an image of phenomenon10 in spectrograph3", "Satellite1 turns from phenomenon10 to phenomenon11", "Instrument1 which is on satellite1 takes an image of phenomenon11 in spectrograph1", "Satellite1 turns from phenomenon11 to phenomenon5", "Image of phenomenon5 is taken with instrument1 on satellite1 in image4"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star3.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star1.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star1.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon11.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon11.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon5.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon5."], "action_taken": 10, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"instrument1 which is on satellite1 takes an image of phenomenon5 in image5\". Based on the domain description, this action is executable(instrument 1 is turned on, is calibrated, is on satellite1, satellite1 is pointing phenomenon5, instrument1 supports image5)\nAfter taking the action, the curremt states of all objects should be: Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, turned on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4, has phenomenon5's image in image5. Satellite0: carries instrument0, has power supply, pointing phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing phenomenon5.\nThen, we compare each proposition in the question one by one.\nthere is an image of phenomenon5 in image5 ::: Instrument1: has phenomenon5's image in image5. ===> MATCH\nSince all propositions in the question match with the current state, so the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "7e66c8c4-300f-44b0-803a-d70ab1048528", "domain": "satellite", "question": "In this state, if on satellite0, instrument3 is switched on, is it True or False that instrument3 is switched on?", "initial_state": "We can find following objects in the intial state description: instrument0, instrument1, instrument2, instrument3, instrument4, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. for groundstation7, instrument0 is calibrated, for groundstation8, instrument4 is calibrated, for groundstation9, instrument3 is calibrated, image6 is supported by instrument0, instrument0 is calibrated for star3, satellite0 carries instrument0 on board. ::: Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Calibration of instrument1 for groundstation6 is complete, image5 is compatible with instrument1, image6 is supported by instrument1, instrument1 is calibrated for groundstation0, instrument1 is on board satellite0, spectrograph2 is supported by instrument1. ::: Instrument1: on satellite0, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Calibration of instrument2 for star4 is complete, image0 is supported by instrument2, image1 is supported by instrument2, satellite0 carries instrument2 on board. ::: Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. for groundstation9, instrument3 is calibrated, image1 is supported by instrument3, spectrograph4 is compatible with instrument3 and thermograph3 is supported by instrument3, satellite0 carries instrument3 on board. ::: Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. for groundstation8, instrument4 is calibrated, image1 is compatible with instrument4, instrument4 supports infrared7, satellite1 carries instrument4 on board. ::: Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. satellite0 carries instrument0 on board, satellite0 carries instrument2 on board, satellite0 carries instrument3 on board, satellite0 has power available, satellite0 is pointing to groundstation2, instrument1 is on board satellite0. ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply. satellite1 carries instrument4 on board, satellite1 has power, satellite1 is aimed towards planet13. ::: Satellite1: carries instrument4, pointing to planet13, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "action_sequence": ["On satellite0 instrument1 is switched on", "From groundstation2 satellite0 turns to groundstation0", "Instrument1 is calibrated on satellite0 to groundstation0", "Satellite0 turns from groundstation0 to planet11", "Instrument1 which is on satellite0 takes an image of planet11 in image5", "Image of planet11 is taken with instrument1 on satellite0 in image6", "Satellite0 turns from planet11 to planet13", "Image of planet13 is taken with instrument1 on satellite0 in image5", "Image of planet13 is taken with instrument1 on satellite0 in spectrograph2", "Satellite0 turns to star10 from planet13", "Image of star10 is taken with instrument1 on satellite0 in image6", "Instrument1 which is on satellite0 takes an image of star10 in spectrograph2", "On satellite0 instrument1 is switched off", "On satellite0 instrument2 is switched on", "Satellite0 turns to star4 from star10", "Calibration of instrument2 which is on satellite0 to star4 is complete", "Satellite0 turns from star4 to star16", "Instrument2 which is on satellite0 takes an image of star16 in image0", "Instrument2 on satellite0 is switched off"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star10, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, not calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, not calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star4, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star4, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star16, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1, has star16's image in image0. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star16, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched off, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1, has star16's image in image0. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star16, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply."], "action_taken": 19, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"on satellite0, instrument3 is switched on\". Based on the domain description, this action is executable(instrument3 is on satellite0, satellite0 has power supply)\nAfter taking the action, the current states of all objects should be: Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched off, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1, has star16's image in image0. Instrument3: on satellite0, turned on, is not calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star16, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.\nThen, we compare each proposition in the question one by one.\ninstrument3 is switched on, ::: Instrument3: on satellite0, turned on, is not calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. ===> MATCH\nSince all propositions in the question match with the current state, so the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "61574b88-2759-4e3b-b2cc-4126ef098ccf", "domain": "satellite", "question": "In this state, if image of phenomenon5 is taken with instrument1 on satellite1 in image5, is it True or False that there is no image of direction star6 in image0?", "initial_state": "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, has power supply, pointing to star3.", "action_sequence": ["instrument1 that is on satellite1 is turned on", "satellite1 turns to star1 from star3", "instrument1 that is on satellite1 is calibrated to star1", "from star1 satellite1 turns to phenomenon10", "satellite1's instrument1 takes an image of phenomenon10 in image5", "satellite1's instrument1 takes an image of phenomenon10 in spectrograph3", "satellite1 turns to phenomenon11 from phenomenon10", "satellite1's instrument1 takes an image of phenomenon11 in spectrograph1", "from phenomenon11 satellite1 turns to phenomenon5", "instrument1 which is on satellite1 takes an image of phenomenon5 in image4"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star3.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star1.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star1.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon10.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon11.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon11.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon5.", "Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to phenomenon5."], "action_taken": 10, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action.\nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"image of phenomenon5 is taken with instrument1 on satellite1 in image5\". Based on the domain description, this action is executable(instrument 1 is turned on, is calibrated, is on satellite1, satellite1 is pointing phenomenon5, instrument1 supports image5)\nAfter taking the action, the current states of all objects should be: Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, turned on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4, has phenomenon5's image in image5. Satellite0: carries instrument0, has power supply, pointing phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing phenomenon5.\nThen, we compare the proposition in the question one by one.\nthere is no image of direction star6 in image0 ::: Instrument1: on satellite1, turned on, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3, has phenomenon10's image in image5, has phenomenon10's image in spectrograph3, has phenomenon11's image in spectrograph1, has phenomenon5's image in image4, has phenomenon5's image in image5. ===> MATCH (since there is no mention of star6's image in image0)\nSince the proposition in the question matches with the current state, the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "1b0fe1d5-e54a-43ae-826a-e5c4da69d8a2", "domain": "satellite", "question": "In this state, if instrument0 that is on satellite0 is turned on, is it True or False that instrument0 is turned on?", "initial_state": "Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.", "action_sequence": ["instrument3 that is on satellite1 is turned on to reach the current state"], "state_progression": ["Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to groundstation4."], "action_taken": 1, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"instrument0 that is on satellite0 is turned on\". Based on the domain description, this action is executable(instrument0 is on satellite0, satellite0 has power supply)\nAfter taking the action, the current states of all objects should be: Instrument0: on satellite0, turned on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to groundstation4.\nThen, we compare each proposition in the question one by one.\ninstrument0 is turned on, ::: Instrument0: on satellite0, turned on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. ===> MATCH\nSince all propositions in the question match with the current state, so the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "e4f0a27f-ffff-440f-8a9e-586bd3c2fac0", "domain": "satellite", "question": "In this state, if satellite0's instrument0 takes an image of phenomenon10 in infrared1, is it True or False that there is an image of groundstation4 in image3?", "initial_state": "Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["instrument3 that is on satellite1 is turned on", "instrument0 on satellite0 is switched on", "satellite1 turns to groundstation5 from phenomenon10", "instrument3 that is on satellite1 is calibrated to groundstation5", "satellite1 turns from groundstation5 to phenomenon16", "satellite1's instrument3 takes an image of phenomenon16 in image3", "satellite1 turns from phenomenon16 to phenomenon17", "satellite1's instrument3 takes an image of phenomenon17 in image3", "satellite1 turns from phenomenon17 to planet11", "image of planet11 is taken with instrument3 on satellite1 in image3", "satellite1 turns from planet11 to planet13", "instrument3 which is on satellite1 takes an image of planet13 in image0", "satellite1 turns from planet13 to planet14", "satellite1's instrument3 takes an image of planet14 in image0", "satellite1 turns to star15 from planet14", "image of star15 is taken with instrument3 on satellite1 in image2", "from groundstation3 satellite0 turns to star1", "instrument0 is calibrated on satellite0 to star1", "satellite0 turns to phenomenon10 from star1"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to groundstation5, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to groundstation5, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon16, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon16, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon17, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon17, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet11, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet11, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet13, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet13, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet14, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet14, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to star1, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply.", "Instrument0: on satellite0, switched on, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to star1, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply.", "Instrument0: on satellite0, switched on, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to phenomenon10, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply."], "action_taken": 19, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"satellite0's instrument0 takes an image of phenomenon10 in infrared1\". Based on the domain description, this action is executable(instrument0 is turned on, is calibrated, is on satellite0, satellite0 is pointing phenomenon10, instrument0 supports infrared1)\nAfter taking the action, the curremt states of all objects should be: Instrument0: on satellite0, switched on, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1, has phenomenon10's image in infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing to phenomenon10, doesn't have power supply. Satellite1: carries instrument3, pointing to star15, doesn't have power supply.\nThen, we compare each proposition in the question one by one.\nthere is an image of groundstation4 in image3 ::: Instrument0: on satellite0, switched on, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1, has phenomenon10's image in infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3, has planet13's image in image0, has planet14's image in image0, has star15's image in image2. ===> NOT MATCH\nSince there is a proposition in the question doesn't match with the current state, the question is false.\nFinal Answer: False.", "answer": 0, "label": "False"}
{"question_id": "62d7951e-4e94-4c3d-b478-ad8702bae17c", "domain": "satellite", "question": "In this state, if from groundstation2, satellite0 turns to groundstation0, is it True or False that satellite1 is aimed towards phenomenon14 and star10 is where satellite0 is pointed?", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "action_sequence": ["instrument1 that is on satellite0 is turned on to reach the current state"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply."], "action_taken": 1, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"from groundstation2, satellite0 turns to groundstation0\". Based on the domain description, this action is executable(satellite0 is currently pointing groundstation2)\nAfter taking the action, the curremt states of all objects should be: Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.\nThen, we compare each proposition in the question one by one.\nsatellite1 is aimed towards phenomenon14 ::: Satellite1: carries instrument4, pointing planet13, has power supply. ===> NOT MATCH\nstar10 is where satellite0 is pointed ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation0, doesn't have power supply. ===> NOT MATCH\nSince there are propositions in the question that don't match with the current state, the question is false.\nFinal Answer: False.", "answer": 0, "label": "False"}
{"question_id": "8ea7b4ae-e75d-4493-82fe-bee4018b5133", "domain": "satellite", "question": "In this state, if instrument3 that is on satellite0 is turned on, is it True or False that there is an image of groundstation7 in image1?", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, has power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "action_sequence": ["instrument1 on satellite0 is switched on from groundstation2", "satellite0 turns to groundstation0 from groundstation2", "instrument1 is calibrated on satellite0 to groundstation0 from groundstation0", "satellite0 turns to planet11 from groundstation0", "image of planet11 is taken with instrument1 on satellite0 in image5", "satellite0's instrument1 takes an image of planet11 in image6", "satellite0 turns to planet13 from planet11", "image of planet13 is taken with instrument1 on satellite0 in image5", "image of planet13 is taken with instrument1 on satellite0 in spectrograph2", "satellite0 turns from planet13 to star10", "satellite0's instrument1 takes an image of star10 in image6", "satellite0's instrument1 takes an image of star10 in spectrograph2", "instrument1 that is on satellite0 is turned off", "instrument2 on satellite0 is switched on", "satellite0 turns from star10 to star4", "instrument2 that is on satellite0 is calibrated to star4", "satellite0 turns from star4 to star16", "image of star16 is taken with instrument2 on satellite0 in image0", "instrument2 on satellite0 is switched off"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation2, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet11, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet11, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet11, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet13, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet13, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing planet13, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star10, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star10, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star10, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star10, has power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, not calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star10, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, not calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star4, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star4, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star16, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1, has star16's image in image0. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star16, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched off, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1, has star16's image in image0. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star16, has power supply. Satellite1: carries instrument4, pointing planet13, has power supply."], "action_taken": 19, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"instrument3 that is on satellite0 is turned on\". Based on the domain description, this action is executable(instrument3 is on satellite0, satellite0 has power supply)\nAfter taking the action, the curremt states of all objects should be: Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched off, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1, has star16's image in image0. Instrument3: on satellite0, turned on, is not calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing star16, doesn't have power supply. Satellite1: carries instrument4, pointing planet13, has power supply.\nThen, we compare each proposition in the question one by one.\nthere is an image of groundstation7 in image1 ::: Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched off, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1, has star16's image in image0. Instrument3: on satellite0, turned on, is not calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. ===> NOT MATCH\nSince there is a proposition in the question doesn't match with the current state, the question is false.\nFinal Answer: False.", "answer": 0, "label": "False"}
{"question_id": "768168a1-d3b0-4022-986f-a51b058b6a20", "domain": "satellite", "question": "In this state, if instrument0 that is on satellite0 is turned on, is it True or False that image of planet13 does not exist in image0?", "initial_state": "We can find following objects in the initial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. for star1, instrument0 is calibrated, for star9, instrument0 is calibrated, instrument0 is on board satellite0, instrument0 supports image3, instrument0 supports infrared1. ::: Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Calibration of instrument1 for groundstation0 is complete, image0 is compatible with instrument1, infrared1 is compatible with instrument1, instrument1 supports image3, satellite0 carries instrument1 on board. ::: Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. calibration of instrument2 for star9 is complete, image2 is compatible with instrument2, image3 is supported by instrument2, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, satellite0 carries instrument2 on board. ::: Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. calibration of instrument3 for star6 is complete, for star8, instrument3 is calibrated, image2 is supported by instrument3, image3 is compatible with instrument3, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite1 has instrument3 on board. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, instrument0 is on board satellite0, satellite0 has power available, groundstation3 is where satellite0 is pointed. ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. satellite1 has instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10. ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["instrument3 on satellite1 is switched on to reach the current state"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply."], "action_taken": 1, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"instrument0 that is on satellite0 is turned on\". Based on the domain description, this action is executable(instrument0 is on satellite0, satellite0 has power supply)\nAfter taking the action, the curremt states of all objects should be: Instrument0: on satellite0, turned on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.\nThen, we compare each proposition in the question one by one.\nimage of planet13 does not exist in image0 ::: Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. ===> MATCH (no instrument has an image of planet13 in image0)\nSince all propositions in the question match with the curent state, so the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "a66541e8-8757-4847-88d9-e286be5ead6f", "domain": "satellite", "question": "In this state, if satellite1 turns from star12 to star0, is it True or False that satellite1 is not pointing to star12 and star0 is where satellite1 is pointed?", "initial_state": "Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.", "action_sequence": ["On satellite1 instrument3 is switched on", "Instrument0 on satellite0 is switched on", "Satellite1 turns to star6 from groundstation4", "Instrument3 is calibrated on satellite1 to star6", "Satellite1 turns to planet14 from star6", "Image of planet14 is taken with instrument3 on satellite1 in spectrograph1", "From planet14 satellite1 turns to star10", "Image of star10 is taken with instrument3 on satellite1 in spectrograph1", "Satellite1 turns from star10 to star12", "Satellite1's instrument3 takes an image of star12 in spectrograph1"], "state_progression": ["Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to groundstation4.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to groundstation4.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star6.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star6.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to planet14.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to planet14.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star10.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star10.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star12.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star12."], "action_taken": 10, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"satellite1 turns from star12 to star0\". Based on the domain description, this action is executable(satellite1 is currently pointing to star12)\nAfter taking the action, the current states of all objects should be: Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.\nThen, we compare each proposition in the question one by one.\nsatellite1 is not pointing to star12, ::: Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0. ===> MATCH\nstar0 is where satellite1 is pointed, ::: Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0. ===> MATCH\nSince all propositions in the question match with the current state, so the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "0f85e7ab-c621-4522-b5b4-5ca537b43694", "domain": "satellite", "question": "In this state, if satellite1 turns from star3 to star1, is it True or False that satellite1 is aimed towards star1 and star3 is not where satellite1 is pointed?", "initial_state": "We can find following objects in the intial state description: instrument0, instrument1, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. Calibration of instrument0 for star1 is complete, image2 is compatible with instrument0, satellite0 carries instrument0 on board. ::: Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. satellite1 has instrument1 on board, image0 is compatible with instrument1, image2 is compatible with instrument1, image5 is supported by instrument1, instrument1 is calibrated for star1, instrument1 supports image4, instrument1 supports spectrograph3, spectrograph1 is supported by instrument1. ::: Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. satellite0 carries instrument0 on board, satellite0 has power available, satellite0 is pointing to phenomenon5. ::: Satellite0: carries instrument0, has power supply, pointing to phenomenon5. satellite1 has instrument1 on board, satellite1 has power available, satellite1 is pointing to star3. ::: Satellite1: carries instrument1, has power supply, pointing to star3. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, is calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, has power supply, pointing to star3.", "action_sequence": ["On satellite1 instrument1 is switched on to reach the current state"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star3."], "action_taken": 1, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"satellite1 turns from star3 to star1\". Based on the domain description, this action is executable(satellite1 is currently pointing star3)\nAfter taking the action, the curremt states of all objects should be: Instrument0: on satellite0, is calibrated, star1 is its calibration target, compatible with image2. Instrument1: on satellite1, switched on, not calibrated, star1 is its calibration target, compatible with image0, compatible with image2, compatible with image4, compatible with image5, compatible with spectrograph1, compatible with spectrograph3. Satellite0: carries instrument0, has power supply, pointing to phenomenon5. Satellite1: carries instrument1, doesn't have power supply, pointing to star1.\nThen, we compare each proposition in the question one by one.\nsatellite1 is aimed towards star1 ::: Satellite1: carries instrument1, doesn't have power supply, pointing to star1. ===> MATCH\nstar3 is not where satellite1 is pointed ::: Satellite1: carries instrument1, doesn't have power supply, pointing to star1. ===> MATCH\nSince all propositions in the question match with the current state, so the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "863996d9-d2a7-4d34-8792-265800aa6950", "domain": "satellite", "question": "In this state, if satellite1 turns from planet11 to planet13, is it True or False that satellite0 is not aimed towards groundstation2?", "initial_state": "We can find following objects in the intial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. for star1, instrument0 is calibrated, for star9, instrument0 is calibrated, instrument0 is on board satellite0, instrument0 supports image3, instrument0 supports infrared1. ::: Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Calibration of instrument1 for groundstation0 is complete, image0 is compatible with instrument1, infrared1 is compatible with instrument1, instrument1 supports image3, satellite0 carries instrument1 on board. ::: Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. calibration of instrument2 for star9 is complete, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, image2 is compatible with instrument2, image3 is supported by instrument2, satellite0 carries instrument2 on board. ::: Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. calibration of instrument3 for star6 is complete, for star8, instrument3 is calibrated, instrument3 is calibrated for groundstation5, image2 is supported by instrument3, image3 is compatible with instrument3, instrument3 supports image0, satellite1 has instrument3 on board. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, instrument0 is on board satellite0, satellite0 has power available, groundstation3 is where satellite0 is pointed. ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. satellite1 has instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10. ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["instrument3 on satellite1 is switched on", "instrument0 that is on satellite0 is turned on", "satellite1 turns from phenomenon10 to groundstation5", "instrument3 is calibrated on satellite1 to groundstation5", "satellite1 turns to phenomenon16 from groundstation5", "image of phenomenon16 is taken with instrument3 on satellite1 in image3", "from phenomenon16 satellite1 turns to phenomenon17", "instrument3 which is on satellite1 takes an image of phenomenon17 in image3", "satellite1 turns to planet11 from phenomenon17", "satellite1's instrument3 takes an image of planet11 in image3"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to groundstation5, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to groundstation5, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon16, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon16, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon17, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon17, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet11, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet11, doesn't have power supply."], "action_taken": 10, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"satellite1 turns from planet11 to planet13\". Based on the domain description, this action is executable(satellite1 is currently pointing to planet11)\nAfter taking the action, the current states of all objects should be: Instrument0: on satellite0, switched on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3, has phenomenon16's image in image3, has phenomenon17's image in image3, has planet11's image in image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to planet13, doesn't have power supply.\nThen, we compare each proposition in the question one by one.\nsatellite0 is not aimed towards groundstation2 ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. ===> MATCH\nSince all propositions in the question match with the current state, so the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "3fff78fc-bc2b-4a21-994e-ac1729f9be3e", "domain": "satellite", "question": "In this state, if instrument1 which is on satellite0 takes an image of star10 in image6, is it True or False that image of star3 does not exist in spectrograph2?", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation2, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "action_sequence": ["On satellite0 instrument1 is switched on", "Satellite0 turns to groundstation0 from groundstation2", "Calibration of instrument1 which is on satellite0 to groundstation0 is complete", "Satellite0 turns to planet11 from groundstation0", "Satellite0's instrument1 takes an image of planet11 in image5", "Satellite0's instrument1 takes an image of planet11 in image6", "Satellite0 turns from planet11 to planet13", "Image of planet13 is taken with instrument1 on satellite0 in image5", "Satellite0's instrument1 takes an image of planet13 in spectrograph2", "From planet13 satellite0 turns to star10 to reach the current state"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation2, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply."], "action_taken": 10, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"instrument1 which is on satellite0 takes an image of star10 in image6\". Based on the domain description, this action is executable(instrument 1 is turned on, is calibrated, is on satellite0, satellite0 is pointing star10, instrument1 supports image6)\nAfter taking the action, the curremt states of all objects should be: Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.\nThen, we compare each proposition in the question one by one.\nimage of star3 does not exist in spectrograph2 ::: Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6. ===> MATCH\nSince all propositions in the question match with the curent state, so the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "a5023e58-3a3b-4c9c-bb47-b5ee5a724cbb", "domain": "satellite", "question": "Given the initial condition, the following actions are performed: instrument3 that is on satellite1 is turned on to reach the current state. In this state, if instrument0 that is on satellite0 is turned on, is it True or False that instrument0 is switched on?", "initial_state": "We can find following objects in the initial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. for star1, instrument0 is calibrated, for star9, instrument0 is calibrated, instrument0 is on board satellite0, instrument0 supports image3, instrument0 supports infrared1. ::: Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Calibration of instrument1 for groundstation0 is complete, image0 is compatible with instrument1, infrared1 is compatible with instrument1, instrument1 supports image3, satellite0 carries instrument1 on board. ::: Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Calibration of instrument2 for star9 is complete, image2 is compatible with instrument2, image3 is supported by instrument2, instrument2 is calibrated for groundstation5, instrument2 is calibrated for groundstation7, satellite0 carries instrument2 on board. ::: Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Calibration of instrument3 for star6 is complete, for star8, instrument3 is calibrated, image2 is supported by instrument3, image3 is compatible with instrument3, instrument3 is calibrated for groundstation5, instrument3 supports image0, satellite1 has instrument3 on board. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. satellite0 carries instrument1 on board, satellite0 carries instrument2 on board, instrument0 is on board satellite0, satellite0 has power available, groundstation3 is where satellite0 is pointed. ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. satellite1 has instrument3 on board, satellite1 has power available and satellite1 is pointing to phenomenon10. ::: Satellite1: carries instrument3, pointing to phenomenon10, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["instrument3 that is on satellite1 is turned on to reach the current state"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply."], "action_taken": 1, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"instrument0 that is on satellite0 is turned on\". Based on the domain description, this action is executable(instrument0 is on satellite0, satellite0 has power supply)\nAfter taking the action, the current states of all objects should be: Instrument0: on satellite0, turned on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.\nThen, we compare each proposition in the question one by one.\ninstrument0 is switched on, ::: Instrument0: on satellite0, turned on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. ===> MATCH\nSince all propositions in the question match with the current state, so the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "d1d1151c-7566-4df2-a469-3e41b069002a", "domain": "satellite", "question": "In this state, if instrument0 on satellite0 is switched on, is it True or False that satellite1 has power available?", "initial_state": "Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to groundstation4, has power supply.", "action_sequence": ["instrument3 on satellite1 is switched on to reach the current state"], "state_progression": ["Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to groundstation4, doesn't have power supply."], "action_taken": 1, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"instrument0 on satellite0 is switched on\". Based on the domain description, this action is executable(instrument0 is on satellite0, satellite0 has power supply)\nAfter taking the action, the current states of all objects should be: Instrument0: on satellite0, turned on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to groundstation4, doesn't have power supply.\nThen, we compare each proposition in the question one by one.\nsatellite1 has power available ::: Satellite1: carries instrument2, carries instrument3, pointing to groundstation4, doesn't have power supply. ===> NOT MATCH\nSince there is a proposition in the question doesn't match with the current state, the question is false.\nFinal Answer: False.", "answer": 0, "label": "False"}
{"question_id": "7467e7f6-a43e-4f53-8d32-7b3576007db0", "domain": "satellite", "question": "In this state, if from star13, satellite0 turns to star16, is it True or False that satellite0 is not aimed towards star13 and satellite0 is pointing to star16?", "initial_state": "Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, has power supply, pointing to groundstation4.", "action_sequence": ["instrument3 on satellite1 is switched on", "instrument0 on satellite0 is switched on", "satellite1 turns to star6 from groundstation4", "instrument3 is calibrated on satellite1 to star6", "satellite1 turns from star6 to planet14", "instrument3 which is on satellite1 takes an image of planet14 in spectrograph1", "satellite1 turns to star10 from planet14", "image of star10 is taken with instrument3 on satellite1 in spectrograph1", "satellite1 turns to star12 from star10", "instrument3 which is on satellite1 takes an image of star12 in spectrograph1", "satellite1 turns from star12 to star0", "satellite0 turns to groundstation2 from star1", "instrument0 is calibrated on satellite0 to groundstation2", "satellite0 turns to phenomenon15 from groundstation2", "instrument0 which is on satellite0 takes an image of phenomenon15 in spectrograph0", "satellite0 turns from phenomenon15 to star11", "satellite0's instrument0 takes an image of star11 in thermograph4", "satellite0 turns to star13 from star11", "satellite0's instrument0 takes an image of star13 in spectrograph0"], "state_progression": ["Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to groundstation4.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to groundstation4.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star6.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star6.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to planet14.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to planet14.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star10.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star10.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star12.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star12.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to groundstation2. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to groundstation2. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to phenomenon15. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to phenomenon15. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star11. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0, has star11's image in thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star11. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0, has star11's image in thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star13. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.", "Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0, has star11's image in thermograph4, has star13's image in spectrograph0. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star13. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0."], "action_taken": 19, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"from star13, satellite0 turns to star16\". Based on the domain description, this action is executable(satellite0 is currently pointing to star13)\nAfter taking the action, the current states of all objects should be: Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0, has star11's image in thermograph4, has star13's image in spectrograph0. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star16. Satellite1: carries instrument2, carries instrument3, doesn't have power supply, pointing to star0.\nThen, we compare each proposition in the question one by one.\nsatellite0 is not aimed towards star13, ::: Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star16. ===> MATCH\nsatellite0 is pointing to star16. ::: Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star16. ===> MATCH\nSince all propositions in the question match with the current state, the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "61471809-ce5c-48bc-bf63-824ba636fe76", "domain": "satellite", "question": "Given the initial condition, the following actions are performed: instrument3 on satellite1 is switched on to reach the current state. In this state, if on satellite0, instrument0 is switched on, is it True or False that image of star6 does not exist in spectrograph2?", "initial_state": "Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to groundstation4, has power supply.", "action_sequence": ["instrument3 on satellite1 is switched on to reach the current state"], "state_progression": ["Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to groundstation4, doesn't have power supply."], "action_taken": 1, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"instrument0 on satellite0 is switched on\". Based on the domain description, this action is executable(instrument0 is on satellite0, satellite0 has power supply)\nAfter taking the action, the current states of all objects should be: Instrument0: on satellite0, turned on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to groundstation4, doesn't have power supply.\nThen, we compare each proposition in the question one by one.\nimage of star6 does not exist in spectrograph2 ::: Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. ===> MATCH (no image of star6 in spectrograph2 is mentioned)\nSince all propositions in the question match with the current state, so the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "cad64924-6a24-4b16-b8b8-11f5142ec36c", "domain": "satellite", "question": "In this state, if on satellite0, instrument3 is switched on, is it True or False that power is not available for satellite0?", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation2, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "action_sequence": ["instrument1 on satellite0 is switched on", "from groundstation2 satellite0 turns to groundstation0", "calibration of instrument1 which is on satellite0 to groundstation0 is complete", "from groundstation0 satellite0 turns to planet11", "instrument1 which is on satellite0 takes an image of planet11 in image5", "instrument1 which is on satellite0 takes an image of planet11 in image6", "satellite0 turns from planet11 to planet13", "instrument1 which is on satellite0 takes an image of planet13 in image5", "instrument1 which is on satellite0 takes an image of planet13 in spectrograph2", "satellite0 turns from planet13 to star10", "image of star10 is taken with instrument1 on satellite0 in image6", "instrument1 which is on satellite0 takes an image of star10 in spectrograph2", "on satellite0 instrument1 is switched off", "on satellite0 instrument2 is switched on", "from star10 satellite0 turns to star4", "instrument2 that is on satellite0 is calibrated to star4", "from star4 satellite0 turns to star16", "satellite0's instrument2 takes an image of star16 in image0", "on satellite0 instrument2 is switched off"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation2, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet11, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to planet13, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star10, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, not calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star10, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, not calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star4, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star4, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star16, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched on, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1, has star16's image in image0. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star16, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched off, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1, has star16's image in image0. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star16, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply."], "action_taken": 19, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"on satellite0, instrument3 is switched on\". Based on the domain description, this action is executable(instrument3 is on satellite0, satellite0 has power supply)\nAfter taking the action, the curremt states of all objects should be: Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched off, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2, has planet11's image in image5, has planet11's image in image6, has planet13's image in image5, has planet13's image in spectrograph2, has star10's image in image6, has star10's image in spectrograph2. Instrument2: on satellite0, switched off, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1, has star16's image in image0. Instrument3: on satellite0, turned on, is not calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star16, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.\nThen, we compare each proposition in the question one by one.\npower is not available for satellite0, ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to star16, doesn't have power supply. ===> MATCH\nSince all propositions in the question match with the curent state, so the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "97eb59cc-8785-4a78-b357-49d03f9beef1", "domain": "satellite", "question": "In this state, if from star13, satellite0 turns to star16, is it True or False that satellite0 is not aimed towards groundstation3?", "initial_state": "We can find following objects in the initial state description: instrument0, instrument1, instrument2, instrument3, satellite0, satellite1. Then, we find all descriptions related to an object, and organize then into the required format. Repeat this process until all objects' states are extracted, and each object's state contains all related properties. for star0, instrument0 is calibrated, instrument0 is calibrated for groundstation2, instrument0 is calibrated for groundstation4, instrument0 is on board satellite0, spectrograph0 is compatible with instrument0, thermograph4 is compatible with instrument0. ::: Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. calibration of instrument1 for star8 is complete, instrument1 is calibrated for groundstation2, instrument1 is calibrated for groundstation4, instrument1 is on board satellite0, instrument1 supports spectrograph1, spectrograph0 is compatible with instrument1. ::: Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. calibration of instrument2 for groundstation9 is complete, calibration of instrument2 for star7 is complete, for groundstation4, instrument2 is calibrated, infrared3 is compatible with instrument2, instrument2 is on board satellite1, spectrograph0 is compatible with instrument2, spectrograph2 is supported by instrument2. ::: Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. instrument3 is calibrated for star6, satellite1 carries instrument3 on board, spectrograph1 is supported by instrument3, spectrograph2 is supported by instrument3. ::: Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. instrument0 is on board satellite0, instrument1 is on board satellite0, satellite0 has power, satellite0 is aimed towards star1. ::: Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. groundstation4 is where satellite1 is pointed, instrument2 is on board satellite1, satellite1 carries instrument3 on board, satellite1 has power. ::: Satellite1: carries instrument2, carries instrument3, pointing to groundstation4, has power supply. After extracting all states, we organize states of all objects into one paragraph. Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to groundstation4, has power supply.", "action_sequence": ["On satellite1 instrument3 is switched on", "On satellite0 instrument0 is switched on", "From groundstation4 satellite1 turns to star6", "Instrument3 is calibrated on satellite1 to star6", "Satellite1 turns to planet14 from star6", "Satellite1's instrument3 takes an image of planet14 in spectrograph1", "Satellite1 turns from planet14 to star10", "Instrument3 which is on satellite1 takes an image of star10 in spectrograph1", "Satellite1 turns from star10 to star12", "Instrument3 which is on satellite1 takes an image of star12 in spectrograph1", "From star12 satellite1 turns to star0", "Satellite0 turns from star1 to groundstation2", "Instrument0 that is on satellite0 is calibrated to groundstation2", "Satellite0 turns from groundstation2 to phenomenon15", "An image of phenomenon15 is taken with instrument0 on satellite0 in spectrograph0", "Satellite0 turns from phenomenon15 to star11", "Satellite0's instrument0 takes an image of star11 in thermograph4", "From star11 satellite0 turns to star13", "Satellite0's instrument0 takes an image of star13 in spectrograph0"], "state_progression": ["Instrument0: on satellite0, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, has power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to groundstation4, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to groundstation4, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to star6, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to star6, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to planet14, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to planet14, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to star10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to star10, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to star12, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to star12, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star1. Satellite1: carries instrument2, carries instrument3, pointing to star0, doesn't have power supply.", "Instrument0: on satellite0, switched on, not calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to groundstation2. Satellite1: carries instrument2, carries instrument3, pointing to star0, doesn't have power supply.", "Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to groundstation2. Satellite1: carries instrument2, carries instrument3, pointing to star0, doesn't have power supply.", "Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to phenomenon15. Satellite1: carries instrument2, carries instrument3, pointing to star0, doesn't have power supply.", "Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to phenomenon15. Satellite1: carries instrument2, carries instrument3, pointing to star0, doesn't have power supply.", "Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star11. Satellite1: carries instrument2, carries instrument3, pointing to star0, doesn't have power supply.", "Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0, has star11's image in thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star11. Satellite1: carries instrument2, carries instrument3, pointing to star0, doesn't have power supply.", "Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0, has star11's image in thermograph4. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star13. Satellite1: carries instrument2, carries instrument3, pointing to star0, doesn't have power supply.", "Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0, has star11's image in thermograph4, has star13's image in spectrograph0. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star13. Satellite1: carries instrument2, carries instrument3, pointing to star0, doesn't have power supply."], "action_taken": 19, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"from star13, satellite0 turns to star16\". Based on the domain description, this action is executable(satellite0 is currently pointing to star13)\nAfter taking the action, the current states of all objects should be: Instrument0: on satellite0, switched on, is calibrated, star0 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph0, compatible with thermograph4, has phenomenon15's image in spectrograph0, has star11's image in thermograph4, has star13's image in spectrograph0. Instrument1: on satellite0, is calibrated, star8 is its calibration target, groundstation2 is its calibration target, groundstation4 is its calibration target, compatible with spectrograph1, compatible with spectrograph0. Instrument2: on satellite1, is calibrated, groundstation9 is its calibration target, star7 is its calibration target, groundstation4 is its calibration target, compatible with infrared3, compatible with spectrograph0, compatible with spectrograph2. Instrument3: on satellite1, switched on, is calibrated, star6 is its calibration target, compatible with spectrograph1, compatible with spectrograph2, has planet14's image in spectrograph1, has star10's image in spectrograph1, has star12's image in spectrograph1. Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star16. Satellite1: carries instrument2, carries instrument3, pointing to star0, doesn't have power supply.\nThen, we compare each proposition in the question one by one.\nsatellite0 is not aimed towards groundstation3, ::: Satellite0: carries instrument0, carries instrument1, doesn't have power supply, pointing to star16. ===> MATCH\nSince all propositions in the question match with the current state, so the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "3613a46d-ef87-41ad-9e57-5911c1f79cae", "domain": "satellite", "question": "In this state, if satellite0 turns from groundstation2 to groundstation0, is it True or False that satellite0 is not aimed towards groundstation2 and satellite0 is pointing to groundstation0?", "initial_state": "Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, is calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation2, has power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.", "action_sequence": ["instrument1 is switched on to reach the current state on satellite0"], "state_progression": ["Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation2, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply."], "action_taken": 1, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"satellite0 turns from groundstation2 to groundstation0\". Based on the domain description, this action is executable(satellite0 is currently pointing to groundstation2)\nAfter taking the action, the current states of all objects should be: Instrument0: on satellite0, is calibrated, groundstation7 is its calibration target, star3 is its calibration target, compatible with image6. Instrument1: on satellite0, switched on, not calibrated, groundstation6 is its calibration target, groundstation0 is its calibration target, compatible with image5, compatible with image6, compatible with spectrograph2. Instrument2: on satellite0, is calibrated, star4 is its calibration target, compatible with image0, compatible with image1. Instrument3: on satellite0, is calibrated, groundstation9 is its calibration target, compatible with image1, compatible with spectrograph4, compatible with thermograph3. Instrument4: on satellite1, is calibrated, groundstation8 is its calibration target, compatible with image1, compatible with infrared7. Satellite0: carries instrument0, carries instrument1, carries instrument2, carries instrument3, pointing to groundstation0, doesn't have power supply. Satellite1: carries instrument4, pointing to planet13, has power supply.\nThen, we compare each proposition in the question one by one.\nsatellite0 is not aimed towards groundstation2, ::: Satellite0: pointing to groundstation0. ===> MATCH\nsatellite0 is pointing to groundstation0. ::: Satellite0: pointing to groundstation0. ===> MATCH\nSince all propositions in the question match with the current state, the question is true.\nFinal Answer: True.", "answer": 1, "label": "True"}
{"question_id": "c22d8593-1b7b-4a59-a4b9-9879dee20d6f", "domain": "satellite", "question": "In this state, if instrument0 on satellite0 is switched on, is it True or False that power is available for satellite0?", "initial_state": "Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, is calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, has power supply.", "action_sequence": ["On satellite1 instrument3 is switched on to reach the current state"], "state_progression": ["Instrument0: on satellite0, is calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, has power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply."], "action_taken": 1, "state_checker_response": "In order to check whether the question matches with the current state, we need to find related objects in the current state, and then compare one by one. If any object can't match, then the question is false. If there is any action needed to take in the question, we should first take the action and return all objects states, including those states that are not affected by the action. \nBecause the question contains one action, we should first take the action and get new states of all objects. After taking the action, we should return states of all objects, including states that are not effected by the action.\nThe action is \"instrument0 on satellite0 is switched on\". Based on the domain description, this action is executable(instrument0 is on satellite0, satellite0 has power supply)\nAfter taking the action, the curremt states of all objects should be: Instrument0: on satellite0, turned on, not calibrated, star1 is its calibration target, star9 is its calibration target, compatible with image3, compatible with infrared1. Instrument1: on satellite0, is calibrated, groundstation0 is its calibration target, compatible with image0, compatible with infrared1, compatible with image3. Instrument2: on satellite0, is calibrated, star9 is its calibration target, groundstation5 is its calibration target, groundstation7 is its calibration target, compatible with image2, compatible with image3. Instrument3: on satellite1, switched on, not calibrated, star6 is its calibration target, star8 is its calibration target, groundstation5 is its calibration target, compatible with image0, compatible with image2, compatible with image3. Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. Satellite1: carries instrument3, pointing to phenomenon10, doesn't have power supply.\nThen, we compare each proposition in the question one by one.\npower is available for satellite0 ::: Satellite0: carries instrument0, carries instrument1, carries instrument2, pointing groundstation3, doesn't have power supply. ===> NOT MATCH\nSince there is a proposition in the question doesn't match with the current state, the question is false.\nFinal Answer: False.", "answer": 0, "label": "False"}
